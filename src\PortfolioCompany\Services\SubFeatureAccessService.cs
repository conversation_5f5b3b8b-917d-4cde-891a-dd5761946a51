using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Audit.Enums;
using Contract.Account;
using Contract.PortfolioCompany;
using DapperRepository;
using DapperRepository.Constants;
using Microsoft.Extensions.Logging;
using PortfolioCompany.Interfaces;
using Shared;

namespace PortfolioCompany.Services
{
    public class SubFeatureAccessService(
        ILogger<SubFeatureAccessService> logger, 
        IDapperGenericRepository dapperRepository) : ISubFeatureAccessService
    {
        private readonly ILogger<SubFeatureAccessService> _logger = logger;
        private readonly IDapperGenericRepository _dapperRepository = dapperRepository;
        /// <summary>
        /// Retrieves the cap table configuration for a given company ID.
        /// </summary>
        /// <param name="companyId">The ID of the company.</param>
        /// <returns>A task representing the asynchronous operation. The task result contains the cap table configuration.</returns>
        public async Task<List<SubFeatureAccessPermissionsModel>> GetSubFeatureAccessPermissions(int userId, int companyId, int featureId, int moduleId = 0)
        {
            if (companyId > 0 && userId > 0)
            {
                _logger.LogInformation("Get sub feature accesses for company id : " + companyId);
                var result =  await _dapperRepository.Query<SubFeatureAccessPermissionsModel>(SqlConstants.ProcGetPcSubFeaturePermissions, new { @UserId = userId, @CompanyId = companyId, @FeatureId = featureId });
                if(moduleId == (int)KpiModuleType.ProfitAndLoss)
                    return result.Where(x => x.ModuleId == (int)KpiModuleType.ProfitAndLoss || x.ModuleId == (int)KpiModuleType.BalanceSheet || x.ModuleId == (int)KpiModuleType.CashFlow).ToList();
                if (moduleId == (int)KpiModuleType.CapTable1)
                    return result.Where(x => x.ModuleId == (int)KpiModuleType.CapTable1 || x.ModuleId == (int)KpiModuleType.CapTable2 || x.ModuleId == (int)KpiModuleType.CapTable3 || x.ModuleId == (int)KpiModuleType.CapTable4 || x.ModuleId == (int)KpiModuleType.CapTable5).ToList();
                else if (moduleId > 0)
                    return result.Where(x => x.ModuleId == moduleId).ToList();
                else return result;
            }
            else
            {
                return new List<SubFeatureAccessPermissionsModel>();
            }
        }
        /// <summary>
        /// Retrieves the fund sub-feature access permissions for a specific user and fund.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="fundId">The ID of the fund.</param>
        /// <param name="kpiType">The type of KPI to check permissions for.</param>
        /// <returns>A task representing the asynchronous operation. The task result indicates whether the user has access to the specified KPI type.</returns>
        public async Task<bool> GetFundSubFeatureAccessPermissions(int userId, int fundId, string kpiType)
        {
            if (fundId <= 0 || userId <= 0)
            {
                _logger.LogWarning("Invalid user ID or fund ID provided. UserId: {UserId}, FundId: {FundId}", userId, fundId);
                return false;
            }
            _logger.LogInformation("Getting sub-feature access permissions for fund ID: {FundId} and user ID: {UserId}", fundId, userId);
            var result = await _dapperRepository.Query<SubFeatureAccessPermissionsModel>(
                SqlConstants.ProcGetFundSubFeaturePermissions, 
                new { @UserId = userId, @FundId = fundId, @FeatureId = (int)Features.Fund });      
            if (result == null || !result.Any())
            {
                _logger.LogInformation("No permissions found for fund ID: {FundId} and user ID: {UserId}", fundId, userId);
                return false;
            }
            var fundModules = result.Where(x => x.ModuleId > 0).ToList();
            
            return kpiType switch
            {
                Constants.FundFinancials => fundModules.Any(x => x.SubFeature.Contains("Fund Financials") && x.CanImport),
                Constants.FundKpis => fundModules.Any(x => x.SubFeature.Contains("Fund KPI") && x.CanImport),
                // Add more cases as needed for other KPI types
                _ => LogAndReturnFalse(kpiType)
            };
        }

        private bool LogAndReturnFalse(string kpiType)
        {
            _logger.LogWarning("Unrecognized KPI type: {KpiType}", kpiType);
            return false;
        }
        public async Task<List<SubFeatureAccessPermissionsModel>> GetAllSubFeatureAccessPermissions(int userId, int id, int featureId)
        {
            return await _dapperRepository.Query<SubFeatureAccessPermissionsModel>(SqlConstants.ProcGetSubFeaturePermissions, new { @FeatureId = featureId, @UserId = userId, @Id = id });
        }
        public async Task<List<PermissionModel>> GetAllFeatureAccessPermissions(int userId, int featureId)
        {
            return await _dapperRepository.Query<PermissionModel>(SqlConstants.ProcGetFeaturePermissions, new { @UserId = userId, @FeatureId = featureId });
        }
    }
}