﻿namespace API.Middlewares
{
    public static class RegexConstants
    {
        public const string SqlInjection1 = @"[\s]*((delete\s*table\s+)|(exec\s+)|(drop\s*table\s+)|(insert\s*into\s+)|(shutdown\s+)|(select\s+)|(update\s*table\s+)|((and|or)+[\s]+1[\s]*=[\s]*1))";
        public const string SqlInjection2 = @"(SELECT|INSERT\s+INTO|UPDATE|DELETE\s+FROM|CREATE\s+TABLE|ALTER\s+TABLE|DROP\s+TABLE)\s+\w+(\s+(SET|VALUES|ADD\s+COLUMN|.+);)\s*";
        public const string SqlInjection3 = @"(\b(AND|OR)\b\s+\b\d+\b\s*=\s*\b\d+\b)";
        public const string SqlInjection4 = @"[\s]*((delete\s+\w+)|(exec\s+)|(drop\s+(column|constraint|database|default|index|table|view)\s+\w+)|(insert\s*into\s+)|(shutdown\s+)|(select\s+)|(\bupdate\s+\w+\b)|((and|or)+[\s]+1[\s]*=[\s]*1))";
        public const string XPathAbbreviatedSyntaxInjection = @"<(/(@?[\w_?\w:\*]+(\[[^]]+\])*)?)+";
        public const string JavaExceptionInjection = @".*?Exception in thread.*";
        public const string ServerSideIncludeInjection1 = @"<!--#(include|exec|echo|config|printenv)\s+.*";
        public const string ServerSideIncludeInjection2 = @"&lt;!--#(include|exec|echo|config|printenv)\s+.*";
        public const string XPathExpandedSyntaxInjection = @"/?(ancestor(-or-self)?|descendant(-or-self)?|following(-sibling))";
        public const string JavaScriptInjection1 = @"<\s*script\b[^>]*>[^<]+<\s*/\s*script\s*>";
        public const string JavaScriptInjection2 = @"&lt;\s*script\b[^&gt;]*&gt;[^&lt;]+&lt;\s*/\s*script\s*&gt;";
        public const string XSSInjection = @"<img\s+src\s*=\s*[^>]*onerror\s*=\s*[^>]*>|<iframe\s+src\s*=\s*[^>]*>|<script\s+src\s*=\s*[^>]*>|<\s*script\b[^>]*>[^<]+<\s*/\s*script\s*>|<\s*iframe\b[^>]*>[^<]+<\s*/\s*iframe\s*>|<\s*img\b[^>]*>[^<]+<\s*/\s*img\s*>|<a\s+onclick\s*=\s*[^>]*>";
        public const string TraversalInjection = @"(\.\./|\.\.\|)";
        public const string TraversalShortInjection = @"(\./|\.\|)";
        public const string HarmfulPattern = @"(?i).*(<|%3C)\s*(img|iframe|object|embed|script).*";
        public const string HarmfulTag = @"(?i).*[\s\S](?:\b(?:x(?:link:href|html|mlns)|data:text\/html|pattern\b.*?=|formaction)|!ENTITY\s+(?:\S+|%\s+\S+)\s+(?:PUBLIC|SYSTEM)|@import)\b.*";
        public const string AttributeVector = @"(?i).*[a-z]+=(?:[^:=]+:.+;)*?[^:=]+:url\(javascript.*";
        public const string MaliciousAttributeInjectionAndMHTMLAttacks = @"(?:[\s\d\/""]+(?:on\w+|style|poster|background)=[$""\w])|(?:-type\s*:\s*multipart)";
        public const string BlindSqlInjectionTests = @"(?:(sleep\((\s*)(\d*)(\s*)\)|benchmark\((.*)\,(.*)\)))";
        public const string FileLocationAttempts = @"(?:(\%SYSTEMROOT\%))";
        public const string sqlInjectionPattern = @"(\b(AND|OR)\b\s*\d\s*=\s*\d\s*(--)?|'[^']*'\s*=\s*'[^']*'|'\s*(AND|OR)\s*'[^']*'\s*=\s*'[^']*')";
    }
    public static class ErrorMessages
    {
        public const string SqlInjection = "The request body contains SQL Injection characters";
        public const string XPathAbbreviatedSyntaxInjection = "The request body contains XPath Abbreviated Syntax Injection characters";
        public const string JavaExceptionInjection = "The request body contains Java Exception Injection characters";
        public const string ServerSideIncludeInjection = "The request body contains Server-Side Include Injection characters";
        public const string XPathExpandedSyntaxInjection = "The request body contains XPath Expanded Syntax Injection characters";
        public const string JavaScriptInjection = "The request body contains JavaScript Injection characters";
        public const string XSSInjection = "The request body contains XSS Injection characters";
        public const string TraversalInjection = "The request body contains Traversal Injection characters";
        public const string TraversalShortInjection = "The request body contains Traversal-Short Injection characters";
        public const string HarmfulPattern = "The request body contains a potentially harmful pattern";
        public const string HarmfulTag = "The request body contains a potentially harmful tag";
        public const string AttributeVector = "The request body contains attribute vector";
        public const string MaliciousAttributeInjectionAndMHTMLAttacks = "The request body contains malicious attribute injection attempts and MHTML attacks";
        public const string BlindSqlInjectionTests = "The request body contains blind SQL injection tests using sleep() or benchmark().";
        public const string FileLocationAttempts = "The request body contains an attempt to locate a file to read or write.";
    }
}
