﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Utility.Resource;

namespace API.Middlewares;
public class RequestValidatorMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestValidatorMiddleware> _logger;

    public RequestValidatorMiddleware(RequestDelegate next, ILogger<RequestValidatorMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }
    public async Task Invoke(HttpContext context)
    {
        var request = context.Request;
        var isUploadDocumentsEndpoint = request.Path.Value?.Contains("upload-documents", StringComparison.OrdinalIgnoreCase) == true
         && (request.Method.Equals("POST", StringComparison.OrdinalIgnoreCase)
             || request.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase));

        // Check if the request is valid and Check if the request method is not a standard HTTP method.
        if (!IsValid(request) || IsNonStandardHttpMethod(request))
        {
            if (isUploadDocumentsEndpoint)
            {
                _logger.LogWarning("UploadDocuments endpoint: Request validation failed. Path: {Path}, Method: {Method}, Origin: {Origin}",
                    request.Path, request.Method, request.Headers["Origin"].ToString());
            }
            await HandleExceptionAsync(context);
            return;
        }


        // Continue the pipeline by invoking the next middleware.
        await _next.Invoke(context);
    }

    private bool IsValid(HttpRequest request)
    {
        // Check if the request contains the "x-method-override" header.
        if (request.Headers.ContainsKey("x-method-override") || request.Headers.ContainsKey("X-HTTP-Method-Override"))
        {
            // Reject the request.
            return false;
        }

        // Check for command injections.
        // This code uses a regular expression to match any string that starts with one of the following characters: `;`, `DROP`, `TABLE`, `SELECT`, or `echo`. It also matches any string that contains a single quote character (`'`).
        string regex = @"(;|DROP TABLE|SELECT * FROM|echo ').+";
        if (Regex.IsMatch(request.Body.ToString(), regex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250)))
        {
            // Reject the request.
            return false;
        }

        if (request?.Query?.Any(x => Regex.IsMatch(x.Value, regex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250))) == true)
            return false;

        // The request is valid.
        return true;
    }

    private bool IsNonStandardHttpMethod(HttpRequest request)
    {
        // Get the request method.
        string requestMethod = request.Method;

        // Get the list of standard HTTP methods.
        string[] standardMethods = { "GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH" };
        if (standardMethods.Any(x => x.ToLower().Equals(requestMethod.ToLower())))
            return false;

        // The request method is not a standard HTTP method.
        return true;
    }
    private async Task HandleExceptionAsync(HttpContext context)
    {
        var response = context.Response;
        response.ContentType = "application/json;charset=utf-8";
        response.StatusCode = StatusCodes.Status500InternalServerError;
        await response.WriteAsync(JsonConvert.SerializeObject(new { code = HttpStatusCode.Forbidden, message = Messages.SomethingWentWrong }));
    }
}

