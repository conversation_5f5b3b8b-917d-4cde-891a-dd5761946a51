﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Net;
//using System.Threading.Tasks;
//using Microsoft.AspNetCore.Http;
//using Microsoft.Extensions.Configuration;
//using Newtonsoft.Json;
//using Utility.Resource;
//using Xunit;
//using API.Middlewares;
//namespace Common.Middlewares
//{
//    public class CorsMiddlewareTests
//    {
//        [Theory]
//        [InlineData("http://example.com", true)]
//        [InlineData("http://example.org", false)]
//        public async Task Invoke_ShouldSetStatusCodeToForbidden_WhenOriginIsNotAllowed(string origin, bool allowed)
//        {
//            var configuration = new ConfigurationBuilder()
//                .AddInMemoryCollection(new Dictionary<string, string>
//                {
//                    {"AllowedOrigins", "http://example.com,http://example.net"}
//                })
//                .Build();

//            var middleware = new CorsMiddleware(next: (innerHttpContext) =>
//            {
//                return Task.FromResult(0);
//            });

//            var context = new DefaultHttpContext();
//            context.Request.Headers["Origin"] = origin;
//            await middleware.Invoke(context, configuration);
//            if (allowed)
//            {
//                Assert.Equal(StatusCodes.Status200OK, context.Response.StatusCode);
//            }
//            else
//            {
//                Assert.Equal(StatusCodes.Status403Forbidden, context.Response.StatusCode);
//            }
//        }

//        [Fact]
//        public async Task Invoke_ShouldSetStatusCodeToForbidden_WhenOriginOrAllowOriginIsNull()
//        {
//            // Arrange
//            var configuration = new ConfigurationBuilder()
//                .AddInMemoryCollection(new Dictionary<string, string>
//                {
//                    {"AllowedOrigins", "http://example.com,http://example.net"}
//                })
//                .Build();

//            var middleware = new CorsMiddleware(next: (innerHttpContext) =>
//            {
//                return Task.FromResult(0);
//            });

//            var context = new DefaultHttpContext();
//            context.Request.Headers["Origin"] = "null";
//            context.Request.Headers["Allow-Origin"] = "null";

//            // Act
//            await middleware.Invoke(context, configuration);

//            // Assert
//            Assert.Equal(StatusCodes.Status403Forbidden, context.Response.StatusCode);
//        }
//    }
//}
