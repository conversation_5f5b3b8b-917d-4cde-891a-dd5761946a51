using System.Reflection.Metadata;

namespace DapperRepository.Constants
{
    public static class SqlConstants
    {
        public static readonly string QueryVDealFundCompany = "SELECT Fund, FundName, Company, CompanyName, ReportingCurrencyID, CurrencyCode FROM V_GET_VALUATION_FILTER";//BFB-1692
        public static readonly string QueryVDealFund = "SELECT Fund, FundName FROM V_GET_VALUATION_FILTER GROUP BY Fund, FundName";//BFB-1692
        public static readonly string QueryVDealFundCompanyByFund = "SELECT Company, CompanyName FROM V_GET_VALUATION_FILTER WHERE (Fund = @FUND) GROUP BY Company, CompanyName";//BFB-1692
        public static readonly string QueryByGetPcTradingRecordsQuarterlyValueDraft = "Select * from [dbo].[view_GetPcTradingRecordsQuarterlyValueDraft] where [WorkflowMappingId]=@workflowRequestId";//BFB-1140
        public static readonly string QueryByPublishTradingRecordsKPIDraft = "exec SP_CREATE_UPDATE_MASTERKPI_PUBLISH @PORTFOLIO_COMPANY_ID = @PortfolioCompanyId, @WORKFLOW_MAPPING_ID = @WorkflowMappingId, @STATUS=@status ";//BFB-1140
        public static readonly string PublishOperationalKpi = "exec  publishOperationalKpiQuarterAndValues @PortfolioCompanyID=@PortfolioCompanyID,@WorkflowMappingId=@WorkflowMappingId";
        public static readonly string PublishInvestmentKpi = "exec  publishInvestmentKpiValues @PortfolioCompanyID=@PortfolioCompanyID,@WorkflowRequestId=@WorkflowRequestId";
        public static readonly string DealListQuery = "SELECT *FROM view_DealList";
        public static readonly string DealPCListQuery = "SELECT *FROM view_DealPCList";
        public static readonly string DealFundListQuery = "SELECT *FROM view_DealFundList";
        public static readonly string DealFundPCListQuery = "SELECT *FROM view_DealPCFundList";
        public static readonly string DealFundPCQueryList = "SELECT *FROM view_DealPCFundQuery";
        public static readonly string DealFundPCQueryListWithFundId = "SELECT *FROM view_DealPCFundQuery where IsDeleted=0 and FundID=@fundID";
        public static readonly string DealTableQueryList = "SELECT *FROM DealDetails WHERE IsDeleted=0";
        public static readonly string DealTableQuery = "SELECT *FROM DealDetails";
        public static readonly string DealTableQueryListWithId = "SELECT *from DealDetails WHERE DealID=@dealId";
        public static readonly string DealFundPCQueryListWithParams = "SELECT *FROM view_DealPCFundQuery WHERE IsDeleted=0";
        public static readonly string DealListQueryWithDealId = "SELECT *FROM view_DealList WHERE DealID=@dealId AND dealIsActive = 0";
        public static readonly string DealFundPCListQueryWithId = "SELECT *FROM view_DealPCFundList WHERE PortfolioCompanyID=@companyId AND FundID=@fundId";
        public static readonly string DealFundPCListQueryWithIds = "SELECT *FROM view_DealPCFundQuery WHERE PortfolioCompanyID=@companyId AND FundID=@fundId AND DealID!=@DealId AND IsDeleted =0";
        public static readonly string GetDealsDeatils = "SELECT *FROM view_DealPCFundQuery WHERE DealID!=@DealId AND DealCustomID = @DealCustomID AND IsDeleted =0";
        public static readonly string GetDealsData = "SELECT * FROM view_DealPCFundQuery WHERE DealCustomID = @DealCustomID AND IsDeleted =0";
        public static readonly string DealPCListQueryWithFundId = "SELECT *FROM view_DealPCList WHERE FundID =@fundId";
        public static readonly string GetFundQuery = "SELECT *FROM view_GetFundList";
        public static readonly string GetPCQuery = "SELECT *FROM view_GetPCList";
        public static readonly string DealFundPCListQueryWithFundId = "SELECT *FROM view_DealPCFundList WHERE FundID=@fundID";
        public static readonly string DealFundPCListQueryAnalytics = "SELECT *FROM view_DealPCFundList";
        public static readonly string DealFundHoldingTable = "SELECT *FROM PortfolioCompanyFundHoldingDetails WHERE IsDeleted=0";
        public static readonly string DealFundHoldingTableWithDealId = "SELECT *FROM PortfolioCompanyFundHoldingDetails WHERE IsDeleted=0 AND DealID=@dealId";
        public static readonly string DealFundHoldingTableWithDealIdNotDeleted = "SELECT *FROM PortfolioCompanyFundHoldingDetails WHERE DealID=@dealId";
        public static readonly string UserFeatureQuery = "exec ProcGetUserFeatures @UserId=@UserId";
        public static readonly string DealFundHoldingQuery = "SELECT *FROM view_DealFundHolding";
        public static readonly string DealFundHoldingQueryWithDealId = "SELECT *FROM view_DealFundHolding WHERE DEALID=@dealID";
        public static readonly string DealFundHoldingQueryWithHoldingId = "SELECT *FROM PortfolioCompanyFundHoldingDetails WHERE PortfolioCompanyFundHoldingID = @holdingID";
        public static readonly string FeatureListByQueryId = "SELECT *FROM M_Groups WHERE GroupID=@groupID AND IsDeleted=0";
        public static readonly string QueryByPC = "SELECT *FROM PortfolioCompanyDetails WHERE IsDeleted=0";
        public static readonly string QueryByPCWithID = "SELECT *FROM PortfolioCompanyDetails WHERE IsDeleted=0 and PortfolioCompanyID=@companyID";
        public static readonly string QueryToGetCompaniesWithIds = "SELECT * FROM PortfolioCompanyDetails WHERE IsDeleted=0 and PortfolioCompanyID IN @Ids";
        public static readonly string QueryByPcOnlyId = "SELECT PortfolioCompanyID FROM PortfolioCompanyDetails WHERE IsDeleted=0";
        public static readonly string QueryBySubFeature = "SELECT *FROM M_SubFeature WHERE isDeleted=0";
        public static readonly string QueryBySubFeatureOnlyId = "SELECT SubFeatureID FROM M_SubFeature WHERE isDeleted = 0";
        public static readonly string QueryByMappingUserSubFeature = "SELECT *from Mapping_UserSubFeature WHERE GroupId=@groupID AND IsDeleted=0 AND FeatureMappingId=@companyID";
        public static readonly string QueryByMappingUserSubFeatureByGroupId = "SELECT *FROM Mapping_UserSubFeature WHERE IsDeleted = 0 AND GroupId=@GroupId";
        public static readonly string QueryByFirm = "SELECT FirmID,FirmName FROM FirmDetails WHERE IsDeleted=0";
        public static readonly string QueryByDesignation = "SELECT DesignationID'DesignationId',Designation FROM M_Designation WHERE IsDeleted=0";
        public static readonly string QueryByCountry = "SELECT CountryID'CountryId',CountryCode,Country FROM M_Country WHERE IsDeleted=0";
        public static readonly string QueryByRegion = "SELECT Region,RegionID'RegionId' FROM M_Region WHERE IsDeleted=0";
        public static readonly string QueryByState = "SELECT StateID'StateId',State,CountryID'CountryId' FROM M_State WHERE IsDeleted=0";
        public static readonly string QueryByCity = "SELECT StateID'StateId',CityID'CityId',City FROM M_City WHERE IsDeleted=0";
        public static readonly string QueryBySector = "SELECT Sector,SectorID FROM M_Sector WHERE IsDeleted=0";
        public static readonly string QueryBySubSector = "SELECT SubSector,SubSectorID,SectorID FROM M_SubSector WHERE IsDeleted=0";
        public static readonly string QueryByCurrency = "SELECT CurrencyID,CurrencyCode,Currency FROM M_Currency";
        public static readonly string QueryByDealExit = "SELECT DealExitMethodID,ExitMethod from M_DealExitMethod WHERE IsDeleted=0";
        public static readonly string QueryByBoardSeat = "SELECT BoardSeat,DealBoardSeatID from M_DealBoardSeat WHERE IsDeleted=0";
        public static readonly string QueryByDealInvestmentStage = "SELECT DealInvestmentStageID,InvestmentStage from M_DealInvestmentStage WHERE IsDeleted=0";
        public static readonly string QueryBySecurityType = "SELECT SecurityType,DealSecurityTypeID from M_DealSecurtyType WHERE IsDeleted=0";
        public static readonly string QueryByDealSourcing = "SELECT DealSourcingID,DealSourcing from M_DealSourcing WHERE IsDeleted=0";
        public static readonly string QueryByTransactionRole = "SELECT TransactionRole,DealTransactionRoleID from M_DealTransactionRole WHERE IsDeleted=0";
        public static readonly string QueryByValuationMethodology = "SELECT ValuationMethodology,DealValuationMethodologyID from M_DealValuationMethodology WHERE IsDeleted=0";
        public static readonly string QueryByPCFundHolding = "SELECT *FROM PortfolioCompanyFundHoldingDetails WHERE IsDeleted =0";
        public static readonly string QueryByPCFundHoldingStatus = "SELECT FundHoldingStatusID,Status FROM M_FundHoldingStatus WHERE IsDeleted=0";
        public static readonly string QueryBySubPageFields = "SELECT *FROM M_SubPageFields WHERE isDeleted=0";
        public static readonly string QueryByMasterKpisWithModuleId = "SELECT *FROM M_MasterKpis WHERE IsDeleted=0 AND ModuleID=@moduleId";
        public static readonly string QueryByInvestmentKPIS = "SELECT *FROM M_InvestmentKPI WHERE IsDeleted=0";
        public static readonly string QueryByRegionCountryMapping = "SELECT *FROM view_RegionCountryMapping";
        public static readonly string QueryByGeoLocationMapping = @"SELECT MG.RegionID,R.Region,C.Country,c.CountryID,PortfolioCompanyID,PCGeographicLocationID 'LocationId' FROM  Mapping_PCGeographicLocation MG 
                                                                        LEFT JOIN M_Region R ON MG.RegionID = R.RegionID
                                                                        LEFT JOIN M_Country C ON MG.CountryID = C.CountryID
                                                                        WHERE MG.IsDeleted = 0 ORDER BY MG.CreatedOn DESC";
        public static readonly string QueryByPCMapping = "exec spQueryByPCMapping @UserID = @userID";
        public static readonly string QueryPCMappingByUserID = "exec spQueryPCMappingByUserID @UserID = @userID";
        public static readonly string QueryDealsMappingByUserID = "exec spQueryDealsMappingByUserID @UserID = @userID";
        public static readonly string QueryByMappingInvestorGeographicLocation = "SELECT *FROM ViewMappingInvestorGeographicLocation";
        public static readonly string QueryGeoLocationByPortoflioCompanyID = "Select  RegionID, CountryID from Mapping_PCGeographicLocation where IsDeleted = 0 and PortfolioCompanyID in @pcIds Group By RegionID, CountryID";
        public static readonly string QueryMappedPCGeoLocation = "Select  RegionID, CountryID from Mapping_PCGeographicLocation where IsDeleted = 0 Group By RegionID, CountryID";
        public static readonly string QueryDealPCDetailsByFundIds = @"Select DealID,DealCustomID,deal.PortfolioCompanyID,pc.CompanyName,deal.FundID,fund.FundName,deal.EncryptedDealID,deal.IsDeleted,pc.EncryptedPortfolioCompanyID,fund.EncryptedFundID,deal.InvestmentDate
                                                                     from DealDetails deal LEFT JOIN PortfolioCompanyDetails pc on deal.PortfolioCompanyID = pc.PortfolioCompanyID
                                                                     LEFT JOIN FundDetails fund on deal.FundID = fund.FundID where deal.IsDeleted = 0 and fund.IsDeleted = 0 and fund.FundID in @FundIds";

        public static readonly string QueryByMappingUserSubFeatureList = "SELECT *FROM Mapping_UserSubFeature";
        public static readonly string QueryByCompanyList = "SELECT PortfolioCompanyID,CompanyName,ReportingCurrencyID,SectorID FROM PortfolioCompanyDetails WHERE IsDeleted=0";
        public static readonly string QueryByFundList = "SELECT FundID,FundName,EncryptedFundId,SectorID,VintageYear,StrategyID FROM FundDetails WHERE IsDeleted=0";
        public static readonly string QueryByDealList = "SELECT *FROM view_DealPCFundListQuery";
        public static readonly string QueryByInvestorFundList = "SELECT *FROM view_InvestorFundList";
        public static readonly string QueryByInvestorsSelectList = "SELECT InvestorId,InvestorName FROM InvestorType WHERE IsDeleted=0 order by investorname asc";
        public static readonly string QueryByInvestorsFundList = "SELECT *FROM view_InvestorFundListByFundId WHERE FundId=@fundId";
        public static readonly string QueryByFundLevelExcel = "exec spFundLevelExcelDownload @FundDetailsId=@fundId,@FYear=@fYear,@TYear=@tYear,@FQuarter=@fQuarter,@TQuarter=@tQuarter";
        public static readonly string QueryByFundOperationalExcel = "exec spFundOperationalExcelDownload @FundId=@fundId,@FYear=@fYear,@TYear=@tYear,@FQuarter=@fQuarter,@TQuarter=@tQuarter";
        public static readonly string QueryByPCompanyLevelExcelDownload = "exec spPCompanyLevelExcelDownload @FundId=@fundId,@FYear=@fYear,@TYear=@tYear,@FQuarter=@fQuarter,@TQuarter=@tQuarter";
        public static readonly string QueryByInvestorsByFundId = "SELECT* FROM view_InvestorFundStaticListByFundId WHERE FundId=@fundId";
        public static readonly string QueryByInvestorFundListByInvestorId = "SELECT *FROM [dbo].[view_InvestorFundList] WHERE InvestorId=@investorId";
        public static readonly string QueryByTradingRecordHeaderKPI = "Select Distinct ISNULL(B.MasterKpiID,0) MasterKpiId  FROM  Mapping_Kpis A LEFT JOIN M_MasterKpis B On A.KpiID=B.MasterKpiID and A.ModuleID= 1 and A.IsDeleted= 0 and B.IsHeader= 1 and PortfolioCompanyID  in (Select PortfolioCompanyID FROM DealDetails WHERE FundID=@fundId)";
        public static readonly string QueryByInvestorDashBoard = "exec SPInvestorDashboard @InvestorId=@investorId";
        public static readonly string QueryByGetFundsByInvestor = "exec spGetFundsByInvestor @InvestorId=@investorId";
        public static readonly string QueryByGetTrackRecordByInvestor = "exec spGetFundTrackRecordByInvestor @FundId = @fundId,@Quarter =@quarter ,@YEAR =@year,@FundInvestorId =@fundInvestorId";
        public static readonly string QueryByGetAllFunds = "exec GetFundList @UserId=@UserId";
        public static readonly string QueryByGetAllFundsByUserID = "exec SPGetFundListByUserID @UserId=@UserId";
        public static readonly string QueryByTop10PortfolioCompaniesBYInvestorTotalValue = "exec spTop10PortfolioCompaniesBYInvestorTotalValue @InvestorId=@investorId";
        public static readonly string QueryByInvestorWiseCompanyDealTrackRecord = "exec spInvestorWiseCompanyDealTrackRecord @InvestorId=@investorId,@FundId = @fundId,@CYear=@Year,@CQuarter=@Quarter,@SearchKeyWord= @SearchKeyWord";
        public static readonly string QueryByGetPCById = "SELECT *FROM [dbo].[view_GetPCById] WHERE PortfolioCompanyId = @Id";
        public static readonly string QueryByGetFundCurrencyByPC = "SELECT *FROM [dbo].[view_FundCurrencyByPC] WHERE PortfolioCompanyID =@Id";
        public static readonly string QueryByGetLocationByPC = "SELECT *FROM [dbo].[view_GetMappedGeographicLocationByPC] WHERE PortfolioCompanyId =@Id";
        public static readonly string QueryByGetEmployeesByPC = "SELECT *FROM [dbo].[view_GetMappedEmployeesByPC] WHERE PortfolioCompanyId =@Id";
        public static readonly string QueryByCurrencySectorByPC = "SELECT *FROM [dbo].[view_GetCurrencySectorByPC] WHERE PortfolioCompanyId =@Id";
        public static readonly string QueryByGetCommentaryByPC = "SELECT FootNoteInvestmentKPI'FootNoteInvestmentKPISection',FootNoteTradingRecord'FootNoteTradingRecordSection',CommentaryID,EncryptedCommentaryID,AssessmentSection,ExitPlansSection,ImpactSection,Quarter,Year FROM CommentaryDetails WHERE QUARTER = @quarter AND  YEAR=@year AND PortfolioCompanyID =@Id AND IsDeleted =0";
        public static readonly string QueryByGetProfitabilityByPC = "SELECT *FROM PortfolioCompanyProfitabilityDetails WHERE IsDeleted =0 AND PortfolioCompanyID = @Id";
        public static readonly string QueryByFundCurrency = "Select A.Currency,A.CurrencyCode FROm M_Currency  A LEFT JOIN FundDetails B on A.CurrencyID=B.CurrencyID  WHERE B.FundID=@fundId";
        public static readonly string QueryByInvestorWiseAllFundsTotalComitment = "Select SUM(NULLIF(CONVERT(decimal(38,8),ISNULL(NULLIF(fi.CommitmentAfterAstreaTransfer, '0'), '0')),0)) TotalComitment FROM FundInvestors FI WHERE FI.InvestorId In(Select InvestorId FROM InvestorType WHERE IsActive=1) AND FI.IsActive=1 AND FI.InvestorId=@investorId";
        public static readonly string QueryByGetInvestorsAll = "SELECT A.*,ISNULL(B.TotalCommitment,'NA') TotalCommitment FROM(Select InvestorName,InvestorId,Website,ISNULL(InvestorType,'NA') InvestorType,EncryptedInvestorId,BusinessDescription FROM InvestorType It LEFT JOIN MInvestorType MIt on It.InvestorTypeId=MIt.InvestorTypeId WHERE It.IsActive=1 AND It.IsDeleted=0 ) A LEFT JOIN (Select MAX(FI.InvestorId) InvestorId,CAST(SUM(NULLIF(CONVERT(decimal(38,8),ISNULL(NULLIF(fi.CommitmentAfterAstreaTransfer, '0'), '0')),0)) AS Varchar) TotalCommitment FROM FundInvestors FI WHERE FI.InvestorId In(Select InvestorId FROM InvestorType WHERE IsActive=1) AND FI.IsActive=1 Group By FI.InvestorId) B On A.InvestorId=B.InvestorId Order By InvestorName";
        public static readonly string QueryByGetCompanyPerformanceDatar = "exec spGetCompanyPerformanceData @FundId = @fundId,@Quarter =@quarter ,@YEAR =@year,@FundInvestorId =@fundInvestorId,@dealId=@dealId";
        public static readonly string QueryByGetMasterCompaniesBYInvestor = "exec spMasterCompaniesBYInvestor @InvestorId=@investorId";
        public static readonly string QueryByInvestorWiseFundName = "SELECT * FROM FUNDINVESTOR WHERE InvestorId=@investorId";
        public static readonly string QueryByFundWiseInvestor = "SELECT * FROM FUNDINVESTOR Order by InvestorId asc";
        public static readonly string QueryByGetMasterCompaniesBYInvestorId = "exec spMasterCompaniesByInvestorId @InvestorId=@investorId";
        public static readonly string QueryByValuationTableDropDownBYInvestorId = "Select  0 FundId,'All Funds' FundName UNION ALL Select Distinct FundId,FundName FROM FundInvestor WHERE InvestorId=@investorId";
        public static readonly string QueryByValuationTableLatestQuarterYear = "SELECT Substring(Quarter, 6, 7)+' '+Substring(Quarter, 1, 4) AS Quarter FROM (SELECT Top 1 [Period] as Quarter FROM (SELECT CAST(Year as varchar) +' '+Quarter [Period] FROM InvestorValuation WHERE IsActive=1 AND InvestorId=@investorId ) C ORDER BY CONVERT(int, SUBSTRING([Period], 1, 4)) * 10 + CONVERT(int, SUBSTRING([Period], 7, 1)) DESC)QY";
        public static readonly string QueryByInvestorWiseValuationTable = "exec spInvestorWiseValuationTable @InvestorId=@investorId,@FundId = @fundId,@Period=@Quarter,@SearchKeyWord= @SearchKeyWord";
        public static readonly string QueryByDeleteKPI = "exec spDeleteKpi @KpiType = @kpiType,@ModuleId =@moduleId ,@KPIId =@kpiId";

        public static readonly string QueryByGetCompanyPerformanceLatestQuarter = "SELECT Distinct Year Label,Quarter Value,Quarter+' '+ CAST(Year as varchar) as KeyValue FROM (SELECT  Max(dbo.Getlastdateofquarter(CONVERT(INT, Replace(C.Quarter,'Q', '')),C.Year)) as QuarterDate from FundInvestors A INNER JOIN  DealDetails B On A.FundId=B.FundID INNER JOIN PortfolioCompanyFundHoldingDetails C On C.DealID=B.DealID WHERE A.IsActive=1 AND A.InvestorId in(@investorId))A INNER JOIN ( SELECT DISTINCT  C.Year,C.Quarter, Convert(Date,DATEADD(DAY, -1, DATEADD(QUARTER,  CONVERT(INT, Replace(C.Quarter,'Q', '')), Convert(nvarchar(4),C.Year) +'-01-01'))) as QuarterDate from FundInvestors A INNER JOIN  DealDetails B On A.FundId=B.FundID INNER JOIN PortfolioCompanyFundHoldingDetails C On C.DealID=B.DealID WHERE A.IsActive=1 AND A.InvestorId in(@investorId)) B ON A.QuarterDate=B.QuarterDate";
        public static readonly string QueryByInvestorWiseCompanyPerformanceLatest = "exec spInvestorWiseCompanyPerformance @InvestorId=@investorId,@MasterCompanyName = @masterCompanyName,@QuarterYear=@Quarter,@SearchKeyWord= @SearchKeyWord";
        public static readonly string QueryByMainDashboardLatestQuarterTotalFundsCompanies = "SELECT * FROM DashBoardLatestQuarterYearByTotalFundsCompanies";
        public static readonly string QueryByMasterCompaniesCount = "Select COUNT(DISTINCT ISNULL(MasterCompanyName,CompanyName)) MaxValuesCount FROM PortfolioCompanyDetails A INNER JOIN DealDetails B On A.PortfolioCompanyID=B.PortfolioCompanyID INNER JOIN PortfolioCompanyFundHoldingDetails C on C.DealID=B.DealID WHERE C.IsDeleted=0";
        public static readonly string QueryBySPCreateDuplicateKPI = "ProcCreateDuplicateKPI";

        public static readonly string QueryByDealTrackRecordLatestQuarterYear = "SELECT Distinct Year Label,Quarter Value,Quarter+' '+ CAST(Year as varchar) as KeyValue FROM (SELECT  Max(dbo.Getlastdateofquarter(CONVERT(INT, Replace(C.Quarter,'Q', '')),C.Year)) as QuarterDate from  DealDetails B INNER JOIN PortfolioCompanyFundHoldingDetails C On C.DealID=B.DealID )A INNER JOIN ( SELECT DISTINCT  C.Year,C.Quarter, Convert(Date,DATEADD(DAY, -1, DATEADD(QUARTER,  CONVERT(INT, Replace(C.Quarter,'Q', '')),Convert(nvarchar(4),C.Year) +'-01-01'))) as QuarterDate from  DealDetails B INNER JOIN PortfolioCompanyFundHoldingDetails C On C.DealID=B.DealID ) B ON A.QuarterDate=B.QuarterDate";
        public static readonly string QueryByKpisByType = "exec GetKPIsByKPIType @KpiType = @KpiType, @PortfolioCompanyId = @PortfolioCompanyId,@ModuleId = @ModuleId";
        public static readonly string QueryByMasterCompaniesCountByQuarterYear = "Select COUNT(DISTINCT ISNULL(MasterCompanyName,CompanyName)) KeyValue FROM PortfolioCompanyDetails A INNER JOIN DealDetails B On A.PortfolioCompanyID=B.PortfolioCompanyID INNER JOIN PortfolioCompanyFundHoldingDetails C on C.DealID=B.DealID WHERE C.IsDeleted=0 AND ( @QuarterYear IS NULL OR @QuarterYear='' or C.Quarter+' '+ CAST(C.Year as varchar)= @QuarterYear)";
        public static readonly string QueryByCompanyValuationCountByQuarterYear = "SELECT Count(PC.CompanyName) KeyValue FROM  PortfolioCompanyFundHoldingDetails C INNER JOIN DealDetails B ON C.DealID = B.DealID INNER JOIN PortfolioCompanyDetails PC on PC.PortfolioCompanyID=B.PortfolioCompanyID WHERE C.IsDeleted = 0 AND ( @QuarterYear IS NULL OR @QuarterYear='' or C.Quarter+' '+ CAST(C.Year as varchar)= @QuarterYear)";
        public static readonly string QueryByMasterCompaniesCountByTopHoldingInvestor = "Select count(distinct isnull(MasterCompanyName,CompanyName)) KeyValue FROM PortfolioCompanyDetails A INNER JOIN DealDetails B On A.PortfolioCompanyID=B.PortfolioCompanyID INNER JOIN PortfolioCompanyFundHoldingDetails C on C.DealID=B.DealID INNER JOIN FundInvestors FI On FI.FundId=B.FundID WHERE FI.IsActive=1 AND C.IsDeleted=0 AND FI.InvestorId=@InvestorId AND ( @QuarterYear IS NULL OR @QuarterYear='' or C.Quarter+' '+ CAST(C.Year as varchar)= @QuarterYear)";
        public static readonly string QueryByUpdateFormulaById = "exec UpdateKPIsById @KpiType = @KpiType,@KpiId = @KpiId,@Formula = @Formula ,@FormulaKpiId = @FormulaKpiId,@MappingId = @MappingId ,@ModifiedBy = @ModifiedBy";
        public static readonly string QueryByDealNewTransaction = "exec spDealNewTransaction @toDate=@toDate,@fromDate=@fromDate, @quarter = @quarter, @year = @year";
        #region tableQuery
        public static readonly string QueryByMasterKpis = "SELECT * FROM M_MasterKpis WHERE IsDeleted = 0 AND ModuleID=@moduleId";
        public static readonly string QueryByValueTypes = "SELECT * FROM M_ValueTypes WHERE IsDeleted = 0";
        public static readonly string QueryByMasterKpiValues = "SELECT * FROM PCMasterKpiValues WHERE IsDeleted = 0 AND PortfolioCompanyID = @companyId AND ModuleID = @moduleId";
        public static readonly string QueryByMappingKPIs = "SELECT * FROM Mapping_Kpis WHERE IsDeleted = 0 AND PortfolioCompanyID = @companyId AND ModuleID = @moduleId";
        public static readonly string QueryByMappingKPIsLast = "SELECT TOP 1 * FROM Mapping_Kpis WHERE IsDeleted =0 AND PortfolioCompanyID= @companyId and ModuleID= @moduleId order by Mapping_KpisID desc";
        public static readonly string QueryByAllFundsByInvestor = "select FD.FundName,FundSize,FT.CommitmentAfterAstreaTransfer,InvestorName from InvestorType IT LEFT JOIN FundInvestors FT ON IT.InvestorId = FT.InvestorId LEFT JOIN FundDetails FD ON FT.FundId = FD.FundID WHERE FD.IsDeleted = 0 and FT.IsDeleted= 0 AND IT.IsDeleted= 0";
        public static readonly string QueryByAllDeals = "SELECT Quarter,YEAR,DealCustomID,FundName,CompanyName,MasterCompanyName,InvestmentCost,RealizedValue,UnrealizedValue,TotalValue FROM DealDetails D LEFT JOIN FundDetails F ON D.FundID =F.FundID LEFT JOIN PortfolioCompanyDetails P ON D.PortfolioCompanyID = P.PortfolioCompanyID LEFT JOIN PortfolioCompanyFundHoldingDetails FC ON D.DealID = FC.DealID WHERE FC.IsDeleted= 0 AND P.IsDeleted = 0 AND F.IsDeleted = 0 AND D.IsDeleted= 0";
        public static readonly string QueryByCompanyKPI = "SELECT *FROM [dbo].[view_GetCompanyKPI] WHERE PortfolioCompanyId=@companyId";
        public static readonly string QueryByProfitLossKPI = "SELECT ProfitAndLossLineItem'KPI',ProfitAndLossLineItemID'KPIId',KPIInfo FROM M_ProfitAndLoss_LineItems WHERE IsDeleted=0";
        public static readonly string QueryByBalanceSheetKPI = "SELECT BalanceSheetLineItemID'KPIId',BalanceSheetLineItem'KPI',KPIInfo FROM M_BalanceSheet_LineItems WHERE IsDeleted=0";
        public static readonly string QueryByCashflowKPI = "SELECT CashFlowLineItem'KPI',CashFlowLineItemID'KPIId',KPIInfo FROM M_CashFlow_LineItems WHERE IsDeleted=0";
        public static readonly string QueryByInvestmentKPI = "SELECT InvestmentKPIId'KPIId',KPI,KpiInfo'KPIInfo' FROM M_InvestmentKPI WHERE IsDeleted=0";
        public static readonly string QueryByCompanyKPIList = "SELECT CompanyKPIID'KPIId',KPI,KpiInfo'KPIInfo' FROM M_CompanyKPI WHERE IsDeleted=0";
        public static readonly string QueryByOperationalKPIList = "SELECT SectorwiseOperationalKPIID 'KPIId',KPI,KPIInfo'KPIInfo' FROM M_SectorwiseOperationalKPI WHERE IsDeleted=0";
        public static readonly string QueryByCopyToCompanies = "exec [dbo].[ProcCopyKPIToCompanies] @KpiType=@kpiType,@CompanyId=@companyId,@UserId=@userId,@CompanyIds=@companyIds,@ModuleId = @moduleId";

        public static readonly string QueryByMappedOperationalKpis = "SELECT * FROM Mapping_PortfolioOperationalKPI WHERE IsDeleted = 0 AND PortfolioCompanyID = @companyId AND DisplayOrder is not null";
        public static readonly string QueryByGetInvestmentKpis = "SELECT * FROM M_InvestmentKPI WHERE IsDeleted = 0";
        public static readonly string QueryByGetOpKpis = "SELECT * FROM M_SectorwiseOperationalKPI WHERE IsDeleted = 0";
        #endregion
        public static readonly string QueryByMainDashboard = "exec spMainDashboard";
        public static readonly string QueryByDiscardDraft = "exec spRejectPCWorkflowDraft @workflowRequestId = @workflowRequestId, @userID = @userID ";
        public static readonly string QueryByInternalReportConfig = "SELECT TemplateId,TemplateName,IsDefault,IsActive,CurrencyUnit FROM InternalReportConfiguration WHERE IsDeleted =0 ORDER BY IsDefault DESC";
        public static readonly string QueryAdhocLatestEntites = @"Select * FROM dbo.AdhocLatestDetails_Monthly_Quarterly_Yearly(@CompanyId)";
        public static readonly string QueryByInternalReportFund = "SELECT FundID,FundName FROM FundDetails WHERE IsDeleted =0";
        public static readonly string QueryByInternalReportModules = "SELECT ModuleID,AliasName AS Name FROM M_KpiModules WHERE IsDeleted =0 AND ModuleID <=10";
        public static readonly string QueryByInternalReportValueType = "SELECT ValueTypeID,HeaderValue FROM M_ValueTypes WHERE IsDeleted=0 and HeaderValue!='IC' AND HeaderValue NOT LIKE '%LTM%' AND HeaderValue NOT LIKE '%YTD%' AND HeaderValue NOT LIKE '%Var%'";
        public static readonly string QueryByInternalReportCalculation = "SELECT CalculationId,CalculationType FROM MCalculation WHERE IsDeleted=0";
        public static readonly string QueryByInternalReportPeriodType = "SELECT PeriodId,PeriodType FROM MPeriodTypes WHERE IsDeleted=0";
        public static readonly string QueryByInternalReportExcelTemplate = "SELECT TemplateId,Name FROM MInternalReportExcelTemplate WHERE IsDeleted=0";
        public static readonly string QueryByInternalReportCompany = "SELECT DISTINCT P.PortfolioCompanyID,FundID FROM PortfolioCompanyDetails P LEFT JOIN DealDetails D ON P.PortfolioCompanyID = D.PortfolioCompanyID WHERE P.IsDeleted=0 and FundID IS NOT NULL";
        public static readonly string QueryByInternalReportKpis = "exec ProcGetInternalReportKpis @CompanyId = @companyId,@TemplateId = @templateId";
        public static readonly string QueryBySetActiveTemplate = "exec ProcSetActiveTemplate @TemplatedId = @templatedId";
        public static readonly string QueryAdhocMappedPeriodTypes = "Select DISTINCT PeriodType Label,PeriodType Value FROM UnstructuredHistory WHERE CompanyId=@CompanyId and IsActive=1 and IsDeleted=0 AND PeriodType<>''";
        public static readonly string QueryByRegionCountryByFundIds = "exec spRegionCountryByFundIds @FundIds = @FundIds";
        public static readonly string QueryByDeleteDealTrackRecord = "DELETE FROM PortfolioCompanyFundHoldingDetails WHERE PortfolioCompanyFundHoldingID=@fundholdingId";
        public static readonly string QueryBySpWorkflowPCDraftList = "exec spWorkflowPCDraftList @userID = @userID,  @PageNumber = @PageNumber, @RecordsPerPage = @RecordsPerPage, @searchText = @searchText";
        public static readonly string QueryBySpWorkflowMappingSubFeatureList = "exec spWorkflowMappingSubFeatureList @WorkflowRequestId = @WorkflowRequestId ";
        public static readonly string QueryByDataAuditLogTrueorFalse = "Select CASE WHEN COUNT(AuditId) <>0 THEN 'true' ELSE 'false' END IsExits FROM DataAuditLog WHERE   PortfolioCompanyID=@PortfolioCompanyID AND AttributeName=@AttributeName AND MonthAndYear=@MonthAndYear and FieldName=@FieldName AND AttributeID=@AttributeID";
        public static readonly string QueryBySpOperationalKpiValuesList = @"exec spOperationalKpiValues @CompanyId = @CompanyId";
        public static readonly string QueryByInternalReportCompanyDeleteQuery = "DELETE FROM MappingInternalReportConfiguration WHERE PortfolioCompanyId = @companyId AND TemplateId= @templateId";
        public static readonly string QueryByInternalReportSectionDeleteQuery = "DELETE FROM InternalReportSectionPreference WHERE  TemplateId= @templateId";
        public static readonly string QueryByInternalReportFundDeleteQuery = "DELETE FROM InternalReportFundPreference WHERE  TemplateId= @templateId";
        public static readonly string QueryByInternalReportDataTypeDeleteQuery = "DELETE FROM InternalReportValueTypePreference WHERE  TemplateId= @templateId";
        public static readonly string QueryByInternalReportCalculationsDeleteQuery = "DELETE FROM InternalReportCalculationPreference WHERE  TemplateId= @templateId";
        public static readonly string QueryByInternalReportPeriodTypeDeleteQuery = "DELETE FROM InternalReportPeriodTypePreference WHERE  TemplateId= @templateId";
        public static readonly string QueryByInternalReportCompanyQuery = "SELECT CompanyName,PortfolioCompanyID ,(CASE WHEN EXISTS(SELECT TemplateId FROM MappingInternalReportConfiguration WHERE PortfolioCompanyId = P.PortfolioCompanyID AND TemplateId =@templateId) THEN 1 ELSE 0 END) AS Tick from PortfolioCompanyDetails P WHERE IsDeleted =0 ORDER BY CompanyName ASC";
        public static readonly string QueryByInternalReportGetSectionQuery = "SELECT M.ModuleID,AliasName'Name' FROM InternalReportSectionPreference P INNER JOIN M_KpiModules M ON P.ModuleId = M.ModuleID WHERE TemplateId=@templateId";
        public static readonly string QueryByInternalReportGetValueTypeQuery = "SELECT M.ValueTypeID,HeaderValue FROM InternalReportValueTypePreference P INNER JOIN M_ValueTypes M ON P.ValueTypeId = M.ValueTypeID WHERE TemplateId=@templateId";
        public static readonly string QueryByInternalReportGetCalculationQuery = "SELECT M.CalculationId,CalculationType FROM InternalReportCalculationPreference P INNER JOIN MCalculation M ON P.CalculationId = M.CalculationId WHERE TemplateId=@templateId";
        public static readonly string QueryByInternalReportGetPeriodTypeQuery = "SELECT M.PeriodId,PeriodType FROM InternalReportPeriodTypePreference P INNER JOIN MPeriodTypes M ON P.PeriodTypeId = M.PeriodId WHERE TemplateId=@templateId";
        public static readonly string QueryByInternalReportGetExcelTemplateQuery = "SELECT M.TemplateId,Name FROM InternalReportExcelTemplatePreference P INNER JOIN MInternalReportExcelTemplate M ON P.ExcelTemplate = M.TemplateId WHERE P.TemplateId=@templateId";
        public static readonly string QueryByCopyExistingTemplate = "exec ProcCopyKPIToTemplate @OldTemplateId = @oldTemplateId,@NewTemplateId=@newTemplateId";

        public static readonly string QueryUserSubFeaturePermisson = @"exec spGetUserSubFeaturePermission @userId = @userId,  @featureID =  @featureID, @EntityId = @EntityId, @featureMappingID = @featureMappingID";
        public static readonly string QueryByDeleteTemplate = "exec ProcDeleteTemplate @TemplateId = @templateId";
        public static readonly string QueryByGetInternalReportCompany = "exec ProcGetIRCompanies @templateIds = @templateIds";
        public static readonly string QueryByCopyToTemplate = "exec ProcCopyKPIToCompanyTemplate @TemplateId = @templateId,@FromCompanyId=@fromCompanyId,@ToCompanyIds=@toCompanyIds";
        public static readonly string QueryGetPeriod = "exec ProcGetPeriod @FromQuarter = @fromQuarter,  @ToQuarter  = @toQuarter, @FromMonth = @fromMonth, @ToMonth = @toMonth,  @FromYear = @fromYear, @ToYear = @toYear";
        public static readonly string QueryByGetCompany = "SELECT PortfolioCompanyID, CompanyName,C.CurrencyCode,FinancialYearEnd FROM PortfolioCompanyDetails P LEFT JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID WHERE PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@companyId,',')) ORDER BY CompanyName ASC";
        public static readonly string QueryByCompanyName = "SELECT  CompanyName,C.CurrencyCode FROM PortfolioCompanyDetails P LEFT JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID WHERE PortfolioCompanyID = @companyId";
        public static readonly string QueryByGetFinancialYearEnd = "SELECT YearEnd, Quarter1, Quarter2, Quarter3, Quarter4 FROM MFinancialPeriod WHERE IsDeleted = 0";
        public static readonly string QueryByGetFundOpKpis = "SELECT SectorwiseOperationalKPIID,KPIInfo, IsHeader,IsBoldKPI FROM M_SectorwiseOperationalKPI WHERE IsDeleted = 0";
        public static readonly string QueryUserDetails = "Select UserID,FirstName,LastName,EmailID FROM UserDetails WHERE UserID=@UserId";
        public static readonly string QueryUser = "SELECT * FROM UserDetails WHERE LOWER(EmailID) = LOWER(@EmailId) AND IsDeleted = 0 AND IsActive = 1";
        public static readonly string QueryByDownloadKpis = "exec ProcGetCalculationKpis @CompanyId = @companyId,@TemplateId = @templateId";
        public static readonly string QueryByDownloadKpisMonthlyExcel = "exec ProcMonthlyExcelGetCalculationKpis @CompanyId = @companyId,@TemplateId = @templateId";
        public static readonly string QueryByGetKpisForValuationLtm = "exec ProcValuationLtmCalculationKpis @CompanyId = @companyId,@ModuleId = @moduleId,@Kpi=@kpi";
        public static readonly string QueryByGetInternalReportData = "exec ProcGetInternalReportData @CompanyId = @companyId,@TemplateId = @templateId,@FromYear = @fromYear,@ToYear = @toYear";
        public static readonly string QueryByGetInternalReportDataMonthlyExcel = "exec ProcGetInternalReportDataMonthlyExcel @CompanyId = @companyId,@TemplateId = @templateId,@FromYear = @fromYear,@ToYear = @toYear";
        public static readonly string QueryByGetValuationLtmData = "exec ProcGetValuationLtmData @CompanyId = @companyId,@ModuleId = @moduleId,@FromYear = @fromYear,@ToYear = @toYear,@Kpi = @kpi,@ValueType = @valueType";
        public static readonly string QueryByGetProfitAndLoss = "SELECT * FROM M_ProfitAndLoss_LineItems WHERE IsDeleted =0";
        public static readonly string QueryByGetCashflow = "SELECT * FROM M_CashFlow_LineItems WHERE IsDeleted =0";
        public static readonly string QueryByGetBalanceSheet = "SELECT * FROM M_BalanceSheet_LineItems WHERE IsDeleted =0";
        public static readonly string QueryByGetMappingProfitAndLoss = "SELECT* FROM Mapping_CompanyProfitAndLossLineItems WHERE PortfolioCompanyID =@companyId and IsDeleted = 0";
        public static readonly string QueryByGetMappingBalanceSheet = "SELECT* FROM Mapping_CompanyBalanceSheetLineItems WHERE PortfolioCompanyID =@companyId and IsDeleted = 0";
        public static readonly string QueryByGetMappingCashflow = "SELECT* FROM Mapping_CompanyCashFlowLineItems WHERE PortfolioCompanyID =@companyId and IsDeleted = 0";
        public static readonly string QueryByGetMappingInvestmentKpi = "SELECT* FROM Mapping_PortfolioInvestmentKPI WHERE PortfolioCompanyID =@companyId and IsDeleted = 0";
        public static readonly string QueryByGetInvestmentKpi = "SELECT* FROM M_InvestmentKPI WHERE  IsDeleted = 0";
        public static readonly string QueryByGetMappingOperationalKpi = "SELECT* FROM Mapping_PortfolioOperationalKPI WHERE PortfolioCompanyID =@companyId and IsDeleted = 0";
        public static readonly string QueryByGetOperationalKpi = "SELECT* FROM M_SectorwiseOperationalKPI WHERE  IsDeleted = 0";
        public static readonly string QueryByGetMappingCompanyKpi = "SELECT* FROM Mapping_PortfolioCompanyKPI WHERE PortfolioCompanyID =@companyId and IsDeleted = 0";
        public static readonly string QueryByGetCompanyKpi = "SELECT CompanyKPIID'CompanywiseKPIID',KPI,KpiInfo,CreatedBy,CreatedBy,IsDeleted,Formula,FormulaKPIId,IsBoldKPI,IsHeader FROM M_CompanyKPI WHERE  IsDeleted = 0";
        public static readonly string QueryByOperationalLast = "SELECT TOP 1 * FROM Mapping_PortfolioOperationalKPI WHERE IsDeleted = 0 AND PortfolioCompanyID = @companyId order by MappingPortfolioOperationalKPIId desc";
        public static readonly string QueryByInvestmentKpiLast = "SELECT TOP 1 * FROM Mapping_PortfolioInvestmentKPI WHERE IsDeleted = 0 AND PortfolioCompanyID = @companyId order by MappingPortfolioInvestmentKPIId desc";
        public static readonly string QueryByCompanyKpiLast = "SELECT TOP 1 * FROM Mapping_PortfolioCompanyKPI WHERE IsDeleted = 0 AND PortfolioCompanyID = @companyId order by MappingPortfolioCompanyKPIId desc";
        public static readonly string QueryByProfitLossLast = "SELECT TOP 1 * FROM Mapping_CompanyProfitAndLossLineItems WHERE IsDeleted = 0 AND PortfolioCompanyID = @companyId order by CompanyProfitAndLossLineItemMappingID desc";
        public static readonly string QueryByCashflowLast = "SELECT TOP 1 * FROM Mapping_CompanyCashFlowLineItems WHERE IsDeleted = 0 AND PortfolioCompanyID = @companyId order by CompanyCashFlowLineItemMappingID desc";
        public static readonly string QueryByBalanceSheetLast = "SELECT TOP 1 * FROM Mapping_CompanyBalanceSheetLineItems WHERE IsDeleted = 0 AND PortfolioCompanyID = @companyId order by CompanyBalanceSheetLineItemMappingID desc";

        public static readonly string QueryByMappingOperationalKpiFirst = "SELECT TOP 1 *FROM Mapping_PortfolioOperationalKPI WHERE MappingPortfolioOperationalKPIId = @mappingId";
        public static readonly string QueryByMappingCompanyKpiFirst = "SELECT TOP 1 *FROM Mapping_PortfolioCompanyKPI WHERE MappingPortfolioCompanyKPIId = @mappingId";
        public static readonly string QueryByMappingInvestmentFirst = "SELECT TOP 1 *FROM Mapping_PortfolioInvestmentKPI WHERE MappingPortfolioInvestmentKPIId = @mappingId";
        public static readonly string QueryByMappingProfitLossFirst = "SELECT TOP 1 *FROM Mapping_CompanyProfitAndLossLineItems WHERE CompanyProfitAndLossLineItemMappingID =  @mappingId";

        public static readonly string QueryByMappingBalanceSheetFirst = "SELECT TOP 1 *FROM Mapping_CompanyBalanceSheetLineItems WHERE CompanyBalanceSheetLineItemMappingID =  @mappingId";
        public static readonly string QueryByCashflowFirst = "SELECT TOP 1 *FROM Mapping_CompanyCashFlowLineItems WHERE CompanyCashFlowLineItemMappingID =  @mappingId";
        public static readonly string QueryByGetPcInvestmentKPIQuarterlyValueDraft = "Select * from [dbo].[view_GetPcInvestmentKPIQuarterlyValueDraft] where [WorkflowRequestId]=@workflowRequestId";
        public static readonly string QueryTableColumnNames = "select c.name from sys.columns c inner join sys.tables t on t.object_id = c.object_id and t.name = @tableName and t.type = 'U'";
        public static readonly string QueryDeletePcInvestmentKPIQuarterlyValue = "exec [dbo].[spDeletePcInvestmentKPIQuarterlyValue] @portfolioCompanyId=@portfolioCompanyId";

        public static readonly string QueryByConsolidatedReportKpis = "exec ProcGetConsolidatedReportKpis @templateId";
        public static readonly string QueryByConsolidatedReportModules = "SELECT 0 ModuleID, CASE WHEN SubPageID = 1 THEN 'PC-'+ sub.AliasName WHEN SubPageID = 5 THEN 'DL-'+ sub.AliasName WHEN SubPageID = 6 THEN 'DL-'+ sub.AliasName WHEN SubPageID = 7 THEN 'FD-'+ sub.AliasName WHEN SubPageID = 8 THEN 'FD-'+ sub.AliasName WHEN SubPageID = 9 THEN 'FD-'+ sub.AliasName WHEN SubPageID = 10 THEN 'PC-'+ sub.AliasName WHEN SubPageID = 11 THEN 'PC-'+ sub.AliasName WHEN SubPageID = 13 THEN'IN-'+ sub.AliasName WHEN SubPageID = 15 THEN 'IN-'+ sub.AliasName WHEN SubPageID = 12 THEN 'FD-'+ sub.AliasName WHEN SubPageID = 2 THEN 'PC-'+ sub.AliasName WHEN SubPageID = 14 THEN 'IN-'+ sub.AliasName WHEN SubPageID = 17 THEN 'IN-'+ sub.AliasName WHEN SubPageID = 18 THEN 'IN-'+ sub.AliasName ELSE sub.AliasName END AS Name, SubPageID 'SubPageId', CASE WHEN SubPageID = 1 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 5 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 6 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 7 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 8 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 9 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 10 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 11 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 13 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 15 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 12 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 2 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 14 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 17 THEN pg.AliasName+'-'+ sub.AliasName WHEN SubPageID = 18 THEN pg.AliasName+'-'+ sub.AliasName ELSE sub.AliasName END AS AliasName FROM M_SubPageDetails sub Inner Join M_PageDetails pg on sub.PageID= pg.PageID WHERE SubPageId NOT IN (2,3,4,16,20,38,39) AND pg.PageID NOT IN(9) UNION ALL SELECT ModuleID, AliasName'Name',0 SubPageId,null AliasName FROM M_KpiModules WHERE IsDeleted =0 AND ModuleID in(1,2,3,4,5,6,7,8,9)";
        public static readonly string QueryByConsolidatedReportDeleteQuery = "DELETE FROM MappingConsolidatedReportConfiguration WHERE TemplateId= @templateId";
        public static readonly string QueryByConsolidatedReportPreferenceDeleteQuery = "DELETE FROM ConsolidatedReportPreference WHERE  TemplateId= @templateId";
        public static readonly string QueryByConsolidatedReportDeleteTemplate = "exec ProcConsolidatedReportDeleteTemplate @TemplateId = @templateId";
        public static readonly string QueryByConsolidatedReportPreferenceSection = "SELECT RTRIM(LTRIM(replace(replace(Item,'S-',''),' S-',''))) Section FROM[dbo].[SplitString]((Select  Section FROM  ConsolidatedReportPreference P  WHERE P.TemplateId=@TemplateId),',') WHERE Item Like'S-%' OR Item Like' S-%'";
        public static readonly string QueryByConsolidatedReportPreferenceMapping = "SELECT RTRIM(LTRIM(replace(replace(Item,'M-',''),' M-',''))) MappingSection FROM[dbo].[SplitString]((Select  Section FROM  ConsolidatedReportPreference P  WHERE P.TemplateId=@TemplateId),',') WHERE Item Like'M-%' OR Item Like' M-%'";
        public static readonly string QueryByConsolidatedReportPreferenceFundIds = "SELECT ISNULL(NULLIF(Item,0),0) FundIds FROM[dbo].[SplitString]((Select  FundIds FROM  ConsolidatedReportPreference P  WHERE P.TemplateId=@TemplateId),',')";
        public static readonly string QueryByGetPortfolioCompanyDetailsId = "SELECT *FROM [dbo].[view_GetPCById] WHERE PortfolioCompanyId in(@Id)";
        public static readonly string QueryByGetConsolidatedFundMappedDetails = " Select A.PortfolioCompanyID PortfolioCompanyId,A.FundID FundId,A.DealID DealId,B.FundName,C.CompanyName,C.MasterCompanyName FROM DealDetails A INNER JOIN FundDetails B On A.FundID=B.FundID INNER JOIN PortfolioCompanyDetails C On C.PortfolioCompanyID=A.PortfolioCompanyID WHERE A.FundID IN(Select Item From dbo.SplitString(@fundIds,',')) and A.IsDeleted=0 and C.IsDeleted=0";
        public static readonly string QueryByGetPCConsolidatedDetails = "Select * FROM view_GetPCConsolidatedDetails WHERE FundId IN(Select Item From dbo.SplitString(@FundIds,','))";
        public static readonly string QueryByGetDealConsolidatedDetails = "Select * FROM view_GetDealConsolidatedDetails WHERE FundId IN(Select Item From dbo.SplitString(@funIds,','))";
        public static readonly string QueryMasterKpiByModuleId = @"SELECT DISTINCT(MasterKpiID)'KPIID',KPI'DisplayName',MM.KPIInfo'KpiInfo',4'Order',CASE WHEN m.ParentKPIID IS NOT NULL THEN (SELECT KPI FROM M_MasterKpis WHERE MasterKpiID=m.ParentKPIID) ELSE NULL END Parentkpi FROM PCMasterKpiValues V
                                                                    INNER JOIN Mapping_Kpis M ON V.MappingKpisID = M.Mapping_KpisID
                                                                    INNER JOIN M_MasterKpis MM ON M.KpiID = MM.MasterKpiID
                                                                    WHERE V.PortfolioCompanyID = @companyId AND V.IsDeleted = 0 AND M.IsDeleted = 0  AND MM.IsDeleted = 0  AND V.ModuleID = @moduleId AND MM.ModuleID = @moduleId AND M.ModuleID = @moduleId
                                                                    AND MM.KPIInfo<> 'Text' AND MM.IsHeader=0 Order by MM.KPI";
        public static readonly string QueryByGetValuationCompanies = "exec ProcGetValuationCompanies @TemplateId=@templateId,@CompanyIds = @companyIds";
        public static readonly string QueryByGetValuationData = "exec ProcGetValuationData @CompanyIds=@companyIds,@TemplateId=@templateId,@FromYear=@fromYear,@ToYear=@toYear";
        public static readonly string QueryByGetValuationField = "exec ProcGetValuationField @TemplateId=@templateId,@CompanyIds = @companyIds";
        public static readonly string QueryByGetPcCustomList = "SELECT FieldId,GroupName,DisplayOrder,FeatureId 'CompanyId' from PortfolioCompanyCustomListDetails WHERE FeatureId IN(Select Item From dbo.SplitString(@companyId,',')) AND FieldId IN (Select Item From dbo.SplitString(@fieldId,',')) AND IsDeleted = 0 ORDER BY FeatureId,DisplayOrder";
        public static readonly string QueryByGetValuationFieldValue = "exec ProcGetValuationFieldValue @TemplateId=@templateId,@CompanyIds = @companyIds";
        public static readonly string QueryByGetValuationKpis = "exec ProcGetValuationKpis @TemplateId=@templateId,@CompanyIds=@companyIds";
        public static readonly string QueryByConsolidatedDealTrackrecordDetails = "Select * From view_GetDealTradingConsolidatedDetails WHERE FundId IN(Select Item From dbo.SplitString(@fundIds,',')) AND (Quarter+' '+Cast(Year as varchar)) IN(Select Item From dbo.SplitString(@quarterYears,','))";
        public static readonly string QueryByConsolidatedFundTrackrecordDetails = "Select * From view_ConsolidatedFundTrackRecord WHERE FundId IN(Select Item From dbo.SplitString(@fundIds,',')) AND (Quarter+' '+Cast(Year as varchar)) IN(Select Item From dbo.SplitString(@quarterYears,','))";
        public static readonly string QueryByAnalyticsFundTrackRecordDetails = "Select * From view_ConsolidatedFundTrackRecord";
        public static readonly string QueryByConsolidatedInvestmentKpis = @"Select A.FieldId KpiId,A.KPI,A.KpiInfo,B.DisplayOrder,B.PortfolioCompanyId,'Inv' Section FROM (Select OI.FieldId,MSt.KPI,MSt.KpiInfo FROM MappingConsolidatedReportConfiguration OI 
                                                                            INNER JOIN M_InvestmentKPI MSt On MSt.InvestmentKPIId=OI.FieldId WHERE TemplateId IN(Select Item From dbo.SplitString(@templateIds,',')) AND ModuleID=4 and SubPageId=0 and MSt.IsDeleted=0
                                                                            ) A LEFT OUTER JOIN view_GetConsolidatedInvestmentKPI B On A.FieldId=B.KpiId  ORDER BY  ISNULL(DisplayOrder, FieldId);";
        public static readonly string QueryByConsolidatedTradingKpis = @"Select A.FieldId KpiId,A.KPI,A.KpiInfo,B.DisplayOrder,B.PortfolioCompanyId,'Tr' Section,A.IsBold,A.IsHeader FROM (Select OI.FieldId,MSt.KPI,MSt.KpiInfo,MSt.IsHeader,MSt.IsBoldKPI IsBold FROM MappingConsolidatedReportConfiguration OI 
                                                                        INNER JOIN M_MasterKpis MSt On MSt.MasterKpiID=OI.FieldId WHERE TemplateId IN(Select Item From dbo.SplitString(@templateIds,',')) AND MSt.ModuleID=1 and OI.ModuleID=1 AND MSt.IsDeleted=0
                                                                        ) A LEFT OUTER JOIN view_GetConsolidatedTradingKPI B On A.FieldId=B.KpiId  ORDER BY  ISNULL(DisplayOrder, FieldId)";
        public static readonly string QueryConsildatedTemplates = "Select C.TemplateId,C.TemplateName,CASE WHEN C.ExcelTemplateId=1001 THEN 'true' ELSE C.IsActive END IsActive,C.ExcelTemplateId,ex.Name ExcelTemplateName FROM (Select A.TemplateId,A.TemplateName,dbo.ConsolidatedReportPreferenceFunction(A.TemplateId) IsActive,ISNULL(B.ExcelTemplate,1000) ExcelTemplateId  FROM ConsolidatedReportConfiguration A LEFT JOIN ConsolidatedExcelTemplatePreference B on A.TemplateId=B.TemplateId WHERE A.IsDeleted=0 and A.IsDefault=0 ) C inner join MConsolidatedReportExcelTemplate ex on c.ExcelTemplateId=ex.TemplateId where ex.IsDeleted=0  ORDER BY TemplateName ASC";
        public static readonly string QueryByGetProfitLossLineItems = "SELECT *FROM M_ProfitAndLoss_LineItems WHERE IsDeleted = 0";
        public static readonly string QueryByGetMappingProfitLossLineItems = "SELECT *FROM Mapping_CompanyProfitAndLossLineItems WHERE PortfolioCompanyID = @companyId AND IsDeleted = 0";
        public static readonly string QueryByGetProfitAndLossValues = "SELECT *FROM ProfitAndLossValues WHERE CompanyProfitAndLossLineItemMappingID IN (SELECT CompanyProfitAndLossLineItemMappingID from Mapping_CompanyProfitAndLossLineItems WHERE PortfolioCompanyID=@companyId AND IsDeleted =0)AND IsDeleted =0";
        public static readonly string QueryByMasterKpiDraftValues = "SELECT * FROM PcMasterKpiValueDraft WHERE IsDeleted = 0 AND PortfolioCompanyId = @companyId AND ModuleId = @moduleId AND WorkflowMappingId = @WorkflowRequestMappingId";
        public static readonly string QueryByConsolidatedInvestmentKpiValues = "Select * FROM view_GetConsolidatedInvestmentKPIValues WHERE QuarterYear IN(Select Item From dbo.SplitString(@QuarterYears,','))";
        public static readonly string QueryByConsolidatedTradingKpiValues = "Select * FROM view_GetConsolidatedTradingKPIValues WHERE QuarterYear IN(Select Item From dbo.SplitString(@QuarterYears,','))";
        public static readonly string QueryBySpCompanyKpiValuesList = @"exec spCompanyKpiValues @CompanyId = @CompanyId";
        public static readonly string QueryBySpCompanyKpiActualValuesList = @"exec spCompanyKpiActualValues @CompanyId = @CompanyId, @dataType= @dataType";
        public static readonly string QueryBySpCompanyKpiBudgetValuesList = @"exec spCompanyKpiBudgetValues @CompanyId = @CompanyId, @dataType= @dataType";
        public static readonly string QueryBySpCompanyKpiForecastValuesList = @"exec spCompanyKpiForecastValues @CompanyId = @CompanyId, @dataType= @dataType";
        public static readonly string QueryBySpInvestmentKpiValuesList = @"exec spInvestmentKpiValues @CompanyId = @CompanyId";
        public static readonly string QueryByGetBalancesheetLineItems = "SELECT * FROM M_BalanceSheet_LineItems WHERE IsDeleted = 0";
        public static readonly string QueryByGetMappingBalancesheetLineItems = "SELECT * FROM Mapping_CompanyBalanceSheetLineItems WHERE PortfolioCompanyID = @companyId AND IsDeleted = 0";
        public static readonly string QueryByGetBalancesheetValues = "SELECT * FROM BalanceSheetValues WHERE CompanyBalanceSheetLineItemMappingID IN (SELECT CompanyBalanceSheetLineItemMappingID from Mapping_CompanyBalanceSheetLineItems WHERE PortfolioCompanyID=@companyId AND IsDeleted =0)AND IsDeleted =0";
        public static readonly string QueryByGetCashflowLineItems = "SELECT * FROM M_CashFlow_LineItems WHERE IsDeleted = 0";
        public static readonly string QueryByGetMappingCashflowItems = "SELECT * FROM Mapping_CompanyCashFlowLineItems WHERE PortfolioCompanyID = @companyId AND IsDeleted = 0";
        public static readonly string QueryByGetCashflowValues = "SELECT * FROM CashFlowValues WHERE CompanyCashFlowLineItemMappingID IN (SELECT CompanyCashFlowLineItemMappingID from Mapping_CompanyCashFlowLineItems WHERE PortfolioCompanyID=@companyId AND IsDeleted =0)AND IsDeleted =0";
        public static readonly string QueryByGetBalancesheetForecastLineItems = "SELECT * FROM BalanceSheet_ForecastData WHERE CompanyBalanceSheetLineItemMappingID IN (SELECT CompanyBalanceSheetLineItemMappingID from Mapping_CompanyBalanceSheetLineItems WHERE PortfolioCompanyID=@companyId AND IsDeleted =0)AND IsDeleted =0";
        public static readonly string QueryByGetCashflowForecastLineItems = "SELECT * FROM CashFlow_ForecastData WHERE CompanyCashFlowLineItemMappingID IN (SELECT CompanyCashFlowLineItemMappingID from Mapping_CompanyCashFlowLineItems WHERE PortfolioCompanyID=@companyId AND IsDeleted =0)AND IsDeleted =0";
        public static readonly string QueryByGetPLForecastValues = "SELECT *FROM ProfitAndLoss_ForecastData WHERE CompanyProfitAndLossLineItemMappingID IN (SELECT CompanyProfitAndLossLineItemMappingID from Mapping_CompanyProfitAndLossLineItems WHERE PortfolioCompanyID=@companyId AND IsDeleted =0)AND IsDeleted =0";
        public static readonly string QueryByGetMasterKpiNotMappedList = "exec GetMasterkpiNotMappedList @companyId=@companyId,@Type=@Type,@ModuleId=@ModuleId";
        public static readonly string QueryBySpOperationalKpiValuesDraftList = @"exec spGetOperationalKpiValuesDraft @CompanyId = @CompanyId, @WorkflowMappingId = @WorkflowMappingId";
        public static readonly string QueryByConsolidatedOpKpis = "Select A.FieldId KpiId, A.KPI, A.KpiInfo, B.DisplayOrder, B.PortfolioCompanyId, 'Op' Section,IsBold,IsHeader FROM ( Select OI.FieldId, MSt.KPI, MSt.KpiInfo,MSt.IsHeader,MSt.IsBoldKPI IsBold FROM MappingConsolidatedReportConfiguration OI INNER JOIN M_SectorwiseOperationalKPI MSt On MSt.SectorwiseOperationalKPIID = OI.FieldId WHERE TemplateId IN ( Select Item From dbo.SplitString(@templateIds, ',') ) AND ModuleID = 3 and SubPageId = 0 and MSt.IsDeleted = 0 ) A LEFT OUTER JOIN view_GetConsolidatedOperationalKPI B On A.FieldId = B.KpiId ORDER BY ISNULL(DisplayOrder, FieldId);";
        public static readonly string QueryByConsolidatedOperationalKpiValues = "Select * FROM view_GetConsolidatedOperationalKPIValues WHERE QuarterYear IN(Select Item From dbo.SplitString(@QuarterYears,',')) AND PortfolioCompanyId IN(SELECT DISTINCT PortfolioCompanyId FROM DealDetails WHERE IsDeleted=0 AND FundID IN(Select Item From dbo.SplitString(@fundIds,',')))";
        public static readonly string QueryByGetGroupingList = "SELECT * FROM MGroupingList WHERE IsDeleted = 0";

        public static readonly string QueryByGetConsolidatedFundFillerDetails = "SELECT FundID as FundId,FundName FROM FundDetails WHERE IsDeleted=0 AND FundID IN(Select Item From dbo.SplitString(@fundIds,','))";
        public static readonly string QueryByGetConsolidatedFundDetails = "Select * FROM view_ConsolidatedFundDetails WHERE FundId IN(Select Item From dbo.SplitString(@FundIds,','))";
        public static readonly string QueryByGetAnalyticsFundDetails = "Select * FROM view_ConsolidatedFundDetails";
        public static readonly string QueryByGetAnalyticsFundDetailsById = "Select * FROM view_ConsolidatedFundDetails WHERE FundId IN ({0})";
        public static readonly string QueryByGetFormulaBySelectedKPISection = "exec GetFormulaBySelectedKPISection @KpiId=@KpiId,@Desc=@Desc,@CompanyId=@CompanyId";
        public static readonly string QueryByGetViewPLActualBudgetValues = "Select * FROM view_PLActualBudgetValues WHERE PortfolioCompanyId=@CompanyId and KpiId=@KpiId and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewPLForecastValues = "Select * FROM view_PLForecastValues WHERE PortfolioCompanyId=@companyId and KpiId=@KpiId and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewBSActualBudgetValues = "Select * FROM view_BSActualBudgetValues WHERE PortfolioCompanyId=@CompanyId and KpiId=@KpiId and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewBSForecastValues = "Select * FROM view_BSForecastValues WHERE PortfolioCompanyId=@companyId and KpiId=@KpiId and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewCFActualBudgetValues = "Select * FROM view_CFActualBudgetValues WHERE PortfolioCompanyId=@CompanyId and KpiId=@KpiId and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewCFForecastValues = "Select * FROM view_CFForecastValues WHERE PortfolioCompanyId=@CompanyId and KpiId=@KpiId and Year in(Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewTradingKpiValues = "Select * FROM view_GetConsolidatedTradingKPIValues WHERE PortfolioCompanyId=@CompanyId and KpiId=@KpiId and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewInvestmentKpiValues = "Select * FROM view_GetConsolidatedInvestmentKPIValues WHERE PortfolioCompanyId=@CompanyId and KpiId=@KpiId and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewOperationalKpiValues = "Select * FROM view_GetConsolidatedOperationalKPIValues WHERE PortfolioCompanyId=@CompanyId and KpiId=@KpiId  and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewCreditKpiValues = "Select * FROM view_GetCreditKPIValues WHERE PortfolioCompanyId=@CompanyId and KpiId=@KpiId and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewImpactKpiValues = "Select * FROM view_ViewImpactValues WHERE PortfolioCompanyId=@CompanyId and KpiId=@KpiId and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByGetViewCompanyKPIValues = "Select * FROM view_CompanyKPIValues WHERE PortfolioCompanyId=@CompanyId and KpiId=@KpiId and Year in (Select Item From dbo.SplitString(@year,','))";
        public static readonly string QueryByTVPIbyVintageYearofInvestor = "exec spTVPIByVintageYearOfInvestor @fromDate=@fromDate,@toDate=@toDate,@InvestorId=@investorId";
        public static readonly string QueryByGetAllWorkflowRequest = "Select * from [dbo].[view_GetActiveWorkflowRequest]";
        public static readonly string QueryByAllCompaniesMappedCurrency = "Select A.PortfolioCompanyID PortfolioCompanyId,MC.CurrencyCode Currency FROM PortfolioCompanyDetails A Left Join M_Currency MC on A.ReportingCurrencyID=MC.CurrencyID";
        public static readonly string QueryByAllCompaniesMappedFundCurrency = "Select A.FundID FundId,MC.CurrencyCode Currency FROM FundDetails A Left Join M_Currency MC on A.CurrencyID=MC.CurrencyID";
        public static readonly string QueryByGetOpMasterKpisByKpiId = "SELECT * FROM M_SectorwiseOperationalKPI WHERE IsDeleted = 0 and SectorwiseOperationalKPIID=@SectorwiseOperationalKPIID";
        public static readonly string QueryByCompanyNameFomPortfolioCompanyDetailsDraft = "SELECT  CompanyName,C.CurrencyCode FROM PortfolioCompanyDetailsDraft P LEFT JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID WHERE PortfolioCompanyID = @companyId and p.WorkfloRequestId = @WorkflowRequestId";
        public static readonly string QueryByspGetKpiByFormulaKpiId = "exec spGetKpiByFormulaKpiId @KpiId=@KpiId,@Module=@Module";
        public static readonly string QueryByGetChartValueByModuleId = "EXEC GetChartValueByModuleId @ModuleId=@ModuleId";
        public static readonly string InvestorBulkUploadTemplate = "select v.InvestorId,vi.InvestorName,f.FundName,v.FirmName,f.FundSize,f.VintageYear, v.InvestorStake from view_InvestorFundList v left join InvestorType vi on v.InvestorId = vi.InvestorId left join FundDetails f on v.FundId = f.FundID where v.InvestorId = @InvestorId order by f.FundName asc";
        public static readonly string QueryByMasterKPIList = "SELECT MasterKpiID'KPIId',KPI,KpiInfo'KPIInfo' FROM M_MasterKpis WHERE IsDeleted=0 and ModuleID=ModuleID";
        public static readonly string QueryByGetInvestmentKpisByKpiId = "SELECT Formula,InvestmentKPIId FormulaKpiId  FROM M_InvestmentKPI WHERE IsDeleted = 0 and Formula <> '' AND Formula <> '||||'";
        public static readonly string QueryBySpMasterKpiValuesList = @"exec spMasterKpiValues @CompanyId = @CompanyId, @dataType= @dataType, @ValueTypeId=@ValueTypeId,@ModuleId=@ModuleId";
        public static readonly string QueryGetCompanyKpiById = @"select * from PCCompanyKPIMonthlyValue where PCCompanyKPIMonthlyValueID=@PCCompanyKPIMonthlyValueID";
        public static readonly string QueryGetCompanyDetailsById = "select CompanyName,CurrencyCode from PortfolioCompanyDetails P LEFT JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID WHERE PortfolioCompanyID =@companyId";
        public static readonly string QueryBySpImpactKpiValuesList = @"exec spImpactKpiValues @CompanyId = @CompanyId, @dataType= @dataType, @ValueTypeId=@ValueTypeId,@ModuleId=@ModuleId";
        public static readonly string QueryBySpPCInvestmentKpiValuesList = @"exec spPCInvestmentKpiValues @CompanyId = @CompanyId, @dataType= @dataType, @ValueTypeId=@ValueTypeId,@ModuleId=@ModuleId";
        public static readonly string QueryGetImpactMappedKpiList = "SELECT mo.ImpactKPIID AS KpiId,mpo.ParentKPIID AS ParentId,mpo.KPIOrder DisplayOrder,mpo.PortfolioCompanyID,mo.KPI,mo.KpiInfo,mo.IsBoldKPI,mo.IsHeader,mo.MethodologyId FROM M_ImpactKPI mo INNER JOIN Mapping_ImpactKPI_Order mpo ON mo.ImpactKPIID = mpo.ImpactKPIID WHERE mo.IsDeleted = 0 AND mpo.IsDeleted=0 AND mpo.PortfolioCompanyID = @companyId";
        public static readonly string QureyGetInvestMentMappedKpiList = " SELECT mo.InvestmentKPIId AS KpiId,mpo.ParentKPIID AS ParentId,mpo.DisplayOrder DisplayOrder,mpo.PortfolioCompanyID,mo.KPI,mo.KpiInfo,mo.IsBoldKPI,mo.IsHeader,mo.MethodologyId FROM M_InvestmentKPI mo INNER JOIN Mapping_PortfolioInvestmentKPI mpo ON mo.InvestmentKPIId = mpo.KpiID WHERE mo.IsDeleted = 0 AND mpo.IsDeleted=0 AND mpo.PortfolioCompanyID = @companyId;";
        public static readonly string QueryGetPortfolioCompanyDetails = "Select * FROM view_GetPortfolioCompanyDetails";
        public static readonly string QueryGetPortfolioCompanyLocationDetails = "Select * FROM view_GetPortfolioCompanyLocationDetails";
        public static readonly string QueryGetPortfolioCompanyInvestmentProfessionals = "Select * FROM view_GetPortfolioCompanyInvestmentProfessionals";
        public static readonly string QueryGetPortfolioCompanyInvestmentKPIs = "SELECT KpiInfo,KpiId,KPIValue,PortfolioCompanyId,QuarterYear PeriodType,KPI,Year,Quarter,IsHeader,IsBoldKpi,ParentId FROM view_GetConsolidatedInvestmentKPIValues WHERE PortfolioCompanyId=@companyId order by PortfolioCompanyId,DisplayOrder";
        public static readonly string QueryGetPortfolioCompanyOperationalKPIs = "SELECT KpiInfo,KpiId,KPIValue,PortfolioCompanyId,QuarterYear PeriodType,KPI,Year,Quarter,IsHeader,IsBoldKpi,ParentId FROM view_GetConsolidatedOperationalKPIValues WHERE PortfolioCompanyId=@companyId AND  QuarterYear IS  NOT NULL  order by PortfolioCompanyId,DisplayOrder";
        public static readonly string QueryGetPortfolioCompanyCompanyKpis = "SELECT * FROM view_EP_CompanyKpiValues WHERE PortfolioCompanyId=@companyId AND PeriodType<>'' order by PortfolioCompanyId,DisplayOrder";
        public static readonly string QueryGetProfitLossValues = "SELECT * FROM View_ProfitLossValues WHERE PortfolioCompanyId=@companyId AND PeriodType<>'' order by PortfolioCompanyId,DisplayOrder";
        public static readonly string QueryGetBalanceSheetValues = "SELECT * FROM View_BalanceSheetValues WHERE PortfolioCompanyId=@companyId AND PeriodType<>'' order by PortfolioCompanyId,DisplayOrder";
        public static readonly string QueryGetCashflowValues = "SELECT * FROM View_CashflowValues WHERE PortfolioCompanyId=@companyId AND PeriodType<>'' order by PortfolioCompanyId,DisplayOrder";

        public static readonly string QueryGetAnalyticsDealDetailsByCompanyIds = "SELECT Cons.*,Fd.FundName,Comp.CompanyName  FROM view_GetDealConsolidatedDetails Cons INNER JOIN FundDetails Fd On Fd.FundID=Cons.FundId INNER JOIN PortfolioCompanyDetails Comp on Comp.PortfolioCompanyID=Cons.PortfolioCompanyId WHERE Comp.IsDeleted=0 and Fd.IsDeleted=0 AND cons.PortfolioCompanyId IN (SELECT Item FROM DBO.SplitString(@companyIds,','))";
        public static readonly string QueryGetAnalyticsDealDeatils = "SELECT Cons.*,Fd.FundName,Comp.CompanyName  FROM view_GetDealConsolidatedDetails Cons INNER JOIN FundDetails Fd On Fd.FundID=Cons.FundId INNER JOIN PortfolioCompanyDetails Comp on Comp.PortfolioCompanyID=Cons.PortfolioCompanyId WHERE Comp.IsDeleted=0 and Fd.IsDeleted=0";
        public static readonly string QueryGetAnalyticsDealTrackRecord = "Select * FROM view_GetDealTradingConsolidatedDetails WHERE DealId IN (Select Item From dbo.SplitString(@dealId,','))";
        public static readonly string QueryGetCompanyAtualBudgetChartValues = @"SELECT * FROM (Select CASE WHEN Month IS NULL and Quarter IS NULL THEN  'Annual' 	WHEN Month IS NULL THEN 'Quarterly' WHEN Quarter IS NULL THEN  'Monthly' END PeriodType,A.CompanyKPIID KPIId,A.KPIActualValue ActualValue,A.KPIBudgetValue BudgetValue,NULL ForecastValue,A.Year,A.Quarter,A.Month,
                                                                    mst.KPI,mst.KpiInfo,Map.ParentKPIID ParentId,A.PortfolioCompanyID PortfolioCompanyId FROM PCCompanyKPIMonthlyValue A INNER JOIN Mapping_PortfolioCompanyKPI Map on Map.PortfolioCompanyID=A.PortfolioCompanyID AND Map.KpiID=A.CompanyKPIID INNER JOIN M_CompanyKPI mst on mst.CompanyKPIID=Map.KpiID WHERE A.IsDeleted=0 and Map.IsDeleted=0 and mst.IsDeleted=0 AND A.PortfolioCompanyID=@companyId And mst.CompanyKPIID=@kpiId )CompanyValues WHERE CompanyValues.PeriodType=@periodType";
        public static readonly string QueryGetCompanyForecastChartValues = @" SELECT * FROM (Select CASE WHEN Month IS NULL and Quarter IS NULL THEN  'Annual' 	WHEN Month IS NULL THEN 'Quarterly' WHEN Quarter IS NULL THEN  'Monthly' END PeriodType, A.CompanyKPIID KPIId,NULL ActualValue,NULL BudgetValue,A.KPIValue ForecastValue,A.Year,A.Quarter,A.Month, A.PortfolioCompanyID PortfolioCompanyId,
                                                                    mst.KPI,mst.KpiInfo,Map.ParentKPIID ParentId FROM CompanyKPIForecastValues A INNER JOIN Mapping_PortfolioCompanyKPI Map on Map.PortfolioCompanyID=A.PortfolioCompanyID AND Map.KpiID=A.CompanyKPIID INNER JOIN M_CompanyKPI mst on mst.CompanyKPIID=Map.KpiID WHERE A.IsDeleted=0 and Map.IsDeleted=0 and mst.IsDeleted=0 AND A.PortfolioCompanyId=@companyId And mst.CompanyKPIID=@kpiId)CompanyValues WHERE CompanyValues.PeriodType=@periodType";
        public static readonly string QueryGetFundsForAnalytics = "SELECT FundID,FundName FROM FundDetails WHERE IsDeleted=0";
        public static readonly string QueryGetAllCurrency = "SELECT CurrencyID'CurrencyId', Currency, CurrencyCode FROM M_Currency WHERE IsDeleted=0";
        public static readonly string QueryGetInvestorsForAnalytics = "SELECT InvestorId,InvestorName FROM InvestorType";
        public static readonly string QueryGetFeatureForAnalytics = "SELECT FeatureID,Feature from M_Features WHERE Feature IN ('Portfolio Company','Fund','Deal','Investors')";
        public static readonly string QueryGetKpiTypeForAnalytics = "SELECT Name, CASE WHEN Name LIKE 'TradingRecords' THEN 1 WHEN Name LIKE 'CreditKPI' THEN 2 WHEN Name LIKE '%Operational%' THEN 3 WHEN Name LIKE '%Investment%' THEN 4 WHEN Name LIKE '%Company%' THEN 5 WHEN Name LIKE '%Impact%' THEN 6 WHEN Name LIKE '%Profit%' THEN 7 WHEN Name = 'BalanceSheet' THEN 8 WHEN Name = 'CashFlow' THEN 9 WHEN Name = 'CapTable1' THEN 11 WHEN Name = 'CapTable2' THEN 12 WHEN Name = 'CapTable3' THEN 13 WHEN Name = 'CapTable4' THEN 14 WHEN Name = 'CapTable5' THEN 15 WHEN Name = 'CustomTable1' THEN 17 WHEN Name = 'CustomTable2' THEN 18 WHEN Name = 'CustomTable3' THEN 19 WHEN Name = 'CustomTable4' THEN 20 WHEN Name = 'OtherKPI1' THEN 21 WHEN Name = 'OtherKPI2' THEN 22 WHEN Name = 'OtherKPI3' THEN 23 WHEN Name = 'OtherKPI4' THEN 24 WHEN Name = 'OtherKPI5' THEN 25 WHEN Name = 'OtherKPI6' THEN 26 WHEN Name = 'OtherKPI7' THEN 27 WHEN Name = 'OtherKPI8' THEN 28 WHEN Name = 'OtherKPI9' THEN 29 WHEN Name = 'OtherKPI10' THEN 30 WHEN Name = 'CapTable6' THEN 31 WHEN Name = 'CapTable7' THEN 32 WHEN Name = 'CapTable8' THEN 33 WHEN Name = 'CapTable9' THEN 34 WHEN Name = 'CapTable10' THEN 35 WHEN Name = 'OtherCapTable1' THEN 36 WHEN Name = 'OtherCapTable2' THEN 37 WHEN Name = 'OtherCapTable3' THEN 38 WHEN Name = 'OtherCapTable4' THEN 39 WHEN Name = 'OtherCapTable5' THEN 40 WHEN Name = 'OtherCapTable6' THEN 41 WHEN Name = 'OtherCapTable7' THEN 42 WHEN Name = 'OtherCapTable8' THEN 43 WHEN Name = 'OtherCapTable9' THEN 44 WHEN Name = 'OtherCapTable10' THEN 45 END AS ModuleId, AliasName FROM M_SubPageFields WHERE SubPageID IN(2, 3, 38, 41,47) AND isDeleted = 0 AND isActive = 1 ORDER BY ModuleId; ";
        public static readonly string QueryGetKpisForAnalytics = "exec ProcGetAnalyticsKpis @CompanyIds=@companyIds,@ModuleIds=@moduleIds";
        public static readonly string QueryByInvestorFundListByInvestorIdForAnalytics = "SELECT DISTINCT FundId'FundID',FundName FROM [dbo].[view_InvestorFundList] WHERE InvestorId IN (SELECT Item FROM DBO.SplitString(@investorId,','))";
        public static readonly string QueryByCompaniesForAnalytics = "exec ProcGetAnalyticsCompanies @FundIds=@fundIds";
        public static readonly string QueryByCompaniesForMonthlyReport = "exec ProcGetMonthlyReportCompanies @FundIds=@fundIds";
        public static readonly string QueryByGetCompaniesAndKpiByFundId = "exec GetCopmanyKPIMappedWithFund @FundId=@fundId";
        public static readonly string QueryByGetFundDetailById = "exec GetFundDetailById @FundId=@fundId";
        public static readonly string QueryGetPcAnalyticsInvestmentKPIs = "SELECT* FROM view_GetAnalyticsInvestmentKPIValues WHERE PortfolioCompanyId=@companyId  AND KpiId IN (SELECT Item FROM DBO.SplitString(@kpiIds,','))";
        public static readonly string QueryGetPcAnalyticsOperationalKPIs = "SELECT* FROM view_GetAnalyticsOperationalKPIValues WHERE PortfolioCompanyId=@companyId  AND KpiId IN (SELECT Item FROM DBO.SplitString(@kpiIds,','))";
        public static readonly string QueryGetPcanalyticsCompanyKpis = "SELECT * FROM view_Analytics_CompanyKpiValues WHERE PortfolioCompanyId=@companyId  AND KpiId IN (SELECT Item FROM DBO.SplitString(@kpiIds,','))";
        public static readonly string QueryGetPcAnalyticsMasterKpis = "SELECT * FROM view_Analytics_MasterKpiValues WHERE PortfolioCompanyId=@companyId  AND KpiId IN (SELECT Item FROM DBO.SplitString(@kpiIds,','))";
        public static readonly string QueryGetPcAnalyticsProfitLoss = "SELECT * FROM View_AnalyticsProfitLossValues WHERE PortfolioCompanyId=@companyId  AND KpiId IN (SELECT Item FROM DBO.SplitString(@kpiIds,','))";
        public static readonly string QueryGetPcAnalyticsBalanceSheet = "SELECT * FROM View_AnalyticsBalanceSheetValues WHERE PortfolioCompanyId=@companyId  AND KpiId IN (SELECT Item FROM DBO.SplitString(@kpiIds,','))";
        public static readonly string QueryGetPcAnalyticsCashflow = "SELECT * FROM View_AnalyticsCashflowValues WHERE PortfolioCompanyId=@companyId  AND KpiId IN (SELECT Item FROM DBO.SplitString(@kpiIds,','))";
        public static readonly string QueryGetAnalyticInvestorFundList = "SELECT *FROM [dbo].[view_InvestorFundList] WHERE InvestorId IN (Select Item From dbo.SplitString(@investorIds,','))";
        public static readonly string QueryGetInvestorPerformance = "SELECT * FROM view_InvestorCompanyPerformance WHERE InvestorId IN (Select Item From dbo.SplitString(@investorIds,','))";
        public static readonly string QueryGetInvestorTrackRecordCompanyDetails = "SELECT * FROM view_InvestorTrackRecordCompanyDetails WHERE InvestorId IN (Select Item From dbo.SplitString(@investorIds,','))";
        public static readonly string QueryGetInvestorFundTrackRecordFundDetails = "Select * from view_InvestorFundTrackRecordFundDetails WHERE InvestorId IN (Select Item From dbo.SplitString(@investorIds,','))";
        public static readonly string QueryGetAnalyticsDeals = "SELECT * FROM DealDetails WHERE IsDeleted=0 AND FundID IN (Select Item From dbo.SplitString(@fundIds, ','))";
        public static readonly string QueryGetDataAnalyticsInvestorFilter = "SELECT FI.InvestorId,FI.FundId,DL.PortfolioCompanyId,DL.DealId FROM FundInvestors FI INNER JOIN DealDetails DL on FI.FundId=Dl.FundID WHERE FI.IsDeleted=0 and DL.IsDeleted=0 and FI.InvestorId IN(Select Item From dbo.SplitString(@investorIds, ',')) OR FI.FundId IN(Select Item From dbo.SplitString(@fundIds, ',')) OR DL.PortfolioCompanyID IN (Select Item From dbo.SplitString(@companyIds, ','))";
        public static readonly string QueryBySpGetOperationalKpiValuesList = @"exec spGetOperationalKpiValues @CompanyId = @CompanyId, @dataType= @dataType, @ValueTypeId=@ValueTypeId, @fromYear=@fromYear, @toYear=@toYear, @isLastYear=@isLastYear";
        public static readonly string QueryByJoinMappedOperationalKpis = "select mo.SectorwiseOperationalKPIID as KpiId,mpo.ParentKPIID as ParentId,mpo.DisplayOrder,mpo.PortfolioCompanyID,mo.KPI,mo.KpiInfo,mo.IsBoldKPI,mo.IsHeader,mo.Formula as MasterFormula,mpo.Formula,mo.MethodologyId from M_SectorwiseOperationalKPI mo inner join Mapping_PortfolioOperationalKPI mpo on mo.SectorwiseOperationalKPIID = mpo.KpiID where mpo.PortfolioCompanyID = @companyId";
        public static readonly string QueryGetBulkUploadCurrencyRates = "SELECT * FROM  CurrencyRates WHERE IsDeleted = 0 and ToCurrencyId=@ToCurrencyId and FromCurrencyId=@FromCurrencyId";
        public static readonly string QueryGetApiCurrencyRates = "SELECT * FROM  ApiCurrencyRates WHERE IsDeleted = 0 and ToCurrencyId=@ToCurrencyId and FromCurrencyId=@FromCurrencyId";
        public static readonly string QueryGetFOFMasterKpiLineItems = "SELECT  mst.KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.MasterKpiID as KpiId,DisplayOrder,mst.KpiInfo FROM Mapping_Kpis map INNER JOIN M_MasterKpis mst on mst.MasterKpiID=map.KpiID WHERE PortfolioCompanyID in (Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0 AND mst.ModuleID=1 order by DisplayOrder asc";
        public static readonly string QueryGetFOFInvestmentKpiLineItems = "SELECT mst.KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.InvestmentKPIId as KpiId,DisplayOrder,mst.KpiInfo FROM Mapping_PortfolioInvestmentKPI map Inner Join  M_InvestmentKPI mst on map.KpiID=mst.InvestmentKPIId WHERE map.PortfolioCompanyID IN(Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0  order by DisplayOrder asc";
        public static readonly string QueryGetFOFImpactKpiLineItems = "SELECT mst.KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.ImpactKPIID as KpiId,KPIOrder,mst.KpiInfo FROM Mapping_ImpactKPI_Order map Inner Join  M_ImpactKPI mst on map.ImpactKPIID=mst.ImpactKPIID WHERE map.PortfolioCompanyID IN(Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0 order by KPIOrder asc";
        public static readonly string QueryGetFOFOperationalKpiLineItems = "SELECT  mst.KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.SectorwiseOperationalKPIID as KpiId,DisplayOrder FROM Mapping_PortfolioOperationalKPI map INNER JOIN M_SectorwiseOperationalKPI mst on mst.SectorwiseOperationalKPIID=map.KpiID WHERE PortfolioCompanyID in (Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0  order by DisplayOrder asc";
        public static readonly string QueryGetFundList = "SELECT *FROM view_GetFundList WHERE FundID in (Select Item From dbo.SplitString(@fundIds,','))";
        public static readonly string QueryGetFundStaticList = "exec ProcGetFundSelectList";
        public static readonly string QueryByDataAnalyticsCurrencyData = "exec spDataAnalyticsCurrencyData @companyId = @companyId, @moduleId = @moduleId ";
        public static readonly string QueryGetFixedFieldsInfoInvestorPerformance = "SELECT pc.CompanyName, fi.InvestorName, pc.PortfolioCompanyID, fi.FundName, fi.InvestorId, fi.FundId FROM FundInvestor fi join DealDetails dd ON fi.FundId = dd.FundID join PortfolioCompanyDetails pc ON dd.PortfolioCompanyID = pc.PortfolioCompanyID WHERE fi.InvestorId = @InvestorId";
        public static readonly string QueryByFundDetails = "spBulkUploadFundDetails";
        public static readonly string QueryByFundTrackRecord = "spBulkUploadFundTrackRecord";
        public static readonly string QueryByFundPcDetails = "spBulkUploadPcDetails";
        public static readonly string QueryByDealDetails = "spBulkUploadDealDetails";
        public static readonly string QueryByDealTrackRecord = "spBulkUploadDealTrackRecord";
        public static readonly string QueryByCustomDealTrackRecord = "spBulkUploadCustomTrackRecord";
        public static readonly string QueryByFundCustomDetails = "spBulkUploadFundCustomDetails";
        public static readonly string QueryByFundInvestmentKpis = "spBulkUploadFofInvestmentKpis";
        public static readonly string QueryByFundTradingRecords = "spBulkUploadFofTradingRecords";
        public static readonly string QueryByFundOperationalKpis = "spBulkUploadFofOperationalKpis";
        public static readonly string QueryByDropStagingTable = "spImportDropTableQueryAtRuntime";
        public static readonly string QueryGetOperationalKpiById = @"select * from PortfolioCompanyOperationalKPIValues where PortfolioCompanyOperationalKPIValueID=@PortfolioCompanyOperationalKPIValueID";
        public static readonly string QueryGetPortfolioCompanyOperationalKPIMonthlyDataById = @"SELECT * FROM PortfolioCompanyOperationalKPIQuarters WHERE Year=@year AND Quarter is null AND Month=@month AND PortfolioCompanyID=@PortfolioCompanyID AND IsDeleted = 0 and ValueTypeID = @ValueTypeID";
        public static readonly string QueryGetPortfolioCompanyOperationalKPIQuarterlyDataById = @"SELECT * FROM PortfolioCompanyOperationalKPIQuarters WHERE Year=@year AND Quarter=@quarter AND Month is null AND PortfolioCompanyID=@PortfolioCompanyID AND IsDeleted = 0 and ValueTypeID = @ValueTypeID";
        public static readonly string QueryGetPortfolioCompanyOperationalKPIAnnualById = @"SELECT * FROM PortfolioCompanyOperationalKPIQuarters WHERE Year=@year AND Quarter is null AND Month is null AND PortfolioCompanyID=@PortfolioCompanyID AND IsDeleted = 0 and ValueTypeID = @ValueTypeID";
        public static readonly string QueryGetPortfolioMasterKpiChartValues = "SELECT * FROM ViewMasterKpiChartValues WHERE ModuleId=@moduleId AND KPIId=@kpiId AND PortfolioCompanyId=@portfolioCompanyId";
        public static readonly string QueryGetInvestmentKpiChartValues = "SELECT * FROM ViewInvestmentKpiChartValues WHERE ModuleId=@moduleId AND KPIId=@kpiId AND PortfolioCompanyId=@portfolioCompanyId";
        public static readonly string QueryGetPortfolioCompanyLocationDetailsDealAnalytic = "Select * FROM view_GetPortfolioCompanyLocationDetails WHERE PortfolioCompanyId IN (Select Item From dbo.SplitString(@companyIds,','))";
        public static readonly string QueryGetPortfolioCompanyDetailsDealAnalytic = "Select * FROM view_GetPortfolioCompanyDetails WHERE PortfolioCompanyId IN (Select Item From dbo.SplitString(@companyIds,','))";
        public static readonly string QueryGetEsgKpiValues = @"SELECT val.PortfolioCompanyID PortfolioCompanyId, val.Year,val.Quarter,1 as ValueTypeId,val.Value as ActualValue,'Actual' ValueType, MST.KpiInfo as kpiInfo FROM Mapping_ESGKpi map  inner join Esg_Kpi_DataRecords val oN map.EsgKpiID=val.KpiId AND map.PortfolioCompanyID=val.PortfolioCompanyId
                                                              INNER JOIN M_ESGKPI MST ON MST.ESGKpiId=map.EsgKpiID and MST.SubPageId=map.SubPageId WHERE map.PortfolioCompanyID=@portfolioCompanyId and map.EsgKpiID=@kpiId And map.SubPageId=@moduleId and map.IsDeleted=0 and val.IsDeleted=0 and MST.IsDeleted=0";
        public static readonly string QueryGetPortfolioOperationalChartValues = "SELECT * FROM ViewOperationalKpiChartValues WHERE PortfolioCompanyID = @portfolioCompanyId AND KpiID = @kpiId";
        public static readonly string QueryGetPortfolioCompanyChartValues = "SELECT * FROM view_CompanyKpiChartValues WHERE KpiInfo<>'Text' AND PortfolioCompanyId=@portfolioCompanyId  AND KPIId=@kpiId";
        public static readonly string QueryGetPortfolioCompanyChartActualValues = "SELECT * FROM view_CompanyKpiChartValues WHERE KpiInfo<>'Text' AND PortfolioCompanyId=@portfolioCompanyId AND ValueType='Actual' AND ActualValue<>''";
        public static readonly string QueryByGetUsers = "SELECT FirstName,LastName,EmailID,PhoneNumber,Organization, CONVERT(date,U.CreatedOn)CreatedOn,C.Country,IsActive FROM UserDetails U INNER JOIN M_Country C ON U.CountryID = C.CountryID WHERE U.IsDeleted =0 ORDER BY CreatedOn DESC";
        public static readonly string QueryByJoinMappedTradingRecords = "SELECT mo.MasterKpiID AS KpiId,mpo.ParentKPIID AS ParentId,mpo.DisplayOrder,mpo.PortfolioCompanyID,mo.KPI,mo.KpiInfo,mo.IsBoldKPI,mo.IsHeader,mo.MethodologyID FROM M_MasterKpis mo inner join Mapping_Kpis mpo ON mo.MasterKpiID = mpo.KpiID WHERE mo.ModuleID = @moduleId and mpo.PortfolioCompanyID = @companyId";
        public static readonly string QueryByJoinMappedCompanyKpis = "SELECT mo.CompanyKPIID AS KpiId,mpo.ParentKPIID AS ParentId,mpo.DisplayOrder,mpo.PortfolioCompanyID,mo.KPI,mo.KpiInfo,mo.IsBoldKPI,mo.IsHeader,mo.MethodologyId FROM M_CompanyKPI mo inner join Mapping_PortfolioCompanyKPI mpo ON mo.CompanyKPIID = mpo.KpiID WHERE mpo.PortfolioCompanyID = @companyId";
        public static readonly string QueryGetInvestorMappingDetails = "SELECT FI.FundInvestorId,DD.DealID DealId,DD.FundID FundId,DD.PortfolioCompanyID PortfolioCompanyId,DD.DealCustomID DealCustomId,FI.InvestorId FROM DealDetails DD INNER JOIN FundInvestors FI On FI.FundId=DD.FundID WHERE DD.IsDeleted=0 AND FI.IsDeleted=0";
        public static readonly string QueryByGetAnalyticsMappingEsgKpi = "SELECT DISTINCT Mst.KPI KpiDisplayName,CASE WHEN (SELECT TOP 1 EsgKpiID FROM Mapping_ESGKpi  WHERE EXISTS (SELECT DISTINCT * FROM M_ESGKPI b inner join Mapping_ESGKpi m ON m.EsgKpiID = b.ESGKpiId WHERE b.KPI = mst.KPI) and PortfolioCompanyID=Map.PortfolioCompanyID) is not null and Map.ParentId > 0 THEN (SELECT KPI FROM M_ESGKPI WHERE ESGKpiId = Map.ParentId) + '-' + Mst.KPI ELSE Mst.KPI END AS Kpi,Map.EsgKpiID KpiId,sub.SubPageID ModuleId,sub.AliasName ModuleName, ISNULL(Map.ParentId, 0) AS ParentKpiId FROM Mapping_ESGKpi Map Inner Join M_ESGKPI Mst on Mst.ESGKpiId=Map.EsgKpiID Inner Join M_SubPageDetails sub on Map.SubPageId=sub.SubPageID WHERE PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@companyId,',')) and map.IsDeleted = 0 AND Mst.IsHeader=0 and Map.SubPageId IN (SELECT Item FROM dbo.SplitString(@SubPageId,','))";
        public static readonly string QueryByGetAnalyticsEsgKpi = "SELECT* FROM M_ESGKPI WHERE  IsDeleted = 0 and SubPageId IN (SELECT Item FROM dbo.SplitString(@SubPageId,','))";
        public static readonly string QueryByDataAnalyticsDealsV1 = "exec ProcGetFundHoldingAnalytics @CompanyIds = @companyIds, @SubPageIds = @subPageIds,@FieldIds=@fieldIds,@FromYear = @fromYear,@ToYear = @toYear";
        public static readonly string QueryByGetAuditLog = "exec ProcGetAuditLogs @KpiId = @kpiId, @ModuleId = @moduleId,@AttributeId=@attributeId,@CompanyId = @companyId,@ValueType = @valueType";
        public static readonly string QueryGetPortfolioOperationalChartActualValues = "SELECT * FROM ViewOperationalKpiChartValues WHERE  ValueType='Actual' AND ActualValue<>'' AND PortfolioCompanyID = @portfolioCompanyId";
        public static readonly string QueryGetPortfolioMasterKpiChartActualValues = "SELECT * FROM ViewMasterKpiChartValues WHERE ModuleId=@moduleId AND PortfolioCompanyId=@portfolioCompanyId AND ValueType='Actual' AND ActualValue<>''";
        public static readonly string QueryGetPortfolioInvestmentKpiChartActualValues = "SELECT * FROM ViewInvestmentKpiChartValues WHERE ModuleId=@moduleId AND PortfolioCompanyId=@portfolioCompanyId AND ValueType='Actual' AND ActualValue<>''";
        public static readonly string QueryGetPortfolioImpactKpiChartActualValues = "SELECT * FROM ViewImpactKpiChartValues WHERE ModuleId=@moduleId AND PortfolioCompanyId=@portfolioCompanyId AND ValueType='Actual' AND ActualValue<>''";
        public static readonly string QueryGetImpactKpiChartValues = "SELECT * FROM ViewImpactKpiChartValues WHERE ModuleId=@moduleId AND KPIId=@kpiId AND PortfolioCompanyId=@portfolioCompanyId";

        public static readonly string QueryGetMappedKpi = "exec SpGetCompanyWiseMappedKPIs @ModuleId=@ModuleId, @CompanyId=@CompanyId";

        public static readonly string QueryGetPluginKPIsValues = "exec SpGetCompanyWisePluginKPIValues @ModuleId=@ModuleId, @CompanyId=@CompanyId";
        public static readonly string QueryGetCommentary = @"SELECT CommentaryID,PortfolioCompanyID,Quarter,Year,Month,CASE
	WHEN (Month IS NULL OR Month =0)  and (Quarter IS NULL OR Quarter = '') THEN  Cast(Year as varchar)
	WHEN (Month IS NULL OR Month =0) THEN Quarter+' '+Cast(Year as varchar)
	WHEN (Quarter IS NULL OR Quarter = '') THEN  [dbo].[GetMonthName](Month)+' '+Cast(Year as varchar)
	END
	Period,SignificantEventsSection,AssessmentSection,ExitPlansSection,ImpactSection,FootNoteInvestmentKPI,FootNoteTradingRecord FROM  CommentaryDetails WHERE IsDeleted = 0 AND PortfolioCompanyID IN(Select Item From dbo.SplitString(@companyIds,','))";
        public static readonly string QueryGetCustomCommentary = @"SELECT Quarter,Year,FieldID,FieldValue,PageFeatureEntityId,Month,CASE
	WHEN (Month IS NULL OR Month =0)  and (Quarter IS NULL OR Quarter = '') THEN  Cast(Year as varchar)
	WHEN (Month IS NULL OR Month =0) THEN Quarter+' '+Cast(Year as varchar)
	WHEN (Quarter IS NULL OR Quarter = '') THEN  [dbo].[GetMonthName](Month)+' '+Cast(Year as varchar)
	END
	Period FROM  PageConfigurationCommentaryCustomFieldValue WHERE IsDeleted = 0 AND PageFeatureEntityId IN(Select Item From dbo.SplitString(@companyIds,','))";
        public static readonly string QueryByGetPortfolioCustomList = "SELECT GroupId,GroupName,FeatureId,FieldId,DisplayOrder FROM  PortfolioCompanyCustomListDetails WHERE IsDeleted = 0 AND FeatureId IN(Select Item From dbo.SplitString(@companyIds,','))";
        public static readonly string GetMappedKpiForOperationalPcQuery = @"SELECT MSt.KPI AS ItemName,MSt.KPI AS DisplayName,Map.KpiID AS KPIID,Map.ParentKPIID AS ParentId, MSt.IsHeader,Map.PortfolioCompanyID PortfolioCompanyId,MSt.KPIInfo,
                                                                        (SELECT KPI FROM M_SectorwiseOperationalKPI WHERE IsDeleted=0 AND SectorwiseOperationalKPIID=Map.ParentKPIID) Parentkpi
                                                                        FROM Mapping_PortfolioOperationalKPI Map INNER JOIN M_SectorwiseOperationalKPI MSt On MSt.SectorwiseOperationalKPIID=Map.KpiID 
                                                                        WHERE Map.IsDeleted=0  and MSt.IsDeleted=0 AND MSt.KPIInfo<>'Text' AND MSt.IsHeader=0 AND Map.PortfolioCompanyID=@CompanyId ORDER BY MSt.KPI";
        public static readonly string QueryGetChartApplicableKPIs = "exec  spFetchChartApplicableKPIs @CompanyId = @CompanyId,@ModuleId = @ModuleId";

        public static readonly string QueryDefaultDocumentInsert = "exec  spInsertDefaultData @ModuleId = @ModuleId,@PortfolioCompanyID = @PortfolioCompanyID,@AttributeId = @AttributeId,@NewValue = @NewValue,@OldCurrency = @OldCurrency,@NewCurrency = @NewCurrency,@AuditType = @AuditType,@CreatedBy = @CreatedBy,@DocumentId = @DocumentId,@SupportingDocumentsId = @SupportingDocumentsId,@CommentId = @CommentId,@KPI= @KPI, @MonthAndYear=@MonthAndYear,@Comments=@Comments";
        public static readonly string QueryByGetCommentaryDraftByPC = "SELECT CommentaryID,EncryptedCommentaryID,AssessmentSection,ExitPlansSection,ImpactSection,Quarter,Year FROM PortfolioCompanyCommentaryDraft WHERE Quarter = @quarter AND  Year=@year AND PortfolioCompanyID =@Id AND IsDeleted =0";
        public static readonly string QuerGetPortfolioCompanyByUserId = " Select Distinct CompanyID from Mapping_UserGroupCompany where GroupID in (Select Distinct GroupID from  [dbo].[Mapping_UserGroup] where IsDeleted = 0  and  UserID = @userID) and  IsActive = 1 and IsDeleted = 0";
        public static readonly string QueryByInvestmentKpiDetails = "spBulkUploadPcInvestmentKpi";
        public static readonly string QueryByMasterKpiDetails = "spBulkUploadKpi";
        public static readonly string SPBulkUploadFundFinancials = "spBulkUploadFundFinancials";
        public static readonly string QueryByCompanyKpiDetails = "spBulkUploadCompanyKPI";
        public static readonly string QueryByOperationalKpiDetails = "spBulkUploadOperationalKPI";
        public static readonly string QueryByMonthlyReportDetails = "spBulkUploadMonthlyReport";
        public static readonly string QueryGetProfitLossDocument = "exec GetMappedSupportedCommentsDetails @ModuleId = @ModuleId,@CompanyId = @CompanyId,@MappingId = @MappingId,@KpiId = @KpiId,@ValueType = @ValueType,@Month = @Month,@Year = @Year,@Quarter = @Quarter";
        public static readonly string QueryGetFinancialDocument = "exec GetMappedSupportedCommentsDetails @ModuleId = @ModuleId,@CompanyId = @CompanyId,@MappingId = @MappingId,@KpiId = @KpiId,@ValueType = @ValueType,@Month = @Month,@Year = @Year,@Quarter = @Quarter";
        public static readonly string QueryByDataAnalyticsFundsV1 = "exec sp_GetFundAnalyticsDetails @FundIds = @fundIds, @SubPageIds = @subPageIds,@FieldIds=@fieldIds,@FromYear = @fromYear,@ToYear = @toYear";
        public static readonly string QueryByGetMetaData = "exec GetMetaData @userId = @userId";
        public static readonly string QueryByImpactKpiDetails = "spBulkUploadPcImpactKpi";
        public static readonly string QueryByFinancialKpiDetails = "spBulkUploadPcFinancials";
        public static readonly string PublishCommentaryDraft = "exec  PublishCommentaryDraft @PortfolioCompanyID=@PortfolioCompanyID,@WorkflowRequestId=@WorkflowRequestId";
        public static readonly string QueryGetCapTableKpiLineItems = "SELECT mst.KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.KpiId as KpiId,DisplayOrder,mst.KpiInfo,KpiTypeId,mst.IsOverrule FROM MappingCapTable map Inner Join  MCapTable mst on map.KpiId = mst.KpiId AND map.ModuleId = @moduleId AND mst.ModuleId = @moduleId WHERE map.PortfolioCompanyID = @companyId AND map.IsDeleted=0 and mst.IsDeleted=0 AND Map.ModuleId = @moduleId AND mst.ModuleId = @moduleId  order by DisplayOrder asc";
        public static readonly string QueryByCapTableKpiDetails = "spBulkUploadPortfolioCapTable";
        public static readonly string QueryByCapTableConfig = @"SELECT C.PeriodId,CASE
        WHEN (Month IS NULL OR MONTH = 0) and Quarter IS NULL THEN  Cast(C.Year as varchar)
        WHEN (Month IS NULL OR MONTH = 0)  THEN C.Quarter+' '+Cast(C.Year as varchar)
        WHEN Quarter IS NULL THEN  [dbo].[GetMonthName](C.Month)+' '+Cast(C.Year as varchar)
        END
        Period,
        CASE
        WHEN (Month IS NULL OR MONTH = 0) and Quarter IS NULL THEN  1
		END IsAnnually,
		CASE
        WHEN C.Quarter IS NOT NULL THEN 1
		END IsQuarterly,
		CASE
         WHEN Month IS NOT NULL AND MONTH > 0 THEN  1
		END IsMonthly,
        V.ModuleId FROM (SELECT DISTINCT  PeriodId,ModuleId FROM PcCapTableValues WHERE PortfolioCompanyId = @companyId AND IsDeleted = 0) AS V
        INNER JOIN CapTablePeriod C ON C.PeriodId = V.PeriodId AND C.IsDeleted = 0 ORDER BY YEAR DESC,Quarter DESC,MONTH DESC";
        public static readonly string QueryByCapTableValues = "exec spCapTableKpiValues @CompanyId=@companyId,@ModuleId=@moduleId,@PeriodId=@periodId";
        public static readonly string QueryGetProfitAndLossKpiLineItems = "SELECT mst.ProfitAndLossLineItem as KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.ProfitAndLossLineItemID as KpiId,map.DisplayOrder,mst.KpiInfo FROM Mapping_CompanyProfitAndLossLineItems map Inner Join  M_ProfitAndLoss_LineItems mst on map.ProfitAndLossLineItemID=mst.ProfitAndLossLineItemID WHERE map.PortfolioCompanyID IN(Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0  order by DisplayOrder asc";
        public static readonly string QueryGetBalanceSheetKpiLineItems = "SELECT mst.BalanceSheetLineItem as KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.BalanceSheetLineItemID as KpiId,map.DisplayOrder,mst.KpiInfo FROM Mapping_CompanyBalanceSheetLineItems map Inner Join  M_BalanceSheet_LineItems mst on map.BalanceSheetLineItemID=mst.BalanceSheetLineItemID WHERE map.PortfolioCompanyID IN(Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0  order by DisplayOrder asc";
        public static readonly string QueryGetCashFlowKpiLineItems = "SELECT mst.CashFlowLineItem as KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.CashFlowLineItemID as KpiId,map.DisplayOrder,mst.KpiInfo FROM Mapping_CompanyCashFlowLineItems map Inner Join  M_CashFlow_LineItems mst on map.CashFlowLineItemID=mst.CashFlowLineItemID WHERE map.PortfolioCompanyID IN(Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0  order by DisplayOrder asc";

        public static readonly string QueryGetCapTableMappedSupportedCommentsDetails = "EXEC GetCapTableMappedSupportedCommentsDetails @ModuleId=@ModuleId,@CompanyId = @CompanyId,@MappingId =@MappingId,@KpiId = @KpiId,@ValueTypeId = @ValueTypeId,@Month = @Month,\t@Year= @Year,@Quarter = @Quarter,@PeriodId = @PeriodId,@ColumnKpiId = @ColumnKpiId";

        public static readonly string QueryGetKPIDocument = "exec GetKpiSupportedCommentsDetails @ModuleId = @ModuleId,@CompanyId = @CompanyId,@MappingId = @MappingId,@KpiId = @KpiId,@ValueType = @ValueType,@Month = @Month,@Year = @Year,@Quarter = @Quarter";
        public static readonly string QueryGetFOFMasterKpiLineItem = "SELECT  mst.KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.MasterKpiID as KpiId,DisplayOrder,mst.KpiInfo  FROM Mapping_Kpis map INNER JOIN M_MasterKpis mst on mst.MasterKpiID=map.KpiID WHERE PortfolioCompanyID in (Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0 AND mst.ModuleID=2 order by DisplayOrder asc";
        public static readonly string QueryGetFOFOperationalKpiLineItem = "SELECT  mst.KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.SectorwiseOperationalKPIID as KpiId,DisplayOrder,mst.KpiInfo  FROM Mapping_PortfolioOperationalKPI map INNER JOIN M_SectorwiseOperationalKPI mst on mst.SectorwiseOperationalKPIID=map.KpiID WHERE map.PortfolioCompanyID in (Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0  order by DisplayOrder asc";
        public static readonly string QueryGetMonthlyReportKpis = "SELECT  mst.KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.KpiId as KpiId,DisplayOrder,mst.KpiInfo FROM MappingMonthlyReport map INNER JOIN MMonthlyReport mst on mst.KpiId=map.KpiID WHERE map.PortfolioCompanyID in (Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0  order by DisplayOrder asc";
        public static readonly string QueryGetFOFCompanyKpiLineItem = "SELECT  mst.KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.CompanyKPIID as KpiId,DisplayOrder,mst.KpiInfo  FROM Mapping_PortfolioCompanyKPI map INNER JOIN M_CompanyKPI mst on mst.CompanyKPIID=map.KpiID WHERE map.PortfolioCompanyID in (Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0  order by DisplayOrder asc";
        public static readonly string spGetAnalyticsCapTable = "exec spGetAnalyticsCapTable @PeriodId=@PeriodId,@ModuleId=@ModuleId,@CompanyId=@CompanyId";
        public static readonly string QueryGetDataAnalyticsInvestorSections = "SELECT SubPageID InvestorSectionId,Name InvestorSectionName,AliasName FROM M_SubPageDetails WHERE SubPageID IN (15,17,18) AND isDeleted =0 AND isActive = 1 ORDER BY InvestorSectionId";
        public static readonly string QueryByDataRequestPc = "SELECT PortfolioCompanyID'CompanyId',CompanyName  FROM PortfolioCompanyDetails WHERE IsDeleted=0";
        public static readonly string QueryByDataRequestFunds = "SELECT FundID'FundId',FundName  FROM FundDetails WHERE IsDeleted=0";
        public static readonly string QueryByDataRequestUsers = "SELECT NEWID() as 'ReceiverId', UserID as 'UserId', EmailID as 'EmailId', FirstName, LastName,  0 'IsExternal' FROM UserDetails WHERE IsDeleted = 0 AND IsActive = 1";
        public static readonly string QueryByDataRequestExternalUsers = "SELECT NEWID() as 'ReceiverId', Id as 'UserId', EmailId as 'EmailId', FirstName, LastName,  1 'IsExternal' FROM ExternalUsers WHERE IsDeleted = 0";
        public static readonly string QueryByDataRequestAttachments = "SELECT ID,DocumentId,RequestGroupId,Name FROM DataRequestAttachments WHERE IsDeleted = 0 and RequestGroupId = @requestGroupId";
        public static readonly string QueryByDataRequestAllUsers = @"SELECT EmailID as 'EmailId', (FirstName +' ' + LastName) as FullName, 0'IsExternal' FROM UserDetails WHERE IsDeleted = 0 AND IsActive = 1";
        public static readonly string QueryUserBrowserDetails = "SELECT * FROM UserBrowserDetails WHERE LOWER(EmailId) = LOWER(@EmailId)";
        public static readonly string QueryGetInvestmentFundCurrencyByCompanyId = "SELECT TOP 1 P.CompanyName,C.CurrencyCode from PortfolioCompanyDetails P LEFT JOIN DealDetails D ON P.PortfolioCompanyID=D.PortfolioCompanyID LEFT JOIN FundDetails F ON D.FundID=F.FundID LEFT JOIN  M_Currency C ON F.CurrencyID=C.CurrencyID WHERE P.PortfolioCompanyID =@companyId";
        public static readonly string QueryGetPcAnalyticsImpactKpis = "SELECT * FROM View_GetAnalyticsImpactKpi WHERE PortfolioCompanyId=@companyId  AND KpiId IN (SELECT Item FROM DBO.SplitString(@kpiIds,','))";
        public static readonly string QueryByConvertCurrencyValuesBasedOnFxRates = "exec ConvertCurrencyValuesBasedOnFxRates @companyId=@companyId, @fye=@fye, @toCurrencyId = @toCurrencyId, @fromCurrencyId = @fromCurrencyId, @moduleId = @moduleId, @kpiIds=@kpiIds, @currencyRateType = @currencyRateType, @fromYear=@fromYear, @toYear=@toYear";
        public static readonly string QueryGetSubFeatureListByPageConfig = "exec SpGetSubFeatureListByPageconfig @FeatureId = @FeatureId";
        public static readonly string ProcGetPcSubFeaturePermissions = "EXEC ProcGetPcSubFeaturePermissions @UserId = @UserId,@CompanyId = @CompanyId,@FeatureId = @FeatureId";
        public static readonly string ProcGetFundSubFeaturePermissions = "EXEC ProcGetFundSubFeaturePermissions @UserId = @UserId,@FundId = @FundId,@FeatureId = @FeatureId";
        public static readonly string QueryByGetAnalyticsAllKpiValues = "exec GetAnalyticsAllKpiValues @CompanyIds = @CompanyIds, @ModuleId = @moduleId, @KpiIds = @kpiIds,@FromYear= @fromYear,@ToYear= @toYear,@SubPageIds = @subPageIds,@FieldIds = @fieldIds, @ToCurrencyId = @ToCurrencyId, @CurrencyRateType = @CurrencyRateType";
        public static readonly string QueryByGetAnalyticsAllStaticValues = "exec GetAnalyticsAllStaticValues @CompanyIds = @CompanyIds,@SubPageIds = @subPageIds,@FieldIds = @fieldIds";
        public static readonly string QueryByGetViewDealPc = "SELECT DealID 'DealId',PortfolioCompanyID 'CompanyId',FundID'FundId',CompanyName,FundName,EncryptedPortfolioCompanyID FROM view_DealPCFundQuery ORDER BY FundName ASC,CompanyName ASC";
        public static readonly string QueryGetDealsPermission = "SELECT * FROM Mapping_GroupFeature mgf INNER JOIN Mapping_UserGroup mug ON mgf.GroupId = mug.GroupID INNER JOIN M_Groups g ON mug.GroupId = g.GroupID WHERE FeatureID = @featureId AND mug.UserID = @userId AND mgf.IsDeleted =0 AND mug.IsDeleted = 0 AND g.IsDeleted = 0;";
        public static readonly string QueryGetDealPermissions = "EXEC GetUserSubFeatureDetails @FeatureId = @featureId,@UserId=@userId,@Id=@dealId";

        public static readonly string ProcGetSubFeaturePermissions = "EXEC GetUserSubFeatureDetails @FeatureId = @FeatureId,@UserId = @UserId,@Id = @Id";
        public static readonly string QueryGetPermissionBasedInvestorDetails = "EXEC GetInvestorDetails @UserId = @UserId";
        public static readonly string ProcGetFeaturePermissions = "sp_UserFeaturePermissions @UserId = @UserId,@FeatureId = @FeatureId";

        public static readonly string SpProcessFeatureMappingGroup = "spAddOrUpdateFeatureMapping";
        public static readonly string QueryGetFxApiCurrencyRates = "SELECT Rate FROM  ApiCurrencyRates WHERE IsDeleted = 0 and ToCurrencyId=@ToCurrencyId and FromCurrencyId=@FromCurrencyId AND  Date= @Date";
        public static readonly string QueryGetFxBulkUploadCurrencyRates = "SELECT Rate FROM  CurrencyRates WHERE IsDeleted = 0 and ToCurrencyId=@ToCurrencyId and FromCurrencyId=@FromCurrencyId AND  Date= @Date";
        public static readonly string ProcGetLpReportKpiConfig = "ProcGetLpReportKpiConfig";
        public static readonly string ProcGetLpReportConfig = "ProcGetLpReportConfig";
        public static readonly string ProcDeleteLpTemplate = "ProcDeleteLpTemplate";
        public static readonly string ProcGetLpTemplateById = "ProcGetLpTemplateById";
        public static readonly string QueryGetAllKPIsByModuleIds = "exec sp_MasterAllKPIsByModuleIds @ModuleIds=@ModuleIds";
        public static readonly string QueryGetAllMappingKpiByModuleIds = "exec sp_MappingAllKPIsByModuleIds @ModuleIds=@ModuleIds,@CompanyIds=@CompanyIds";
        public static readonly string QueryGetMappingLpReportTemplateByFundId = "Select TemplateId,CompanyId,TemplateName FROM vw_MappingLpReportTemplate WHERE FundId=@fundId AND FundId<>''";
        public static readonly string QueryGetMappingLpReportTemplateByCompanyId = "Select TemplateId,CompanyId,TemplateName FROM vw_MappingLpReportTemplate WHERE CompanyId=@companyId AND CompanyId<>''";
        public static readonly string QueryGetLpReportConfigByCompanyId = "Exec GetLpReportConfigByCompanyId @PortfolioCompanyId=@PortfolioCompanyId,@TemplateId=@TemplateId";
        public static readonly string QueryGetLpReportConfigByFundId = "Exec GetLpReportConfigByFundId @FundId=@FundId,@TemplateId=@TemplateId";
        public static readonly string QueryGetCalculateLpReportKpiValuesByCompanyId = "Exec CalculateLpReportKpiValues @PortfolioCompanyId=@PortfolioCompanyId,@TemplateId=@TemplateId";
        public static readonly string QueryGetFootNotesByCompanyAndModule = "Exec GetFootNotesByCompanyAndModule @PortfolioCompanyId=@PortfolioCompanyId,@ModuleId=@ModuleId";
        public static readonly string QueryGetCommentaryByCompanyId = "Exec SpGetCommentary @Period=@Period,@PortfolioCompanyId=@PortfolioCompanyId";
        public static readonly string QueryGetPortfolioCompanyDetailsById = "Select * FROM view_GetPortfolioCompanyDetails WHERE PortfolioCompanyId IN @companyIds";
        public static readonly string QueryGetPortfolioCompanyLocationDetailsById = "Select * FROM view_GetPortfolioCompanyLocationDetails WHERE PortfolioCompanyId IN @companyIds";
        public static readonly string QueryGetPortfolioCompanyInvestmentProfessionalsById = "Select * FROM view_GetPortfolioCompanyInvestmentProfessionals WHERE PortfolioCompanyId IN @companyIds";
        public static readonly string QueryBySpMonthlyReportValuesList = @"exec spGetMonthlyReportKpiValues @AsOnMonth = @AsOnMonth, @CompanyIds= @CompanyIds";
        public static readonly string QueryGetLpReportLatestQuarter = "Exec GetLpReportLatestQuarter @PortfolioCompanyId=@PortfolioCompanyId,@ModuleId=@ModuleId, @ValueTypeId = @ValueTypeId";

        public static readonly string QueryGetFundCompanyCurrency = "SELECT Id,FinancialYearEnd,CurrencyCode,FundCurrencyCode FROM view_FundCompanyCurrencyCode WHERE Id IN @companyIds";
        public static readonly string QueryByGetAnalyticsParentChildValuesGetAnalyticsAllKpiValues = "exec GetAnalyticsParentChildValues @CompanyIds = @CompanyIds, @ModuleId = @moduleId, @KpiIds = @kpiIds,@FromYear= @fromYear,@ToYear= @toYear,@SubPageIds = @subPageIds,@FieldIds = @fieldIds, @ToCurrencyId = @ToCurrencyId, @CurrencyRateType = @CurrencyRateType, @ParentKpiId = @ParentKpiId";
        public static readonly string QueryGetGetCommentaryPeriodMSubSectionField = "SELECT TOP 1 * FROM MSubSectionFields WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE SubPageID = (SELECT TOP 1 SubPageID FROM M_SubPageDetails WHERE PageID = (SELECT TOP 1 PageID FROM M_PageDetails WHERE [Name] = 'Portfolio Company') AND [Name] = 'Commentary') AND isDeleted = 0 AND [Name] = 'Commentary Period') AND [Name] = 'Period'";
        public static readonly string QueryByGetGrowthReportKpi = @"exec ProcGetGrowthReportKpiConfig @CompanyId = @CompanyId, @ModuleId = @ModuleId";
        public static readonly string QueryGetMasterKpiLineItems = "SELECT  mst.KPI,map.PortfolioCompanyID PortfolioCompanyId,mst.MasterKpiID as KpiId,DisplayOrder,mst.KpiInfo  FROM Mapping_Kpis map INNER JOIN M_MasterKpis mst on mst.MasterKpiID=map.KpiID WHERE PortfolioCompanyID in (Select Item From dbo.SplitString(@companyIds,',')) and map.IsDeleted=0 and mst.IsDeleted=0 AND mst.ModuleID=@moduleId order by DisplayOrder asc";
        public static readonly string QueryByDeleteGrowthReportKpi = @"exec ProcDeleteGrowthReport";
        public static readonly string ProcGetParentChildEsgKpiValues = @"EXEC ProcGetParentChildEsgKpiValues @CompanyIds = @CompanyIds,@ParentKPIId = @ParentKPIId,@SubPageId = @SubPageId,@FromYear = @FromYear,@ToYear = @ToYear,@PageNo = @PageNo,@PageSize = @PageSize";
        public static readonly string QueryByProcGetGrowthRowKpi = @"exec ProcGetGrowthRowKpi";
        public static readonly string QueryByProcGetGrowthColumnKpi =
            @"exec ProcGetGrowthColumnKpiHeader";
        public static readonly string ProcGrowthReportColumnKpiData =
            @"exec ProcGrowthReportColumnKpiData @CompanyIds = @CompanyIds, @JSON = @JSON";
        public static readonly string ProcGrowthReportRowKpiData =
            @"exec ProcGrowthReportRowKpiData @CompanyIds = @CompanyIds, @JSON = @JSON";
        public static readonly string ProcGrowthReportLTMRowKpiData =
            @"exec ProcGrowthReportRowKpiDataLTM @CompanyIds = @CompanyIds, @JSON = @JSON";

        public static readonly string GetLpReportFundMappedCompanies = "exec SP_GetLpReportFundMappedCompanies @FundId=@FundId,@TemplateId=@TemplateId";
        public static readonly string QueryUpdateKpiPreference = "exec  UpdateKpiPreference @PortfolioCompanyId = @PortfolioCompanyId ,@ModuleId = @ModuleId,@KpiId = @KpiId,@IsDefault = @IsDefault,@IsFavourite = @IsFavourite";

        public static readonly string GetDocumentsConfigFromIngestionTable = "Select DI.[Id] as [Id], DI.ProcessId as [ProcessId] , DIM.CompanyId as [CompanyId], DIM.[FeatureId] as [FeatureId] , DIM.[SourceTypeId] as [SourceTypeId], DI.S3Path as [S3Path], DI.[FileName] as [FileName], DI.Extension as [Extension] , DI.[DocumentTypeId] as [DocumentTypeId] , DI.[PeriodType] as [PeriodType] , DI.[Year] as [Year] , DI.[Month] as [Month] , DI.[Quarter] as [Quarter]  from DataIngestionDocuments DI inner join DIMappingDocumentsDetails DIM on DI.ProcessId = DIM.ProcessId where DI.IsDeleted = 0 AND DIM.ProcessId  = @DIProcessID";
        #region Clo pageconfig
        public static readonly string GetCLOPageConfiguration = "GetCLOPageConfiguration";
        public static readonly string QueryToInsertCLOConfig = "INSERT INTO [dbo].[CLO_MappingTabDetails]\r\n           ([TabId]\r\n           ,[CompanyId]\r\n           ,[PageId]\r\n           ,[AliasName]\r\n           ,[SequenceNo]\r\n           ,[IsActive]\r\n           ,[IsDelete]\r\n           ,[CreatedOn]\r\n           ,[CreatedBy], [ModifiedOn],[CLOId])\r\n     VALUES\r\n           (@TabId\r\n           ,@CompanyId\r\n           ,@PageId\r\n           ,@AliasName\r\n           ,@SequenceNo\r\n           ,@IsActive\r\n           ,@IsDeleted\r\n           ,GETUTCDATE()\r\n           ,@UserId,GETUTCDATE(),@CLOId)";
        public static readonly string QueryToUpdateCLOConfig = "UPDATE [dbo].[CLO_MappingTabDetails]\r\n   SET [AliasName] = @AliasName\r\n      ,[SequenceNo] = @SequenceNo\r\n      ,[IsActive] = @IsActive\r\n      ,[IsDelete] = @IsDeleted\r\n      ,[ModifiedOn] = GETUTCDATE()\r\n      ,[ModifiedBy] = @UserId\r\n ,[CLOId] = @CLOId WHERE id=@id and [TabId] = @TabId and [CompanyId] = @CompanyId and [PageId] = @PageId";
        public static readonly string GetCLOSubPageConfiguration = "GetCLOSubPageConfiguration";
        public static readonly string QueryToIsTabDetailsExist = "SELECT * FROM CLO_MappingTabDetails where CompanyId=@CompanyId and TabId=@TabId";
        public static readonly string QueryToIsCLOTabDetailsExist = "SELECT * FROM CLO_MappingTabDetails where CompanyId=@CompanyId and TabId=@TabId and CLOId=@CloId";
        public static readonly string QueryToGetIdBasedOnCompanyAndTab = "SELECT Id FROM CLO_MappingTabDetails where (CompanyId=@CompanyId and TabId=@TabId) AND (CLOId = @CloId OR CLOId IS NULL)";
        public static readonly string GetCLOTableConfiguration = "GetCLOTableConfiguration";
        public static readonly string QueryToInsertTableConfig = "INSERT INTO [dbo].[CLO_MappingTableColDetails] ([CompanyId] ,[CLOId] ,[ColumnAliasName] ,[SequenceNo] ,[IsActive] ,[IsDeleted] ,[ColumnId] ,[CreatedBy] ,[CreatedOn] ,[KPITableId] ,[Name] ,[IsCustom]) VALUES (@CompanyId ,@CLOId ,@AliasName ,@SequenceNo ,@IsActive ,@IsDeleted ,@ColumnId ,@UserId ,GETUTCDATE() ,@TableId ,@Name ,@IsCustom)";
        public static readonly string QueryToUpdateTableConfig = "UPDATE [dbo].[CLO_MappingTableColDetails] SET [CompanyId] =@CompanyId ,[CLOId] =@CLOId ,[ColumnAliasName] =@AliasName ,[SequenceNo] =@SequenceNo ,[IsActive] =@IsActive ,[IsDeleted] =@IsDeleted ,[ColumnId] =@ColumnId ,[ModifiedOn] =GETUTCDATE() ,[ModifiedBy] =@UserId ,[KPITableId] =@TableId ,[Name] =@Name ,[IsCustom] =@IsCustom WHERE [id]=@Id";
        public static readonly string QueryToIsCLOTableDetailsExists = "SELECT 1 FROM CLO_MappingTableColDetails where CompanyId=@CompanyId and KPITableId=@TableId and CLOId=@CloId;\r\n";
        public static readonly string QueryToIsTableDetailsExists = "SELECT 1 FROM CLO_MappingTableColDetails where CompanyId=@CompanyId and KPITableId=@TableId;\r\n";
        public static readonly string QueryByIC = "SELECT * FROM CLO_InvestmentCompanyDetails";
        #endregion Clo pageconfig
        public static readonly string QueryByGetMappingFundKpi = "SELECT* FROM MappingFundSectionKpi WHERE FundId = @FundId and IsDeleted = 0 and ModuleId=@ModuleId";
        public static readonly string QueryByGetFundKpi = "SELECT* FROM MFundSectionKpi WHERE  IsDeleted = 0 and ModuleId=@ModuleId";
        public static readonly string QueryByGetMappingFundKpiByModuleIds = "SELECT* FROM MappingFundSectionKpi WHERE FundId = @FundId and IsDeleted = 0 and ModuleId in (SELECT item FROM dbo.SplitString(@ModuleId, ','))";
        public static readonly string QueryByGetFundKpiModuleIds = "SELECT* FROM MFundSectionKpi WHERE  IsDeleted = 0 and ModuleId in (SELECT item FROM dbo.SplitString(@ModuleId, ','))";
        public static readonly string QueryByFundKpiLast = "SELECT TOP 1 * FROM MappingFundSectionKpi WHERE IsDeleted = 0 AND FundId = @fundId  order by MappingFundSectionKpiId desc";
        public static readonly string QueryByMappingFundKpiFirst = "SELECT TOP 1 *FROM MappingFundSectionKpi WHERE MappingFundSectionKpiId =  @mappingId";
        public static readonly string QueryByFundKpiCopyToFunds = "exec [dbo].[ProcFundKpiCopyKPIToFunds] @FundId=@fundId,@UserId=@userId,@FundIds=@fundIds,@ModuleId=@moduleId";
        public static readonly string QueryByDeleteFundKpi = " exec spDeleteFundKpi @FundKpiId =@fundKpiId, @ModuleId = @moduleId";
        public static readonly string QueryByGetFundKpiNotMappedList = "exec GetFundKpiNotMappedList @FundId=@fundId,@ModuleId=@moduleId";
        public static readonly string QueryBySPCreateDuplicateFundKpi = "ProcCreateDuplicateFundKPI";
        public static readonly string QueryBySpFundKpiValuesList = @"exec spFundKpiValues @FundId = @FundId, @dataType= @dataType, @ValueTypeId=@ValueTypeId,@ModuleId=@ModuleId";
        public static readonly string ProcGetAllMappedKpi = "exec GetAllMappedKpi @CompanyIds = @companyIds,@ModuleIds=@moduleIds";
        public static readonly string ProcGetDealByCompanyId = "SELECT TOP 1 DealID 'DealId',InvestmentDate FROM DealDetails WHERE IsDeleted =0 AND PortfolioCompanyID =@companyId";
        public static readonly string QueryByDealTrackRecordLatestQuarterYearByDealId = "SELECT Distinct Year Label,Quarter Value,Quarter+' '+ CAST(Year as varchar) as KeyValue FROM (SELECT  Max(dbo.Getlastdateofquarter(CONVERT(INT, Replace(C.Quarter,'Q', '')),C.Year)) as QuarterDate from  DealDetails B INNER JOIN PortfolioCompanyFundHoldingDetails C On C.DealID=B.DealID where b.DealID=@dealId)A INNER JOIN ( SELECT DISTINCT  C.Year,C.Quarter, Convert(Date,DATEADD(DAY, -1, DATEADD(QUARTER,  CONVERT(INT, Replace(C.Quarter,'Q', '')),Convert(nvarchar(4),C.Year) +'-01-01'))) as QuarterDate from  DealDetails B INNER JOIN PortfolioCompanyFundHoldingDetails C On C.DealID=B.DealID where b.DealID =@dealId) B ON A.QuarterDate=B.QuarterDate";

        public static readonly string QueryByGetFxRate = "EXEC GetCurrencyRates @FromDate = @FromDate, @ToDate = @ToDate, @FilterSource = @FilterSource, @FromCurrencyCode = @FromCurrencyCode, @ToCurrencyCode = @ToCurrencyCode";
        public static readonly string QueryByGetSDGLogo = "SELECT PortfolioCompanyId,Id,DocumentId FROM SDGImages WHERE IsDeleted = 0 AND PortfolioCompanyId IN(Select Item From dbo.SplitString(@companyIds,','))";
        public static readonly string QueryGetFundKpiLineItems = "SELECT  mst.KPI,map.FundId FundId,mst.FundSectionKpiId as KpiId,DisplayOrder,mst.KpiInfo  FROM MappingFundSectionKpi map INNER JOIN MFundSectionKpi mst on mst.FundSectionKpiId=map.KpiId WHERE FundId =@fundId and map.IsDeleted=0 and mst.IsDeleted=0 AND mst.ModuleId=@moduleId order by DisplayOrder asc";
        public static readonly string QueryTOGetCLOReportingDataColumn = "Select consoltab.PageId, consoltab.TabId ,COALESCE(tabdetails.TabName,consoltab.TabName) as sectionName ," +
            "consoltab.TableName, consoltab.TableIdentifier,consoltab.TableObjectName,consoltab.SequenceNo from (Select tab.PageId, tab.TabId , tab.ParentId, tab.TabName , tbl.TableName , tbl.TableIdentifier,tbl.TableObjectName,tbl.SequenceNo from CLO_MTabDetails tab inner join CLO_MKPITable tbl on tbl.TabId = tab.TabId) as consoltab " +
            "left join CLO_MTabDetails as tabdetails on consoltab.ParentId = tabdetails.TabId order by consoltab.PageId , consoltab.TabId";
    }
}
