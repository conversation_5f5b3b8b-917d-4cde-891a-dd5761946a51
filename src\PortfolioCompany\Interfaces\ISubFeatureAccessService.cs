﻿using Contract.Account;
using Contract.PortfolioCompany;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PortfolioCompany.Interfaces
{
    public interface ISubFeatureAccessService
    {
        Task<List<SubFeatureAccessPermissionsModel>> GetSubFeatureAccessPermissions(int userId, int companyId, int featureId, int moduleId = 0);
        Task<List<SubFeatureAccessPermissionsModel>> GetAllSubFeatureAccessPermissions(int userId, int id, int featureId);
        Task<List<PermissionModel>> GetAllFeatureAccessPermissions(int userId, int featureId);
        Task<bool> GetFundSubFeatureAccessPermissions(int userId, int fundId, string kpiType);
    }
}