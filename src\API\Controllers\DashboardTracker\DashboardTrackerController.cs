using API.Filters.CustomAuthorization;
using API.Helpers;
using Contract.Account;
using Contract.PortfolioCompany;
using Contract.Utility;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Net;
using System.Threading.Tasks;
using Utility.Resource;

namespace API.Controllers.DashboardTracker
{
    [Route("api/")]
    [ApiController]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    public class DashboardTrackerController : ControllerBase
    {
        private readonly IDashboardTrackerService _dashboardTrackerService;
        private readonly IHelperService _helperService;
        public DashboardTrackerController(IDashboardTrackerService dashboardTrackerService, IHelperService helperService)
        {
            _dashboardTrackerService = dashboardTrackerService;
            _helperService = helperService;
        }

        [HttpGet("dashboard-tracker/get")]
        [UserFeatureAuthorize((int)Features.DashboardTracker)]
        public async Task<IActionResult> GetCompanies()
        {

            var result = await _dashboardTrackerService.GetPortfolioCompanies(
                new PortfolioCompanyFilter
                {
                    CreatedBy = _helperService.GetCurrentUserId(User)
                });

            return Ok(result);
        }

        [HttpPost("dashboard-tracker/config/save")]
        [UserFeatureAuthorize((int)Features.DashboardTracker)]
        public async Task<IActionResult> SaveDashboardTrackerConfig([FromBody] DashboardTrackerConfigDto dto)
        {
            if (!ModelState.IsValid)
            {
                return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
            }

            try
            {
                int userId = _helperService.GetCurrentUserId(User);

                // Set audit fields based on whether it's an insert or update
                if (dto.ID.HasValue && dto.ID.Value > 0)
                {
                    dto.ModifiedBy = userId;
                    dto.ModifiedOn = DateTime.UtcNow;
                }
                else
                {
                    dto.CreatedBy = userId;
                    dto.CreatedOn = DateTime.UtcNow;
                }

                int result = await _dashboardTrackerService.SaveDashboardTrackerConfigAsync(dto);

                if (result > 0)
                {
                    string message = dto.ID.HasValue && dto.ID.Value > 0
                        ? "Dashboard tracker configuration updated successfully"
                        : "Dashboard tracker configuration added successfully";
                    return JsonResponse.Create(HttpStatusCode.OK, message, new { id = result });
                }
                else
                {
                    return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
                }
            }
            catch (Exception ex)
            {
                // Log the exception here if you have logging configured
                return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
            }
        }

        [HttpPost("dashboard-tracker/table-data")]
        [UserFeatureAuthorize((int)Features.DashboardTracker)]
        public async Task<IActionResult> GetDashboardTableData([FromBody] PaginationFilter filter)
        {
            filter = filter ?? new PaginationFilter();
            int userId = _helperService.GetCurrentUserId(User);
            var result = await _dashboardTrackerService.GetDashboardTableDataAsync(userId, filter);
            return Ok(result);
        }

        [HttpPost("dashboard-tracker/save-dropdown-values")]
        [UserFeatureAuthorize((int)Features.DashboardTracker)]
        public async Task<IActionResult> SaveDropdownValues([FromBody] TrackerDropdownValueDto dto)
        {
            if (dto == null || dto.DropdownValues == null || dto.DropdownValues.Count == 0)
                return BadRequest("Invalid input");

            var result = await _dashboardTrackerService.SaveTrackerDropdownValuesAsync(dto);
            if (result)
                return Ok(new { success = true });
            else
                return StatusCode(500, new { success = false, message = "Failed to save dropdown values." });
        }
        [HttpGet("dashboard-tracker/get-column-data")]
        [UserFeatureAuthorize((int)Features.DashboardTracker)]

        public async Task<IActionResult> GetAllTrackerConfigs()
        {
            var configs = await _dashboardTrackerService.GetAllTrackerConfigsAsync();
            return Ok(configs);
        }
    }

}
