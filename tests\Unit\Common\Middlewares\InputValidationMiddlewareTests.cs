﻿using API.Middlewares;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Moq;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Common.Middlewares
{
    public class InputValidationMiddlewareTests
    {
        [Fact]
        public async Task Invoke_WithValidRequestBody_DoesNotThrowException_CallNext()
        {
            // Arrange
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            context.Request.Body = new MemoryStream(System.Text.Encoding.UTF8.GetBytes("{\"name\":\"Test\"}"));
            context.Request.ContentLength = context.Request.Body.Length;
            // Act
            await middleware.InvokeAsync(context, configuration.Object);

            // Assert
            Assert.True(true); // No exception was thrown
        }

        [Fact]
        public async Task Invoke_WithInvalidRequestBody_ThrowsBadHttpRequestException()
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            context.Request.Body = new MemoryStream(System.Text.Encoding.UTF8.GetBytes("{\"name\":\"<script>alert('test')</script>\"}"));
            context.Request.ContentLength = context.Request.Body.Length;
            await middleware.InvokeAsync(context, configuration.Object);
            Assert.Equal(StatusCodes.Status400BadRequest, context.Response.StatusCode);
        }
        [Fact]
        public async Task Test_SqlInjectionInRequestBody_ThrowsBadHttpRequestException_For_Drop_Commands()
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes("sundrop column Users"));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await Assert.ThrowsAsync<BadHttpRequestException>(() => middleware.InvokeAsync(context, configuration.Object));
        }

        [Fact]
        public async Task Test_SqlInjectionInRequestBody_ThrowsBadHttpRequestException()
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes("delete table Users"));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await Assert.ThrowsAsync<BadHttpRequestException>(() => middleware.InvokeAsync(context, configuration.Object));
        }

        [Fact]
        public async Task Test_JavaExceptionInjectionInRequestBody_ThrowsBadHttpRequestException()
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes("Exception in thread"));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await Assert.ThrowsAsync<BadHttpRequestException>(() => middleware.InvokeAsync(context, configuration.Object));
        }

        [Fact]
        public async Task Test_ServerSideIncludeInjectionInRequestBody_ThrowsBadHttpRequestException()
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes("<!--#include virtual=\"/etc/passwd\" -->"));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await Assert.ThrowsAsync<BadHttpRequestException>(() => middleware.InvokeAsync(context, configuration.Object));
        }

        [Fact]
        public async Task Test_XPathExpandedSyntaxInjectionInRequestBody_ThrowsBadHttpRequestException()
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes("/descendant::node()"));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await Assert.ThrowsAsync<BadHttpRequestException>(() => middleware.InvokeAsync(context, configuration.Object));
        }
        [Theory]
        [InlineData("./../../.../")]
        [InlineData("<iframe src='http://example.com'></iframe>")]
        public async Task ValidateRegex_Identifies_Traversal_Short_Injection_Characters(string requestBody)
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await Assert.ThrowsAsync<BadHttpRequestException>(() => middleware.InvokeAsync(context, configuration.Object));
        }
        [Theory]
        [InlineData("<img src='http://example.com' onerror='alert(1)'>")]
        public async Task ValidateRegex_Identifies_XSS_Injection_Characters(string requestBody)
        {
            DefaultHttpContext context = await HttpContextMock(requestBody);
            Assert.Equal(StatusCodes.Status400BadRequest, context.Response.StatusCode);
        }
        [Theory]
        [InlineData("<img src='http://example.com' onerror='alert(\"XSS\")'>")]
        public async Task ValidateRegex_Identifies_XSS_Injection_Characters_InLine(string requestBody)
        {
            DefaultHttpContext context = await HttpContextMock(requestBody);
            Assert.Equal(StatusCodes.Status400BadRequest, context.Response.StatusCode);
        }
        [Theory]
        [InlineData("<img src='test.jpg'>")]
        [InlineData("<iframe src='test.html'>")]
        [InlineData("<object data='test.swf'>")]
        [InlineData("<embed src='test.swf'>")]
        public async Task Test_HarmfulTag(string requestBody)
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await Assert.ThrowsAsync<BadHttpRequestException>(() => middleware.InvokeAsync(context, configuration.Object));
        }
        [Theory]
        [InlineData("<xlink:href='test'>")]
        [InlineData("<html data:text/html='test'>")]
        [InlineData("<pattern formaction='test'>")]
        [InlineData("<!ENTITY % test PUBLIC>")]
        [InlineData("<style>@import 'test.css';</style>")]
        public async Task Test_AttributeVector(string requestBody)
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await Assert.ThrowsAsync<BadHttpRequestException>(() => middleware.InvokeAsync(context, configuration.Object));
        }

        [Theory]
        [InlineData("<a href='javascript:alert(1)'>Click me</a>")]
        public async Task Test_JavascriptUriAndTags(string requestBody)
        {
            DefaultHttpContext context = await HttpContextMock(requestBody);
            Assert.Equal(StatusCodes.Status400BadRequest, context.Response.StatusCode);
        }
        [Theory]
        [InlineData("<img onerror='alert(1)' src='test.jpg'>")]
        [InlineData("<div style='background:url(javascript:alert(1))'>Test</div>")]
        [InlineData("<video poster='javascript:alert(1)'>Test</video>")]
        [InlineData("<div background='javascript:alert(1)'>Test</div>")]
        //  [InlineData("<style>-type:multipart</style>")]
        public async Task Test_MaliciousAttributeInjectionAndMHTMLAttacks(string requestBody)
        {
            DefaultHttpContext context = await HttpContextMock(requestBody);
            Assert.Equal(StatusCodes.Status400BadRequest, context.Response.StatusCode);
        }

        private static async Task<DefaultHttpContext> HttpContextMock(string requestBody)
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await middleware.InvokeAsync(context, configuration.Object);
            return context;
        }

        [Theory]
        [InlineData("SELECT * FROM users WHERE id = 1 OR sleep(5)")]
        [InlineData("SELECT benchmark(1000000,MD5('test'))")]
        public async Task Test_BlindSqlInjection(string requestBody)
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await Assert.ThrowsAsync<BadHttpRequestException>(() => middleware.InvokeAsync(context, configuration.Object));
        }
        [Theory]
        [InlineData("%SYSTEMROOT%\\System32")]
        public async Task Test_FileLocationAttempts(string requestBody)
        {
            var configurationSection = new Mock<IConfigurationSection>();
            configurationSection.Setup(a => a.Value).Returns("Endpoint1,Endpoint2");

            var configuration = new Mock<IConfiguration>();
            configuration.Setup(c => c.GetSection("ExcludeEndPointList")).Returns(configurationSection.Object);
            var middleware = new InputValidationMiddleware(_ => Task.CompletedTask);
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
            context.Request.ContentLength = context.Request.Body.Length;
            context.Request.Method = HttpMethods.Post;
            context.Request.ContentType = "application/json";
            await Assert.ThrowsAsync<BadHttpRequestException>(() => middleware.InvokeAsync(context, configuration.Object));
        }
    }
}