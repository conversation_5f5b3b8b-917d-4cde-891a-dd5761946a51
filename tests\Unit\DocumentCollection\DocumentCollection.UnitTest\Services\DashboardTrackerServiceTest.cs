﻿using Contract.PortfolioCompany;
using Contract.Repository;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models.DashboardTracker;
using DataAccessLayer.Models.Tracker;
using DataAccessLayer.UnitOfWork;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Services;
using Moq;
using System.Linq.Expressions;
using Workflow;
using Workflow.Models;

namespace DocumentCollection.UnitTest.Services
{
    public class DashboardTrackerServiceTest
    {
        private readonly Mock<IWorkflowPCService> _workflowPCServiceMock;
        private readonly Mock<IFileService> _fileServiceMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly DashboardTrackerService _service;
        private object totalRecords;

        public DashboardTrackerServiceTest()
        {
            _workflowPCServiceMock = new Mock<IWorkflowPCService>();
            _fileServiceMock = new Mock<IFileService>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _service = new DashboardTrackerService(_workflowPCServiceMock.Object, _fileServiceMock.Object, _unitOfWorkMock.Object);
        }

        private Mock<IGenericRepository<DashboardTrackerConfig>> SetupConfigRepo(List<DashboardTrackerConfig> configs)
        {
            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            repoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(configs);
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);
            return repoMock;
        }

        [Fact]
        public async Task GetPortfolioCompanies_ReturnsEmpty_WhenNoCompanies()
        {
            // Arrange
            var filter = new PortfolioCompanyFilter();
            _workflowPCServiceMock.Setup(x => x.GetPortfolioCompanies(filter)).ReturnsAsync((WorkflowPCModel)null);

            // Act
            var result = await _service.GetPortfolioCompanies(filter);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task SaveDashboardTrackerConfigAsync_UpdatesExistingConfig()
        {
            // Arrange
            var dto = new DashboardTrackerConfigDto
            {
                ID = 10,
                FieldType = 1,
                DataType = 2,
                Name = "Test",
                FrequencyType = 1,
                StartPeriod = "2023",
                EndPeriod = "2024",
                IsPrefix = true,
                TimeSeriesDateFormat = "yyyy-MM",
                MapTo = 1,
                IsActive = true,
                IsDeleted = false,
                ModifiedBy = 2,
                ModifiedOn = System.DateTime.UtcNow
            };

            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);
            repoMock.Setup(x => x.Update(It.IsAny<DashboardTrackerConfig>())).Verifiable();
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.SaveDashboardTrackerConfigAsync(dto);

            // Assert
            repoMock.Verify(x => x.Update(It.IsAny<DashboardTrackerConfig>()), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
            Assert.True(result > 0);
        }

        [Fact]
        public async Task GetDashboardColumnsAsync_ReturnsEmpty_WhenNoConfigs()
        {
            // Arrange

            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            repoMock.Setup(x => x.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(new List<DashboardTrackerConfig>());
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);

            // Act
            var result = await _service.GetDashboardColumnsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task SaveTrackerDropdownValuesAsync_ReturnsFalse_WhenDtoIsNull()
        {
            // Act
            var result = await _service.SaveTrackerDropdownValuesAsync(null);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SaveTrackerDropdownValuesAsync_ReturnsFalse_WhenDropdownValuesEmpty()
        {
            // Arrange
            var dto = new TrackerDropdownValueDto { DropdownValues = new List<string>() };

            // Act
            var result = await _service.SaveTrackerDropdownValuesAsync(dto);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SaveDashboardTrackerConfigAsync_InsertsNewConfig()
        {
            // Arrange
            var dto = new DashboardTrackerConfigDto
            {
                FieldType = 1,
                DataType = 2,
                Name = "Test",
                FrequencyType = 1,
                StartPeriod = "2023",
                EndPeriod = "2024",
                IsPrefix = true,
                TimeSeriesDateFormat = "yyyy-MM",
                MapTo = 1,
                IsActive = true,
                IsDeleted = false,
                CreatedBy = 1,
                CreatedOn = DateTime.UtcNow
            };

            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);
            repoMock.Setup(x => x.Insert(It.IsAny<DashboardTrackerConfig>())).Verifiable();
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.SaveDashboardTrackerConfigAsync(dto);

            // Assert
            repoMock.Verify(x => x.Insert(It.IsAny<DashboardTrackerConfig>()), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
            Assert.True(result >= 0);
        }

        [Fact]
        public async Task GetDashboardColumnsAsync_ReturnsColumns_WhenConfigsExist()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig { ID = 1, Name = "Col1", IsActive = true, IsDeleted = false }
            };
            SetupConfigRepo(configs);

            // Act
            var result = await _service.GetDashboardColumnsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("Col1", result[0].Name);
        }
        
        [Fact]
        public async Task GetAllTrackerConfigsAsync_ReturnsConfigsWithDropdowns()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig { ID = 1, DataType = 4, Name = "Dropdown", IsDeleted = false }
            };
            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            repoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(configs);
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);

            var dropdownRepoMock = new Mock<IGenericRepository<TrackerDropdownValue>>();
            dropdownRepoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<TrackerDropdownValue, bool>>>()))
                .ReturnsAsync(new List<TrackerDropdownValue>
                {
                    new TrackerDropdownValue { TackerFieldConfigId = 1, DropdownValue = "A" }
                });
            _unitOfWorkMock.SetupGet(x => x.TrackerDropdownValueRepository).Returns(dropdownRepoMock.Object);

            // Act
            var result = await _service.GetAllTrackerConfigsAsync();

            // Assert
            Assert.Single(result);
            Assert.Equal("Dropdown", result[0].Name);
            Assert.Contains("A", result[0].DropdownList);
        }

        [Fact]
        public async Task GetAllTrackerConfigsAsync_ReturnsEmpty_WhenNoConfigs()
        {
            // Arrange
            var repoMock = new Mock<IGenericRepository<DashboardTrackerConfig>>();
            repoMock.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DashboardTrackerConfig, bool>>>()))
                .ReturnsAsync(new List<DashboardTrackerConfig>());
            _unitOfWorkMock.SetupGet(x => x.DashboardTrackerConfigRepository).Returns(repoMock.Object);

            // Act
            var result = await _service.GetAllTrackerConfigsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

    }
}
