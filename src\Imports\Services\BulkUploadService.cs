using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contract.Common;
using ClosedXML.Excel;
using Contract.BulkUpload;
using Contract.Funds;
using Contract.KPI;
using Contract.Utility;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.UnitOfWork;
using Imports.Kpi;
using Imports.Models;
using Microsoft.Extensions.Logging;
using Shared;
using Utility.Helpers;
using Utility.Services;
using Imports.Helpers;
using System.Data;
using System;
using Contract.CapTable;
using Contract.PortfolioCompany;
using Contract.Account;
using DocumentFormat.OpenXml.Spreadsheet;

namespace Imports.Services
{
    public class BulkUploadService(ILogger<BulkUploadService> logger, IUnitOfWork unitOfWork, IDapperGenericRepository dapperGenericRepository) : IBulkUpload
    {
        private readonly ILogger<BulkUploadService> _logger = logger;
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private readonly IDapperGenericRepository _dapperGenericRepository = dapperGenericRepository;
        /// <summary>
        /// Asynchronously processes the KPI data from a bulk upload.
        /// </summary>
        /// <param name="bulkUploadDataModel">The data model containing the bulk upload information.</param>
        /// <returns>A task that represents the asynchronous operation and contains a list of status objects.</returns>
        public async Task<List<Status>> ProcessKpi(BulkUploadDataModel bulkUploadDataModel)
        {
            List<Status> statuses = [];
            List<ExcelCodeStatus> excelCodeStatuses = [];
            try
            {
                using XLWorkbook xLWorkbook = new(bulkUploadDataModel.Path);
                xLWorkbook.CalculateMode = XLCalculateMode.Auto;
                string sheetName = await GetPageConfigName((int)bulkUploadDataModel.ModuleID);
                if (xLWorkbook.Worksheets.FirstOrDefault(x => x.Worksheet.Name == sheetName) == null)
                {
                    _logger.LogInformation($"@@@Sheet is empty or invalid sheet found in the file.");
                    return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.SheetEmpty);
                }
                else
                {
                    if (bulkUploadDataModel.ModuleName == Constants.MonthlyReportSheet)
                    {
                        return await ProcessMonthlyReportExcelSheet(bulkUploadDataModel, statuses, excelCodeStatuses, xLWorkbook, sheetName);
                    }
                    else
                    {
                        return await ProcessExcelSheet(bulkUploadDataModel, statuses, excelCodeStatuses, xLWorkbook, sheetName);
                    }

                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"@@@Processing file {ex.Message}.");
                return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.ExcelError);
            }
        }
        public async Task<List<Status>> ProcessFundKpi(BulkUploadDataModel bulkUploadDataModel)
        {
            List<Status> statuses = [];
            List<ExcelCodeStatus> excelCodeStatuses = [];
            List<PcKpiValueModel> pcKpiValueModels = [];
            List<FundConfigModel> sheetList = await GetFundPageConfigSheetName();
            try
            {
                using XLWorkbook xLWorkbook = new(bulkUploadDataModel.Path);
                xLWorkbook.CalculateMode = XLCalculateMode.Auto;
                var permissions = await _dapperGenericRepository.Query<SubFeatureAccessPermissionsModel>(SqlConstants.ProcGetFundSubFeaturePermissions, new { UserId = bulkUploadDataModel.UserID, FundId = bulkUploadDataModel.FundId, FeatureId = (int)Features.Fund });
                sheetList.RemoveAll(sheet => !permissions.Any(permission => permission.ModuleId == sheet.ModuleId && permission.CanImport));
                foreach (var worksheet in xLWorkbook.Worksheets)
                {
                    excelCodeStatuses = [];
                    if (sheetList.Any(x => x.AliasName == worksheet.Name))
                    {
                        bulkUploadDataModel.ModuleID = sheetList.FirstOrDefault(x => x.AliasName == worksheet.Name).ModuleId;
                        var kpivalues = await ProcessFundExcelSheet(bulkUploadDataModel, statuses, excelCodeStatuses, xLWorkbook, worksheet.Name);
                        if (kpivalues.Count > 0)
                        {
                            pcKpiValueModels.AddRange(kpivalues);
                        }
                    }
                    if (excelCodeStatuses.Any())
                    {
                        BulkUploadHelper.AddFileMessage(statuses, excelCodeStatuses, worksheet.Name, Constants.error, MessageConstants.FileMessageError);
                    }
                }
                return await ProcessFundDataValidation(bulkUploadDataModel, statuses, excelCodeStatuses, string.Empty, pcKpiValueModels);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"@@@Processing file {ex.Message}.");
                return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.ExcelError);
            }
        }
        /// <summary>
        /// Processes the financial KPIs for bulk upload.
        /// </summary>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="moduleTypes">The list of module types.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of statuses.</returns>
        public async Task<List<Status>> ProcessFinancialKpis(BulkUploadDataModel bulkUploadDataModel, List<KpiModuleType> moduleTypes)
        {
            List<Status> statuses = [];
            List<ExcelCodeStatus> excelCodeStatuses = [];
            try
            {
                using XLWorkbook xLWorkbook = new(bulkUploadDataModel.Path);
                xLWorkbook.CalculateMode = XLCalculateMode.Manual;
                string sheetName = await GetPageConfigName((int)bulkUploadDataModel.ModuleID);
                var sheetNames = await GetFinancialKpisSheetName(moduleTypes);
                if (!xLWorkbook.Worksheets.Any(x => sheetNames.Contains(x.Worksheet.Name)))
                {
                    _logger.LogInformation($"@@@Sheet is empty or invalid sheet found in the file.");
                    return BulkUploadHelper.SetFinancialSheetValidation(statuses, Constants.error, MessageConstants.SheetEmpty, sheetName);
                }
                else if (xLWorkbook.Worksheets.FirstOrDefault(x => x.Worksheet.Name == sheetName) != null)
                {
                    return await ProcessExcelSheet(bulkUploadDataModel, statuses, excelCodeStatuses, xLWorkbook, sheetName);
                }
                else
                {
                    return [];
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"@@@Processing file {ex.Message}.");
                return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.ExcelError);
            }
        }
        /// <summary>
        /// Retrieves the sheet names for the given financial KPI module types.
        /// </summary>
        /// <param name="moduleTypes">The list of financial KPI module types.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of sheet names.</returns>
        public async Task<List<string>> GetFinancialKpisSheetName(List<KpiModuleType> moduleTypes)
        {
            List<string> sheetNames = [];
            foreach (var moduleType in moduleTypes)
            {
                string sheetName = await GetPageConfigName((int)moduleType);
                sheetNames.Add(sheetName);
            }
            return sheetNames;
        }
        /// <summary>
        /// Asynchronously processes the CapTableKpi using the provided bulk upload data model.
        /// </summary>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of <see cref="Status"/>.</returns>
        public async Task<List<Status>> ProcessCapTableKpi(BulkUploadDataModel bulkUploadDataModel, bool isOtherCapTable = false)
        {
            List<Status> statuses = [];
            int subPageId = isOtherCapTable ? (int)PageConfigurationSubFeature.OtherCapTable : (int)PageConfigurationSubFeature.CapTable;
            List<ExcelCodeStatus> excelCodeStatuses = [];
            try
            {
                using XLWorkbook xLWorkbook = new(bulkUploadDataModel.Path);
                xLWorkbook.CalculateMode = XLCalculateMode.Manual;
                List<CapTemplateModule> pageConfigActiveSheets = GetActiveCapTableFieldNames(subPageId);
                var featureId = _unitOfWork.FeatureRepository.GetFirstOrDefault(x => x.Feature == Constants.PortfolioCompany).FeatureId;
                var permissions = await _dapperGenericRepository.Query<SubFeatureAccessPermissionsModel>(SqlConstants.ProcGetPcSubFeaturePermissions, new { UserId = bulkUploadDataModel.UserID, CompanyId = bulkUploadDataModel.PortfolioCompanyID, FeatureId = featureId });
                pageConfigActiveSheets.RemoveAll(sheet => !permissions.Any(permission => permission.ModuleId == sheet.ModuleId && permission.CanImport));
                var workSheets = xLWorkbook.Worksheets.Where(s => pageConfigActiveSheets.Select(x => x.AliasName?[..Math.Min(x.AliasName.Length, 24)]).Contains(s.Name)).ToList();
                if (workSheets?.Count == 0)
                {
                    _logger.LogInformation($"@@@Sheet is empty or invalid sheet found in the file.");
                    return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.SheetEmpty);
                }
                else
                {
                    return await ProcessCapTableExcelSheets(bulkUploadDataModel, statuses, excelCodeStatuses, pageConfigActiveSheets, workSheets);
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"@@@Processing file {ex.Message}.");
                return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.ExcelError);
            }
        }
        /// <summary>
        /// Retrieves the active CapTable field names along with their corresponding module information.
        /// </summary>
        /// <returns>A list of CapTemplateModule objects representing the active CapTable field names.</returns>
        public List<CapTemplateModule> GetActiveCapTableFieldNames(int subPageId)
        {
            return (from field in _unitOfWork.SubPageFieldsRepository.GetActiveRecords(x => x.SubPageID == subPageId)
                    join module in _unitOfWork.M_KpiModulesRepository.GetActiveRecords() on field.Name equals module.Name
                    select new CapTemplateModule()
                    {
                        Name = field.Name,
                        ModuleId = module.ModuleID,
                        AliasName = field.AliasName,
                        FieldId = field.FieldID,
                        Order = field.SequenceNo
                    }).ToList();
        }
        /// <summary>
        /// The list of Excel worksheets to be processed.
        /// </summary>
        /// <param name="workSheets">The list of Excel worksheets.</param>
        private async Task<List<Status>> ProcessCapTableExcelSheets(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, List<CapTemplateModule> pageConfigActiveSheets, List<IXLWorksheet> workSheets)
        {
            List<PcKpiValueModel> pcKpiValueModels = [];
            List<PcCapTableUploadModel> pcCapTableUploadModels = [];
            foreach (var (workSheet, activeSheet, moduleId) in from IXLWorksheet workSheet in workSheets
                                                               let activeSheet = pageConfigActiveSheets.FirstOrDefault(x => x.AliasName?[..Math.Min(x.AliasName.Length, 24)] == workSheet.Name)
                                                               let moduleId = activeSheet?.ModuleId ?? 0
                                                               select (workSheet, activeSheet, moduleId))
            {
                excelCodeStatuses = [];
                List<MSubSectionFields> subSectionFields = await GetPageConfigKpiExcelHeaders(moduleId);
                List<FoFKpiModel> kpiModels = await GetKpiListByModuleId(bulkUploadDataModel.PortfolioCompanyID, moduleId);
                var usedCapTableColumns = workSheet.Row(3).Cells().Where(x => !string.IsNullOrEmpty(x.GetValue<string>()))?.Select(x => x.Address.ColumnNumber).ToList();
                List<BulkUploadHeaderModel> bulkUploadHeaders = BulkUploadHelper.GetCapTableHeaders(excelCodeStatuses, workSheet, usedCapTableColumns.Count == 0 ? 2 : usedCapTableColumns.Last());
                List<BulkUploadHeaderModel> bulkUploadPeriodHeader = CapTableHelper.GetCapTablePeriodHeader(excelCodeStatuses, workSheet);
                if (!workSheet.CellsUsed().Any())
                {
                    return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.NoData);
                }
                CapTableHelper.SetCommonValidation(statuses, excelCodeStatuses, workSheet, subSectionFields, kpiModels, usedCapTableColumns, bulkUploadHeaders, bulkUploadPeriodHeader);
                CapTableHelper.ProcessExcelCellValues(bulkUploadDataModel, statuses, excelCodeStatuses, pcKpiValueModels, pcCapTableUploadModels, workSheet, moduleId, kpiModels, usedCapTableColumns, bulkUploadHeaders, bulkUploadPeriodHeader);
            }
            return await SetReturnResult(bulkUploadDataModel, statuses, excelCodeStatuses, pcKpiValueModels, pcCapTableUploadModels);
        }

        /// <summary>
        /// Sets the return result based on the provided parameters.
        /// </summary>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="statuses">The list of statuses.</param>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="pcKpiValueModels">The list of PC KPI value models.</param>
        /// <param name="pcCapTableUploadModels">The list of PC Cap Table upload models.</param>
        /// <returns>The list of statuses.</returns>
        private async Task<List<Status>> SetReturnResult(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, List<PcKpiValueModel> pcKpiValueModels, List<PcCapTableUploadModel> pcCapTableUploadModels)
        {
            if (statuses.Count == 0)
            {
                return await ProcessCapTableDataValidation(bulkUploadDataModel, statuses, excelCodeStatuses, string.Empty, pcKpiValueModels, pcCapTableUploadModels);
            }
            else
            {
                return statuses;
            }
        }

        /// <summary>
        /// Processes the Excel sheet with the specified sheet name.
        /// </summary>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="statuses">The list of statuses.</param>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="xLWorkbook">The XLWorkbook object.</param>
        /// <param name="sheetName">The name of the sheet to process.</param>
        /// <returns>The list of statuses after processing the Excel sheet.</returns>
        private async Task<List<Status>> ProcessExcelSheet(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, XLWorkbook xLWorkbook, string sheetName)
        {
            IXLWorksheet worksheet = BulkUploadHelper.ReadFileFromPath(xLWorkbook, sheetName);
            List<M_ValueTypes> valueTypes = await GetValueTypes();
            List<MSubSectionFields> subSectionFields = await GetPageConfigKpiExcelHeaders((int)bulkUploadDataModel.ModuleID);
            List<FoFKpiModel> kpiModels = await GetKpiListByModuleId(bulkUploadDataModel.PortfolioCompanyID, (int)bulkUploadDataModel.ModuleID);
            List<BulkUploadHeaderModel> bulkUploadHeaders = BulkUploadHelper.GetHeaders(excelCodeStatuses, worksheet, valueTypes, subSectionFields, bulkUploadDataModel.ModuleID);
            BulkUploadHelper.SetHeaderValidation(excelCodeStatuses, worksheet, bulkUploadHeaders, subSectionFields, bulkUploadDataModel.ModuleID);
            if (!worksheet.CellsUsed().Any())
            {
                if (bulkUploadDataModel.IsFinancial)
                {
                    return BulkUploadHelper.SetFinancialSheetValidation(statuses, Constants.error, MessageConstants.NoData, sheetName);
                }
                return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.NoData);
            }
            BulkUploadHelper.MandatoryColumnValidation(excelCodeStatuses, worksheet, kpiModels);
            BulkUploadHelper.UnmatchedColumnValidation(excelCodeStatuses, worksheet, bulkUploadHeaders);
            var lastRow = worksheet.LastRowUsed().RowNumber();
            var rowUsed = worksheet.RowsUsed().Where(x => x.RowNumber() > 1 && x.RowNumber() <= lastRow).ToList();
            var usedValues = GetFieldValueList(worksheet, rowUsed, excelCodeStatuses);
            BulkUploadHelper.CheckExtraRowsAnyExtraValues(usedValues, excelCodeStatuses);
            var kpiDataList = BulkUploadHelper.GetKpiDataList(kpiModels, usedValues, bulkUploadHeaders, bulkUploadDataModel.PortfolioCompanyID, (int)bulkUploadDataModel.ModuleID);
            List<PcKpiValueModel> pcKpiValueModels = BulkUploadHelper.ProcessKpiRecordsByRow(excelCodeStatuses, kpiDataList);
            return await ProcessDataValidation(bulkUploadDataModel, statuses, excelCodeStatuses, sheetName, pcKpiValueModels);
        }
        private async Task<List<PcKpiValueModel>> ProcessFundExcelSheet(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, XLWorkbook xLWorkbook, string sheetName)
        {
            IXLWorksheet worksheet = BulkUploadHelper.ReadFileFromPath(xLWorkbook, sheetName);
            List<M_ValueTypes> valueTypes = await GetValueTypes();
            List<MSubSectionFields> subSectionFields = await GetFundPageConfigKpiExcelHeaders((int)bulkUploadDataModel.ModuleID);
            List<FoFKpiModel> kpiModels = await GetFundKpiListByModuleId((int)bulkUploadDataModel.FundId, (int)bulkUploadDataModel.ModuleID);
            List<BulkUploadHeaderModel> bulkUploadHeaders = BulkUploadHelper.GetHeaders(excelCodeStatuses, worksheet, valueTypes, subSectionFields, bulkUploadDataModel.ModuleID);
            BulkUploadHelper.SetHeaderValidation(excelCodeStatuses, worksheet, bulkUploadHeaders, subSectionFields, bulkUploadDataModel.ModuleID);
            if (!worksheet.CellsUsed().Any())
            {
                BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.NoData);
            }
            BulkUploadHelper.MandatoryColumnValidation(excelCodeStatuses, worksheet, kpiModels);
            BulkUploadHelper.UnmatchedColumnValidation(excelCodeStatuses, worksheet, bulkUploadHeaders);
            var lastRow = worksheet.LastRowUsed().RowNumber();
            var rowUsed = worksheet.RowsUsed().Where(x => x.RowNumber() > 1 && x.RowNumber() <= lastRow).ToList();
            var usedValues = GetFieldValueList(worksheet, rowUsed, excelCodeStatuses);
            BulkUploadHelper.CheckExtraRowsAnyExtraValues(usedValues, excelCodeStatuses);
            var kpiDataList = BulkUploadHelper.GetKpiDataList(kpiModels, usedValues, bulkUploadHeaders, bulkUploadDataModel.PortfolioCompanyID, (int)bulkUploadDataModel.ModuleID);
            return BulkUploadHelper.ProcessKpiRecordsByRow(excelCodeStatuses, kpiDataList);
        }
        /// <summary>
        /// Processes the data validation for bulk upload.
        /// </summary>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="statuses">The list of statuses.</param>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="sheetName">The name of the sheet.</param>
        /// <param name="pcKpiValueModels">The list of PC KPI value models.</param>
        /// <returns>The list of statuses after processing the data validation.</returns>
        private async Task<List<Status>> ProcessDataValidation(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, string sheetName, List<PcKpiValueModel> pcKpiValueModels)
        {
            if (excelCodeStatuses.Count == 0)
            {
                if (pcKpiValueModels.Count == 0)
                {
                    _logger.LogInformation($"@@@File is empty or invalid data found in the file.");
                    if (bulkUploadDataModel.IsFinancial)
                    {
                        return BulkUploadHelper.SetFinancialSheetValidation(statuses, Constants.error, MessageConstants.NoData, sheetName);
                    }
                    return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.NoData);
                }
                else
                {
                    _logger.LogInformation("Start:ProcessExcelDataIntoDb" + (int)bulkUploadDataModel.ModuleID + DateTime.Now);
                    return await ProcessExcelDataIntoDb(bulkUploadDataModel, statuses, excelCodeStatuses, sheetName, pcKpiValueModels);
                }
            }
            else
            {
                _logger.LogError($"@@@Error occured while processing the file");
                BulkUploadHelper.AddFileMessage(statuses, excelCodeStatuses, sheetName, Constants.error, MessageConstants.FileMessageError);
            }
            return statuses;
        }
        private async Task<List<Status>> ProcessFundDataValidation(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, string sheetName, List<PcKpiValueModel> pcKpiValueModels)
        {
            if (statuses.Count == 0)
            {
                if (pcKpiValueModels.Count == 0)
                {
                    _logger.LogInformation($"@@@File is empty or invalid data found in the file.");
                    return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.NoData);
                }
                else
                {
                    _logger.LogInformation("Start:ProcessExcelDataIntoDb" + (int)bulkUploadDataModel.ModuleID + DateTime.Now);
                    return await ProcessFundKpisExcelDataIntoDb(bulkUploadDataModel, statuses, excelCodeStatuses, sheetName, pcKpiValueModels);
                }
            }
            else
            {
                _logger.LogError($"@@@Error occured while processing the file");
                return statuses;
            }
        }
        /// <summary>
        /// The list of PcCapTableUploadModel objects representing the cap table data to be processed.
        /// </summary>
        /// <param name="pcCapTableUploadModels">The list of PcCapTableUploadModel objects.</param>
        private async Task<List<Status>> ProcessCapTableDataValidation(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, string sheetName, List<PcKpiValueModel> pcKpiValueModels, List<PcCapTableUploadModel> pcCapTableUploadModels)
        {
            if (excelCodeStatuses.Count == 0)
            {
                if (pcKpiValueModels.Count == 0)
                {
                    _logger.LogInformation($"@@@File is empty or invalid data found in the file.");
                    return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.NoData);
                }
                else
                {
                    await CreatePeriodStageTable(bulkUploadDataModel, pcCapTableUploadModels);
                    return await ProcessExcelCapTableDataIntoDb(bulkUploadDataModel, statuses, excelCodeStatuses, sheetName, pcKpiValueModels);
                }
            }
            else
            {
                _logger.LogError($"@@@Error occured while processing the file");
                BulkUploadHelper.AddFileMessage(statuses, excelCodeStatuses, sheetName, Constants.error, MessageConstants.FileMessageError);
            }
            return statuses;
        }
        /// <summary>
        /// Creates a period stage table for bulk upload.
        /// </summary>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="pcCapTableUploadModels">The list of PC cap table upload models.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private static async Task CreatePeriodStageTable(BulkUploadDataModel bulkUploadDataModel, List<PcCapTableUploadModel> pcCapTableUploadModels)
        {
            string tableStagingName = BulkUploadHelper.CreateTableName(Constants.CapTablePeriodStaging, bulkUploadDataModel.UserID);
            string query = CommonQuery<PcCapTableUploadModel>.CreateTable(tableStagingName);
            var tableData = pcCapTableUploadModels.ToDataTable(tableStagingName);
            bulkUploadDataModel.CapTableStagingName = tableStagingName;
            await ImportHelper.ImportCreateTableQueryAtRuntime(tableStagingName, query, bulkUploadDataModel.Connection);
            await ImportHelper.InsertDataQueryUsingSqlBulkCopy(tableStagingName, tableData, bulkUploadDataModel.Connection);
        }
        /// <summary>
        /// Processes the Excel CapTable data into the database.
        /// </summary>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="statuses">The list of statuses.</param>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="sheetName">The name of the sheet.</param>
        /// <param name="pcKpiValueModels">The list of PC KPI value models.</param>
        /// <returns>The list of statuses.</returns>
        private async Task<List<Status>> ProcessExcelCapTableDataIntoDb(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, string sheetName, List<PcKpiValueModel> pcKpiValueModels)
        {
            try
            {
                await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.CapTableDataStaging, SqlConstants.QueryByCapTableKpiDetails, true);
                BulkUploadHelper.AddFileMessage(statuses, excelCodeStatuses, sheetName, Constants.Ok, MessageConstants.FileSuccess);
                _logger.LogInformation($"@@@Success:{MessageConstants.FileSuccess}");
                return statuses;
            }
            catch (Exception ex)
            {
                _logger.LogError($"@@@Error:{ex.Message}");
                return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.FileError);
            }
        }


        /// <summary>
        /// The list of PcKpiValueModel objects containing the data to be inserted into the database.
        /// </summary>
        /// <param name="pcKpiValueModels">The list of PcKpiValueModel objects.</param>
        private async Task<List<Status>> ProcessExcelDataIntoDb(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, string sheetName, List<PcKpiValueModel> pcKpiValueModels)
        {
            try
            {
                switch (bulkUploadDataModel.ModuleID)
                {
                    case (int)KpiModuleType.Investment:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.InvestmentKpiStaging, SqlConstants.QueryByInvestmentKpiDetails);
                        break;
                    case (int)KpiModuleType.TradingRecords:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.TradingKpiStaging, SqlConstants.QueryByMasterKpiDetails);
                        break;
                    case (int)KpiModuleType.Impact:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.ImpactKpiStaging, SqlConstants.QueryByImpactKpiDetails);
                        break;
                    case (int)KpiModuleType.ProfitAndLoss:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.ProfitAndLossStaging, SqlConstants.QueryByFinancialKpiDetails);
                        break;
                    case (int)KpiModuleType.BalanceSheet:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.BalanceSheetStaging, SqlConstants.QueryByFinancialKpiDetails);
                        break;
                    case (int)KpiModuleType.CashFlow:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.CashFlowStaging, SqlConstants.QueryByFinancialKpiDetails);
                        break;
                    case (int)KpiModuleType.CreditKPI:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.CreditKpiStaging, SqlConstants.QueryByMasterKpiDetails);
                        break;
                    case (int)KpiModuleType.Company:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.CompanyKpiStaging, SqlConstants.QueryByCompanyKpiDetails);
                        break;
                    case (int)KpiModuleType.Operational:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.OperationalKpiStaging, SqlConstants.QueryByOperationalKpiDetails);
                        break;
                    case (int)KpiModuleType.MonthlyReport:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.MonthlyReportStaging, SqlConstants.QueryByMonthlyReportDetails);
                        break;
                    case (int)KpiModuleType.CustomTable1 or (int)KpiModuleType.CustomTable2 or (int)KpiModuleType.CustomTable3 or (int)KpiModuleType.CustomTable4:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.CustomKpiStaging, SqlConstants.QueryByMasterKpiDetails);
                        break;
                    case (int)KpiModuleType.OtherKPI1 or (int)KpiModuleType.OtherKPI2 or (int)KpiModuleType.OtherKPI3 or (int)KpiModuleType.OtherKPI4 or (int)KpiModuleType.OtherKPI5
                    or (int)KpiModuleType.OtherKPI6 or (int)KpiModuleType.OtherKPI7 or (int)KpiModuleType.OtherKPI8 or (int)KpiModuleType.OtherKPI9 or (int)KpiModuleType.OtherKPI10:
                        await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.OtherKpiStaging, SqlConstants.QueryByMasterKpiDetails);
                        break;
                }
                BulkUploadHelper.AddFileMessage(statuses, excelCodeStatuses, sheetName, Constants.Ok, MessageConstants.FileSuccess);
                _logger.LogInformation($"@@@Success:{MessageConstants.FileSuccess}");
                return statuses;
            }
            catch (Exception ex)
            {
                _logger.LogError($"@@@Error:{ex.Message}");
                return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.FileError);
            }
        }
        private async Task<List<Status>> ProcessFundKpisExcelDataIntoDb(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, string sheetName, List<PcKpiValueModel> pcKpiValueModels)
        {
            try
            {
                await InsertDataIntoDb(bulkUploadDataModel, pcKpiValueModels, Constants.FundFinancialsStaging, SqlConstants.SPBulkUploadFundFinancials,false,true);
                BulkUploadHelper.AddFileMessage(statuses, excelCodeStatuses, sheetName, Constants.Ok, MessageConstants.FileSuccess);
                _logger.LogInformation($"@@@Success:{MessageConstants.FileSuccess}");
                return statuses;
            }
            catch (Exception ex)
            {
                _logger.LogError($"@@@Error:{ex.Message}");
                return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.FileError);
            }
        }

        /// <summary>
        /// Gets the page configuration name based on the module ID.
        /// </summary>
        /// <param name="moduleId">The module ID.</param>
        /// <returns>The page configuration name.</returns>
        public async Task<string> GetPageConfigName(int moduleId)
        {
            string pageConfigName = string.Empty;
            pageConfigName = _unitOfWork.M_KpiModulesRepository.GetFirstOrDefault(x => !x.IsDeleted && x.IsActive && x.ModuleID == moduleId)?.PageConfigFieldName;
            var subPageField = await _unitOfWork.SubPageFieldsRepository.FindFirstAsync(x => !x.IsDeleted && x.IsActive && (x.SubPageID == (int)PageConfigurationSubFeature.KeyPerformanceIndicator || x.SubPageID == (int)PageConfigurationSubFeature.OtherKPIs || x.SubPageID == (int)PageConfigurationSubFeature.CompanyFinancials || x.SubPageID == (int)PageConfigurationSubFeature.Reports) && x.Name == pageConfigName);
            if (subPageField?.AliasName?.Length > Constants.CellValueMaxLength)
                return subPageField?.AliasName[..Math.Min(subPageField.AliasName.Length, Constants.CellValueMaxLength)];
            return subPageField?.AliasName;
        }
        public async Task<List<FundConfigModel>> GetFundPageConfigSheetName()
        {
            var modules = await _unitOfWork.MFundKpiModulesRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive);
            var subPageFields = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive &&
                (x.SubPageID == (int)PageConfigurationSubFeature.FundKeyKpis || x.SubPageID == (int)PageConfigurationSubFeature.FundKpis));

            // Join modules and subPageFields based on PageConfigFieldName and Name
            var joinedList = (from module in modules
                              join subPage in subPageFields
                              on module.PageConfigFieldName equals subPage.Name
                              select new FundConfigModel()
                              {
                                  AliasName = subPage.AliasName?.Length > Constants.CellValueMaxLength
                                  ? subPage.AliasName[..Math.Min(subPage.AliasName.Length, Constants.CellValueMaxLength)]
                                  : subPage.AliasName,
                                  ModuleId = module.ModuleId,
                                  PageConfigName = module.PageConfigFieldName
                              }).ToList();
            return joinedList;
        }
        /// <summary>
        /// Inserts the provided data into the database.
        /// </summary>
        /// <typeparam name="T">The type of data to be inserted.</typeparam>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="dataList">The list of data to be inserted.</param>
        /// <param name="tableName">The name of the table to insert the data into.</param>
        /// <param name="spName">The name of the stored procedure to be executed.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task InsertDataIntoDb<T>(BulkUploadDataModel bulkUploadDataModel, List<T> dataList, string tableName, string spName, bool isCap = false,bool isFundKpi=false)
        {
            string tableStagingName = BulkUploadHelper.CreateTableName(tableName, bulkUploadDataModel.UserID);
            string query = CommonQuery<T>.CreateTable(tableStagingName);
            var tableData = dataList.ToDataTable(tableStagingName);
            await ProcessValueIntoActualTable(bulkUploadDataModel, spName, tableStagingName, query, tableData, isCap,isFundKpi);
        }

        /// <summary>
        /// Asynchronously processes the given bulk upload data into the actual table.
        /// </summary>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="spName">The stored procedure name.</param>
        /// <param name="tableName">The name of the table.</param>
        /// <param name="query">The query to create the table.</param>
        /// <param name="tableData">The data to be inserted into the table.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task ProcessValueIntoActualTable(BulkUploadDataModel bulkUploadDataModel, string spName, string tableName, string query, DataTable tableData, bool isCap = false,bool isFundKpi= false)
        {
            await ImportHelper.ImportCreateTableQueryAtRuntime(tableName, query, bulkUploadDataModel.Connection);
            await ImportHelper.InsertDataQueryUsingSqlBulkCopy(tableName, tableData, bulkUploadDataModel.Connection);
            if (bulkUploadDataModel.ModuleID != (int)KpiModuleType.MonthlyReport)
                await ImportHelper.ImportStagingTblToActualTblQueryWithDocuments(bulkUploadDataModel, spName, tableName, isCap,isFundKpi);
            else
                await ImportHelper.ImportStagingTblToActualTblQueryWithoutDocuments(bulkUploadDataModel, spName, tableName);
            await ImportHelper.ImportDropTableQueryAtRuntime(tableName, bulkUploadDataModel.Connection);
        }

        /// <summary>
        /// Retrieves a list of FoFKpiModel objects based on the specified company ID and module ID.
        /// </summary>
        /// <param name="companyId">The ID of the company.</param>
        /// <param name="moduleId">The ID of the module.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of FoFKpiModel objects.</returns>
        private async Task<List<FoFKpiModel>> GetKpiListByModuleId(int companyId, int moduleId)
        {
            List<FoFKpiModel> kpiModels = [];
            switch (moduleId)
            {
                case (int)KpiModuleType.Investment:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetFOFInvestmentKpiLineItems, new { companyIds = companyId });
                    break;
                case (int)KpiModuleType.TradingRecords:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetFOFMasterKpiLineItems, new { companyIds = companyId });
                    break;
                case (int)KpiModuleType.Impact:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetFOFImpactKpiLineItems, new { companyIds = companyId });
                    break;
                case (int)KpiModuleType.CapTable1:
                case (int)KpiModuleType.CapTable2:
                case (int)KpiModuleType.CapTable3:
                case (int)KpiModuleType.CapTable4:
                case (int)KpiModuleType.CapTable5:
                case (int)KpiModuleType.CapTable6:
                case (int)KpiModuleType.CapTable7:
                case (int)KpiModuleType.CapTable8:
                case (int)KpiModuleType.CapTable9:
                case (int)KpiModuleType.CapTable10:
                case (int)KpiModuleType.OtherCapTable1:
                case (int)KpiModuleType.OtherCapTable2:
                case (int)KpiModuleType.OtherCapTable3:
                case (int)KpiModuleType.OtherCapTable4:
                case (int)KpiModuleType.OtherCapTable5:
                case (int)KpiModuleType.OtherCapTable6:
                case (int)KpiModuleType.OtherCapTable7:
                case (int)KpiModuleType.OtherCapTable8:
                case (int)KpiModuleType.OtherCapTable9:
                case (int)KpiModuleType.OtherCapTable10:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetCapTableKpiLineItems, new { @companyId = companyId, @moduleId = moduleId });
                    break;
                case (int)KpiModuleType.ProfitAndLoss:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetProfitAndLossKpiLineItems, new { companyIds = companyId });
                    break;
                case (int)KpiModuleType.BalanceSheet:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetBalanceSheetKpiLineItems, new { companyIds = companyId });
                    break;
                case (int)KpiModuleType.CashFlow:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetCashFlowKpiLineItems, new { companyIds = companyId });
                    break;
                case (int)KpiModuleType.CreditKPI:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetFOFMasterKpiLineItem, new { companyIds = companyId });
                    break;
                case (int)KpiModuleType.Company:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetFOFCompanyKpiLineItem, new { companyIds = companyId });
                    break;
                case (int)KpiModuleType.Operational:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetFOFOperationalKpiLineItem, new { companyIds = companyId });
                    break;
                case (int)KpiModuleType.MonthlyReport:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetMonthlyReportKpis, new { companyIds = companyId });
                    break;
                case (int)KpiModuleType.CustomTable1 or (int)KpiModuleType.CustomTable2 or (int)KpiModuleType.CustomTable3 or (int)KpiModuleType.CustomTable4 or (int)KpiModuleType.OtherKPI1 or (int)KpiModuleType.OtherKPI2 or (int)KpiModuleType.OtherKPI10 or
                (int)KpiModuleType.OtherKPI3 or (int)KpiModuleType.OtherKPI4 or (int)KpiModuleType.OtherKPI5 or (int)KpiModuleType.OtherKPI6 or (int)KpiModuleType.OtherKPI7 or (int)KpiModuleType.OtherKPI8 or (int)KpiModuleType.OtherKPI9:
                    kpiModels = await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetMasterKpiLineItems, new { companyIds = companyId, moduleId });
                    break;
            }

            return kpiModels;
        }
        private async Task<List<FoFKpiModel>> GetFundKpiListByModuleId(int fundId, int moduleId)
        {
            return await _dapperGenericRepository.Query<FoFKpiModel>(SqlConstants.QueryGetFundKpiLineItems, new { fundId, moduleId });
        }

        /// <summary>
        /// Retrieves a list of KpiFieldValueModel objects based on the provided parameters.
        /// </summary>
        /// <param name="worksheet">The Excel worksheet.</param>
        /// <param name="usedRow">The list of used rows in the worksheet.</param>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <returns>A list of KpiFieldValueModel objects.</returns>
        public List<KpiFieldValueModel> GetFieldValueList(IXLWorksheet worksheet, List<IXLRow> usedRow, List<ExcelCodeStatus> excelCodeStatuses)
        {
            worksheet.RecalculateAllFormulas();
            return usedRow.SelectMany(x => x.CellsUsed().Where(x => x.Address.ColumnNumber > 2).Select(x => new KpiFieldValueModel()
            {
                Address = x.Address.ToString(),
                RowNumber = x.Address.RowNumber,
                ColumnNumber = x.Address.ColumnNumber,
                KpiValue = BulkUploadHelper.FormattingValue(x, excelCodeStatuses),
                DataType = x.DataType,
                KpiTextValue = x.GetRichText()?.Text,
                KpiId = worksheet.Cell(x.Address.RowNumber, 2).IsEmpty() ? 0 : worksheet.Cell(x.Address.RowNumber, 2).GetValue<int>(), // Get kpiId from column number 2
                CellFormat = x.GetFormattedString().Contains(Constants.KpiInfoPercent) ? Constants.KpiInfoPercent : x.Style.NumberFormat?.ToString()
            })).ToList();
        }

        /// <summary>
        /// NumberFormatMappings
        /// </summary>
        private static readonly Dictionary<int, string> NumberFormatMappings = new Dictionary<int, string>
        {
            { 0, "General" },
            { 1, "0" },
            { 2, "0.00" },
            { 3, "#,##0;[Red]-#,##0" },
            { 4, "#,##0.00" },
            { 9, "0%" },
            { 10, "0.00%" },
            { 11, "0.00E+00" },
            { 12, "# ?/?" },
            { 13, "# ??/??" },
            { 14, "mm-dd-yy" },
            { 15, "d-mmm-yy" },
            { 16, "d-mmm" },
            { 17, "mmm-yy" },
            { 18, "h:mm AM/PM" },
            { 19, "h:mm:ss AM/PM" },
            { 20, "h:mm" },
            { 21, "h:mm:ss" },
            { 22, "m/d/yy h:mm" },
            { 37, "#,##0 ;(#,##0)" },
            { 38, "#,##0 ;[Red](#,##0)" },
            { 39, "#,##0.00;(#,##0.00)" },
            { 40, "#,##0.00;[Red](#,##0.00)" },
            { 45, "mm:ss" },
            { 46, "[h]:mm:ss" },
            { 47, "mmss.0" },
            { 48, "##0.0E+0" },
            { 49, "@" }
        };
        /// <summary>
        /// GetNumberFormatString
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        private static string GetNumberFormatString(IXLCell cell)
        {
            if (!string.IsNullOrEmpty(cell.Style.NumberFormat.Format))
            {
                return cell.Style.NumberFormat.Format;
            }

            if (NumberFormatMappings.TryGetValue(cell.Style.NumberFormat.NumberFormatId, out var format))
            {
                return format;
            }

            return cell.Style.NumberFormat?.ToString();
        }
        public List<KpiFieldValueModel> GetFieldValueListMonthlyReport(IXLWorksheet worksheet, List<IXLRow> usedRow, List<ExcelCodeStatus> excelCodeStatuses, List<FoFKpiModel> kpiModels)
        {
            return usedRow.SelectMany(row => row.CellsUsed().Where(cell => cell.Address.ColumnNumber > 1).Select(cell =>
            {
                var kpiFieldValueModel = new KpiFieldValueModel
                {
                    Address = cell.Address.ToString(),
                    RowNumber = cell.Address.RowNumber,
                    ColumnNumber = cell.Address.ColumnNumber,
                    KpiValue = BulkUploadHelper.FormattingValue(cell, excelCodeStatuses),
                    DataType = cell.DataType,
                    KpiTextValue = cell.GetRichText()?.Text,
                    KpiId = worksheet.Cell(cell.Address.RowNumber, 1).IsEmpty() ? 0 : (int)kpiModels?[cell.Address.RowNumber - 2]?.KpiId,
                    CellFormat = GetNumberFormatString(cell),
                    IsMerged = cell.IsMerged()
                };

                // If the cell is merged, get the first and last column numbers of the merged range
                if (cell.IsMerged())
                {
                    var mergedRange = cell.MergedRange();
                    kpiFieldValueModel.MergedColRange = $"{mergedRange.FirstColumn().ColumnNumber()}-{mergedRange.LastColumn().ColumnNumber()}";
                }

                return kpiFieldValueModel;
            })).ToList();
        }


        /// <summary>
        /// Retrieves the list of MSubSectionFields based on the provided moduleId.
        /// </summary>
        /// <param name="moduleId">The ID of the module.</param>
        /// <returns>The list of MSubSectionFields.</returns>
        public async Task<List<MSubSectionFields>> GetPageConfigKpiExcelHeaders(int moduleId)
        {
            var pageConfigNameData = await _unitOfWork.M_KpiModulesRepository.FindFirstAsync(x => !x.IsDeleted && x.IsActive && x.ModuleID == moduleId);
            string pageConfigName = pageConfigNameData?.PageConfigFieldName;
            var fieldData = await _unitOfWork.SubPageFieldsRepository.FindFirstAsync(x => !x.IsDeleted && x.IsActive && (x.SubPageID == (int)PageConfigurationSubFeature.KeyPerformanceIndicator || x.SubPageID == (int)PageConfigurationSubFeature.OtherKPIs || x.SubPageID == (int)PageConfigurationSubFeature.CompanyFinancials || x.SubPageID == (int)PageConfigurationSubFeature.CapTable || x.SubPageID == (int)PageConfigurationSubFeature.OtherCapTable || x.SubPageID == (int)PageConfigurationSubFeature.Reports) && x.Name == pageConfigName);
            int? fieldId = fieldData?.FieldID;
            return await _unitOfWork.MSubSectionFieldsRepository.FindAllAsync(y => !y.IsDeleted && y.FieldID == fieldId);
        }
        public async Task<List<MSubSectionFields>> GetFundPageConfigKpiExcelHeaders(int moduleId)
        {
            var pageConfigNameData = await _unitOfWork.MFundKpiModulesRepository.FindFirstAsync(x => !x.IsDeleted && x.IsActive && x.ModuleId == moduleId);
            string pageConfigName = pageConfigNameData?.PageConfigFieldName;
            var fieldData = await _unitOfWork.SubPageFieldsRepository.FindFirstAsync(x => !x.IsDeleted && x.IsActive && (x.SubPageID == (int)PageConfigurationSubFeature.FundKeyKpis || x.SubPageID == (int)PageConfigurationSubFeature.FundKpis) && x.Name == pageConfigName);
            int? fieldId = fieldData?.FieldID;
            return await _unitOfWork.MSubSectionFieldsRepository.FindAllAsync(y => !y.IsDeleted && y.FieldID == fieldId);
        }
        /// <summary>
        /// Retrieves a list of M_ValueTypes asynchronously.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of M_ValueTypes.</returns>
        public async Task<List<M_ValueTypes>> GetValueTypes()
        {
            return await _unitOfWork.M_ValueTypesRepository.FindAllAsync(x => !x.IsDeleted);
        }
        /// <summary>
        /// ProcessMonthlyReportExcelSheet
        /// </summary>
        /// <param name="bulkUploadDataModel"></param>
        /// <param name="statuses"></param>
        /// <param name="excelCodeStatuses"></param>
        /// <param name="xLWorkbook"></param>
        /// <param name="sheetName"></param>
        /// <returns></returns>
        private async Task<List<Status>> ProcessMonthlyReportExcelSheet(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, XLWorkbook xLWorkbook, string sheetName)
        {
            IXLWorksheet worksheet = BulkUploadHelper.ReadFileFromPath(xLWorkbook, sheetName);
            List<M_ValueTypes> valueTypes = await GetValueTypes();
            List<MSubSectionFields> subSectionFields = await GetPageConfigKpiExcelHeaders((int)bulkUploadDataModel.ModuleID);
            List<FoFKpiModel> kpiModels = await GetKpiListByModuleId(bulkUploadDataModel.PortfolioCompanyID, (int)bulkUploadDataModel.ModuleID);
            BulkUploadHelper.SetMonthlyReportCompanyColor(worksheet, bulkUploadDataModel, excelCodeStatuses);
            List<BulkUploadHeaderModel> bulkUploadHeaders = BulkUploadHelper.GetHeaders(excelCodeStatuses, worksheet, valueTypes, subSectionFields, bulkUploadDataModel.ModuleID);
            BulkUploadHelper.SetMonthlyReportHeaderValidation(excelCodeStatuses, worksheet, bulkUploadHeaders, subSectionFields, bulkUploadDataModel.ModuleID);
            if (!worksheet.CellsUsed().Any())
            {
                return BulkUploadHelper.SetSheetValidation(statuses, Constants.error, MessageConstants.NoData);
            }
            BulkUploadHelper.MonthlyReportMandatoryColumnValidation(excelCodeStatuses, worksheet, kpiModels, bulkUploadHeaders);
            BulkUploadHelper.UnmatchedColumnValidation(excelCodeStatuses, worksheet, bulkUploadHeaders);
            var lastRow = worksheet.LastRowUsed().RowNumber();
            var rowUsed = worksheet.RowsUsed().Where(x => x.RowNumber() > 1 && x.RowNumber() <= lastRow).ToList();
            var usedValues = GetFieldValueListMonthlyReport(worksheet, rowUsed, excelCodeStatuses, kpiModels);
            BulkUploadHelper.CheckExtraRowsAnyExtraValues(usedValues, excelCodeStatuses);
            var kpiDataList = BulkUploadHelper.GetKpiDataList(kpiModels, usedValues, bulkUploadHeaders, bulkUploadDataModel.PortfolioCompanyID, (int)bulkUploadDataModel.ModuleID);
            List<PcKpiValueModel> pcKpiValueModels = BulkUploadHelper.ProcessKpiRecordsByRow(excelCodeStatuses, kpiDataList, true, bulkUploadDataModel.ColorColNo);
            return await ProcessDataValidation(bulkUploadDataModel, statuses, excelCodeStatuses, sheetName, pcKpiValueModels);
        }
    }
}