﻿using API.Middlewares;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Common.Middlewares
{
    public class RequestValidatorMiddlewareTests
    {
        private readonly Mock<RequestDelegate> _nextMock;
        private readonly Mock<ILogger<RequestValidatorMiddleware>> _loggerMock;
        private readonly RequestValidatorMiddleware _middleware;

        public RequestValidatorMiddlewareTests()
        {
            _nextMock = new Mock<RequestDelegate>();
            _loggerMock = new Mock<ILogger<RequestValidatorMiddleware>>();
            _middleware = new RequestValidatorMiddleware(_nextMock.Object, _loggerMock.Object);

        }

        [Fact]
        public async Task Invoke_ShouldReturnForbidden_WhenXMethodOverrideHeaderExists()
        {
            var context = new DefaultHttpContext();
            context.Request.Headers.Add("x-method-override", "PUT");
            context.Response.Body = new MemoryStream();

            await _middleware.Invoke(context);

            context.Response.Body.Seek(0, SeekOrigin.Begin);
            var reader = new StreamReader(context.Response.Body, Encoding.UTF8);
            var response = await reader.ReadToEndAsync();
            Assert.Equal("application/json;charset=utf-8", context.Response.ContentType);
            Assert.Equal(StatusCodes.Status500InternalServerError, context.Response.StatusCode);
        }

        [Fact]
        public async Task Invoke_ShouldReturnForbidden_WhenCommandInjectionExistsInBody()
        {
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes("echo 'Hello World'"));
            context.Response.Body = new MemoryStream();

            await _middleware.Invoke(context);

            context.Response.Body.Seek(0, SeekOrigin.Begin);
            var reader = new StreamReader(context.Response.Body, Encoding.UTF8);
            var response = await reader.ReadToEndAsync();
            Assert.Equal("application/json;charset=utf-8", context.Response.ContentType);
            Assert.Equal(StatusCodes.Status500InternalServerError, context.Response.StatusCode);
        }

        [Fact]
        public async Task Invoke_ShouldReturnForbidden_WhenCommandInjectionExistsInQuery()
        {
            var context = new DefaultHttpContext();
            context.Request.QueryString = new QueryString("?name=DROP TABLE users");
            context.Response.Body = new MemoryStream();

            await _middleware.Invoke(context);

            context.Response.Body.Seek(0, SeekOrigin.Begin);
            var reader = new StreamReader(context.Response.Body, Encoding.UTF8);
            var response = await reader.ReadToEndAsync();
            Assert.Equal("application/json;charset=utf-8", context.Response.ContentType);
            Assert.Equal(StatusCodes.Status500InternalServerError, context.Response.StatusCode);
        }

        [Fact]
        public async Task Invoke_ShouldReturnForbidden_WhenNonStandardHttpMethodExists()
        {
            var context = new DefaultHttpContext();
            context.Request.Method = "HEAD";
            context.Response.Body = new MemoryStream();

            await _middleware.Invoke(context);

            context.Response.Body.Seek(0, SeekOrigin.Begin);
            var reader = new StreamReader(context.Response.Body, Encoding.UTF8);
            var response = await reader.ReadToEndAsync();
            Assert.Equal("application/json;charset=utf-8", context.Response.ContentType);
            Assert.Equal(StatusCodes.Status500InternalServerError, context.Response.StatusCode);
        }

        [Fact]
        public async Task Invoke_ShouldCallNextDelegate_WhenRequestIsValid()
        {
            var context = new DefaultHttpContext();
            context.Request.Method = "GET";
            context.Response.Body = new MemoryStream();
            _nextMock.Setup(n => n(context)).Returns(Task.CompletedTask);

            await _middleware.Invoke(context);

            _nextMock.Verify(n => n(context), Times.Once);
        }
    }
}