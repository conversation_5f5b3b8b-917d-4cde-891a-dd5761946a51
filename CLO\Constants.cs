﻿using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CLO
{
    internal static class Constants
    {
        public const string DateDisplayFormat = "d MMMM yyyy";
        public const string DateDisplayFormat_Table = "MMM yyyy";
        public const string InputStringValidation_Regex = "[^\\w\\s,:\\-\\/().&$€]";
        public const string CLO_Module = "CLO_Module";
        public const string QueryoutputDateformat = "yyyy-MM-dd";
        public const string  CurrencySymbolMatchingpattern = @"\p{Sc}";

        #region SQL Queries
        public static readonly string GetDynamicTableRecordByCompanyIds = "ProcGetRecordByCompany";
        public const string DeleteCompanyRecords = "ProcDeletRecordByCompany";
        public const string DropTempTableProc = "ProcDropTempTable";
        public const string CreateTempTableProc = "ProcCreateTempTableByTable";
        public const string CreateTableDynamicWithColumnList = "ProcCreateTableDynamic";
        public const string UpdateTableFromTempTable = "ProcUpdateTableFromTempTable";
        public const string InsertDataFromTempTable = "ProcInsertDataFromTempTable";
        public const string DeleteFootnoteRecords = "ProcDeleteCloFootnoteTable";
        public static readonly string GetInvestmentCompanyAndCLOReportingRecords = "ProcGetInvestmentCompanyAndCLOReportingRecords";

        #endregion

    }
}
