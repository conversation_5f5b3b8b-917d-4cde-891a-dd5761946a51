﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Net;
using System.Threading.Tasks;
using Utility.Resource;

namespace API.Middlewares;
/// <summary>
/// GlobalExceptionHandler to catch uncaught exceptions
/// </summary>
public class GlobalExceptionHandler
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionHandler> _logger;

    public GlobalExceptionHandler(RequestDelegate next, ILogger<GlobalExceptionHandler> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task Invoke(HttpContext context)
    {
        try
        {
            var isUploadDocumentsEndpoint = context.Request.Path.Value?.Contains("upload-documents", StringComparison.OrdinalIgnoreCase) == true
       && (context.Request.Method.Equals("POST", StringComparison.OrdinalIgnoreCase)
           || context.Request.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase));

            if (IsTransferEncoding(context))
            {
                if (isUploadDocumentsEndpoint)
                {
                    _logger.LogWarning("UploadDocuments endpoint: Unsupported MediaType Header Transfer Encoding. Path: {Path}, Method: {Method}, Origin: {Origin}",
                        context.Request.Path, context.Request.Method, context.Request.Headers["Origin"].ToString());
                }
                await context.Response.WriteAsync(JsonConvert.SerializeObject(new { code = HttpStatusCode.UnsupportedMediaType, message = "UnSupported MediaType Header Transfer Encoding" }));
                return;
            }
            await _next.Invoke(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = context.Response;
        response.ContentType = "application/json";
        response.StatusCode = StatusCodes.Status500InternalServerError;
        _logger.LogError(exception, exception.Message);
        await response.WriteAsync(JsonConvert.SerializeObject(new { code = HttpStatusCode.InternalServerError, message = Messages.SomethingWentWrong }));
    }
    private bool IsTransferEncoding(HttpContext context)
    {
        if (context.Request.Headers.ContainsKey("Transfer-Encoding"))
        {
            var response = context.Response;
            response.ContentType = "application/json";
            response.StatusCode = StatusCodes.Status415UnsupportedMediaType;
            _logger.LogError("UnSupported Header Transfer Encoding");
            return true;
        }
        return false;

    }
}

