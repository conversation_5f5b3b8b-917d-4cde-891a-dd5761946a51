﻿using Azure.Core;
using CLO.CQRS.Commands;
using CLO.CQRS.Results;
using CLO.Helpers;
using CLO.Models;
using CLO.Services.interfaces;
using CLO.Shared.APIResponse;
using Contract.ConsolidatedReport;
using Contract.Utility;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models.CLO;
using DataAccessLayer.Models.CLO.Clo_Details;
using DataAccessLayer.Models.CLO.CloCommentries;
using DataAccessLayer.Models.CLO.InvestmentCompany;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.UnitOfWork;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Transactions;
using Utility.Resource;

namespace CLO.Services
{
    public class InvestmentCompanyService : IInvestmentCompanyService
    {
        private readonly ILogger<InvestmentCompanyService> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITableMetadataService _tableMetadataService;
        private readonly ICLOService _cloService;
        private readonly IDapperGenericRepository _dapperGenericRepository;


        public InvestmentCompanyService(ILogger<InvestmentCompanyService> logger, IInjectedParameters InjectedParameters, IDapperGenericRepository dapperGenericRepository, ITableMetadataService tableMetadataService, ICLOService cloService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _unitOfWork = InjectedParameters.UnitOfWork;
            _tableMetadataService = tableMetadataService;
            _cloService = cloService;
            _dapperGenericRepository = dapperGenericRepository;
        }

        public async Task<bool> IsInvestmentCompanyExist(InvestmentCompanyModel investmentCompanyModel)
        {
            var investmentCompany = await _unitOfWork.InvestmentCompanyDetailsRepository
                .GetFirstOrDefaultAsync(x => x.InvestmentCompanyID == investmentCompanyModel.Id);
            return investmentCompany != null;
        }
        public async Task<APIResponse> InsertInvestmentCompanyDetailRepository(InvestmentCompanyModel model)
        {
            try
            {
                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    var investmentCompany = model.ToSchema();
                    _unitOfWork.InvestmentCompanyDetailsRepository.Insert(investmentCompany);
                    await _unitOfWork.SaveAsync();
                    scope.Complete();

                    return new APIResponse { Status = true, Message = Messages.Add_InvestmentCompany_Success };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, ex);
                return new APIResponse { Status = false, Message = Messages.Save_InvestmentCompany_Failure };
            }
        }

        public async Task<APIResponse> UpdateInvestmentCompanyDetailRepository(InvestmentCompanyModel model)
        {
            try
            {
                var existingEntity = await _unitOfWork.InvestmentCompanyDetailsRepository.GetFirstOrDefaultAsync(x => x.InvestmentCompanyID == model.Id);

                if (existingEntity == null)
                {
                    return new APIResponse { Status = false, Message = Messages.Save_CLO_Failure };
                }

                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    model.ToSchema(existingEntity);
                    _unitOfWork.InvestmentCompanyDetailsRepository.Update(existingEntity);
                    await _unitOfWork.SaveAsync();
                    scope.Complete();

                    return new APIResponse { Status = true, Message = Messages.Update_InvestmentCompany_Success };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, ex);
                return new APIResponse { Status = false, Message = Messages.Save_CLO_Failure };
            }
        }

        public async Task<int> AddCommentaries(CommentaryModel commentaryModel)
        {
            var existingCommentary = GetExistingCommentary(commentaryModel);

            if (existingCommentary == null)
            {
                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    clo_commentries commentries = InsertCloCommentries(commentaryModel);
                    await _unitOfWork.SaveAsync();
                    scope.Complete();
                    return 1;
                }
            }
            else
            {
                return await UpdateCommentary(commentaryModel, existingCommentary);
            }
        }

        private clo_commentries GetExistingCommentary(CommentaryModel commentaryModel)
        {
            return _unitOfWork.CloCommentriesRepository.Get(x =>
                x.InvestmentCompanyId == commentaryModel.InvestmentCompanyId);
        }

        public async Task<int> UpdateCommentary(CommentaryModel model, clo_commentries existingCommentary)
        {
            if (model.Glicommentry != null)
            {
                existingCommentary.Glicommentry = model.Glicommentry;
            }
            if (model.MarketCommentry != null)
            {
                existingCommentary.MarketCommentry = model.MarketCommentry;
            }
            _unitOfWork.CloCommentriesRepository.Update(existingCommentary);
            await _unitOfWork.SaveAsync();
            return 1;
        }

        private clo_commentries InsertCloCommentries(CommentaryModel model)
        {
            var commentries = model.ToSchema();
            _unitOfWork.CloCommentriesRepository.Insert(commentries);
            return commentries;
        }

        public async Task<List<InvestmentCompanyModel>> GetInvestmentCompanies()
        {
            var investmentCompanies = await _unitOfWork.InvestmentCompanyDetailsRepository.GetAllAsyn();
            _logger.LogInformation("InvestmentCompanies retrieved successfully from business layer");
            return investmentCompanies
                .OrderByDescending(x => x.CreatedOn)
                .Select(i => i.ToModel())
                .ToList();
        }

        public async Task<InvestmentCompanyModel> GetInvestmentCompanyById(int id)
        {
            var investmentCompany = await _unitOfWork.InvestmentCompanyDetailsRepository.GetFirstOrDefaultAsync(x => x.InvestmentCompanyID == id);

            if (investmentCompany == null)
            {
                _logger.LogWarning($"InvestmentCompany with ID {id} not found.");
                return null;
            }

            _logger.LogInformation($"InvestmentCompany with ID {id} retrieved successfully.");
            return investmentCompany.ToModel();
        }

        public async Task<CommentaryModel> GetcommentriesById(int id)
        {
            var commentries = await _unitOfWork.CloCommentriesRepository.GetFirstOrDefaultAsync(x => x.InvestmentCompanyId == id);

            if (commentries == null)
            {
                _logger.LogWarning($"Commentary with ID {id} not found.");
                return null;
            }

            _logger.LogInformation($"Commentary with ID {id} retrieved successfully.");
            return commentries.ToModel();
        }

        public async Task<int> DeleteInvestmentCompany(int investmentCompanyId, string connectionString, string tableName = null)
        {
            var startTime = DateTime.Now;
            _logger.LogInformation($"DeleteInvestmentCompany started at {startTime}");
            var existingEntity = _unitOfWork.InvestmentCompanyDetailsRepository.Get(x => x.InvestmentCompanyID == investmentCompanyId);
            if (existingEntity == null)
            {
                _logger.LogWarning($"InvestmentCompany with ID {investmentCompanyId} not found before deletion.");
                return -1;
            }
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                _logger.LogInformation($"SQL connection opened at {DateTime.Now}");
                using (var scope = connection.BeginTransaction())
                {
                    try
                    {
                        DeleteCLOCommand request = new DeleteCLOCommand
                        {
                            CloUniqueID = investmentCompanyId.ToString(),
                            ConnectionString = connectionString,
                            TableName = tableName
                        };

                        List<string> tableMetadata = new List<string>();
                        List<string> footerMappingdata = new List<string>();
                        if (string.IsNullOrEmpty(request.TableName))
                        {
                            tableMetadata = GetAllTableNameFromMetadata();
                            footerMappingdata = _tableMetadataService.GetAllTableIdentifierFromMetadata();
                        }
                        else
                        {
                            string table = _tableMetadataService.GetTableMetadata(request.TableName).TableName;
                            tableMetadata.Add(table);
                            footerMappingdata.Add(request.TableName);
                        }
                        await DataDeleteHelper.DeleteCLOTablesData(request, connection, tableMetadata, scope, footerMappingdata, _tableMetadataService);
                        _logger.LogInformation($"DataDeleteHelper.DeleteCLOTablesData completed at {DateTime.Now}");
                        if (string.IsNullOrEmpty(request.TableName))
                        {
                            await DeleteAllClosForCompany(investmentCompanyId, connectionString);
                            _logger.LogInformation($"DeleteAllClosForCompany completed at {DateTime.Now}");

                            if (existingEntity != null)
                            {
                                _unitOfWork.InvestmentCompanyDetailsRepository.Delete(existingEntity);
                                await _unitOfWork.SaveAsync();
                                _logger.LogInformation($"InvestmentCompany with ID {investmentCompanyId} deleted successfully at {DateTime.Now}");
                                scope.Commit();
                                _logger.LogInformation($"Transaction committed at {DateTime.Now}");
                                return 1;
                            }
                        }
                        scope.Commit();
                        return 1;
                    }
                    catch (Exception ex)
                    {
                        scope.Rollback();
                        Console.WriteLine("Transaction rolled back. Error: " + ex.Message);
                        _logger.LogError($"Transaction rolled back at {DateTime.Now}. Error: {ex.Message}");
                        return -1;
                    }
                }
            }
            var endTime = DateTime.Now;
            _logger.LogInformation($"DeleteInvestmentCompany ended at {endTime}");
            _logger.LogInformation($"Total time taken: {endTime - startTime}");
            return -1;
        }

        private async Task DeleteAllClosForCompany(int investmentCompanyId, string connectionString)
        {
            var startTime = DateTime.Now;
            _logger.LogInformation($"DeleteAllClosForCompany started at {startTime}");
            var cloUniqueIDs = _unitOfWork.CloDetailsRepository
                                .GetMany(x => x.CompanyID == investmentCompanyId).Select(x => x.UniqueID).ToList();
            if (!cloUniqueIDs.Any())
            {
                _logger.LogWarning($"No CLOs found for InvestmentCompany with ID {investmentCompanyId}.");
            }

            foreach (var uniqueID in cloUniqueIDs)
            {
                DeleteCLOCommand request = new DeleteCLOCommand
                {
                    CloUniqueID = uniqueID,
                    ConnectionString = connectionString
                };

                await _cloService.DeleteClo(request);
                _logger.LogInformation($"CLO with UniqueID {uniqueID} deleted for InvestmentCompany with ID {investmentCompanyId} at {DateTime.Now}");
            }
            var endTime = DateTime.Now;
            _logger.LogInformation($"DeleteAllClosForCompany ended at {endTime}");
            _logger.LogInformation($"Total time taken: {endTime - startTime}");

        }

        public List<string> GetAllTableNameFromMetadata()
        {
            return _tableMetadataService.GetAllTableNameFromMetadata();
        }

        public async Task<int> SaveFootnote(TableFootnoteModel footnote)
        {
            if (!await IsFootnoteExist(footnote))
            {
                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    var footnoteSchema = footnote.ToSchema();
                    _unitOfWork.TableFootnoteSchemaRepository.Insert(footnoteSchema);
                    await _unitOfWork.SaveAsync();
                    scope.Complete();
                    return 1;
                }
            }
            else
            {
                return await UpdateFootnote(footnote);
            }
        }

        private async Task<bool> IsFootnoteExist(TableFootnoteModel footnote)
        {
            var existingFootnote = await _unitOfWork.TableFootnoteSchemaRepository
            .GetFirstOrDefaultAsync(x => x.FootnoteMapping == footnote.FootnoteMapping
                                    && x.FootnoteIdentifier == footnote.FootnoteIdentifier);
            return existingFootnote != null;
        }

        private async Task<int> UpdateFootnote(TableFootnoteModel footnote)
        {
            var existingFootnote = await _unitOfWork.TableFootnoteSchemaRepository
             .GetFirstOrDefaultAsync(x => x.FootnoteMapping == footnote.FootnoteMapping
                                     && x.FootnoteIdentifier == footnote.FootnoteIdentifier);

            if (existingFootnote != null)
            {
                footnote.ToSchema(existingFootnote);
                _unitOfWork.TableFootnoteSchemaRepository.Update(existingFootnote);
                await _unitOfWork.SaveAsync();
                return 1;
            }

            return -1;
        }

        public async Task<string> GetFootnote(TableFootnoteModel footnote)
        {
            var existingFootnote = await _unitOfWork.TableFootnoteSchemaRepository
                .GetFirstOrDefaultAsync(x => x.FootnoteMapping == footnote.FootnoteMapping
                                        && x.FootnoteIdentifier == footnote.FootnoteIdentifier);

            if (existingFootnote != null)
            {
                _logger.LogInformation($"Footnote found for mapping {footnote.FootnoteMapping} and identifier {footnote.FootnoteIdentifier}");
                return existingFootnote.FootnoteContent;
            }

            _logger.LogWarning($"No footnote found for mapping {footnote.FootnoteMapping} and identifier {footnote.FootnoteIdentifier}");
            return string.Empty;
        }

        public async Task<CLOReportingModel> GetInvestmentCompaniesAndCLODetails(string connectionString)
        {
            CLOReportingModel cLOReportingModel = new CLOReportingModel();
            try
            {
                List<InvestmentCompanyData> InvestmentCompaniesData = new List<InvestmentCompanyData>();
                List<TableMetadataModel> tableMetadataList = new List<TableMetadataModel>();
                Dictionary<string, TableDataResult> tableDataResult = null;
                List<ReportingDataCoulmn> reportingDataCoulmnsForInvestMentCompany = new List<ReportingDataCoulmn>();
                List<ReportingDataCoulmn> reportingDataCoulmnsForCLO = new List<ReportingDataCoulmn>();

                tableMetadataList = _tableMetadataService.GetTableMetadataWithColumn();
                if (tableMetadataList.Any())
                {
                    _logger.LogInformation($"Executing SQL queries for {tableMetadataList.Count} tables");
                    tableDataResult = await TableDataHelper.GetMultipleTablesDataAsync(connectionString, tableMetadataList);
                }
                var reportingDataCoulmns = await _dapperGenericRepository.Query<ReportingDataCoulmn>(SqlConstants.QueryTOGetCLOReportingDataColumn);
                if (reportingDataCoulmns != null)
                {
                    reportingDataCoulmnsForInvestMentCompany = reportingDataCoulmns.Where(s => s.PageId == 1).ToList();
                    reportingDataCoulmnsForCLO = reportingDataCoulmns.Where(s => s.PageId == 2).ToList();
                }
                List<CLO_InvestmentCompanyDetails> CLO_InvestmentCompanyDetails = await _unitOfWork.InvestmentCompanyDetailsRepository.GetAllAsyn();
                if (CLO_InvestmentCompanyDetails != null)
                {
                    List<InvestmentCompanyModel> investmentCompanies = CLO_InvestmentCompanyDetails.OrderByDescending(x => x.CreatedOn).Select(i => i.ToModel()).ToList();
                    foreach (var item in investmentCompanies)
                    {
                        InvestmentCompanyData investmentCompany = new InvestmentCompanyData();
                        investmentCompany.Id = item.Id;
                        investmentCompany.CompanyName = item.CompanyName;
                        investmentCompany.SectionData = await GetInvestmentCompanyTabDetailsAsync(item, tableDataResult, reportingDataCoulmnsForInvestMentCompany);
                        investmentCompany.CLOs = GetCLOPageDetails(item, tableDataResult, reportingDataCoulmnsForCLO)?.Result;
                        InvestmentCompaniesData.Add(investmentCompany);
                    }
                    cLOReportingModel.Data = InvestmentCompaniesData;
                }

            }
            catch (Exception ex)
            {
                _logger.LogError($"GetInvestmentCompaniesAndCLODetails at {DateTime.Now}. Error: {ex.Message}");
            }
            return cLOReportingModel;
        }


        private async Task<List<SectionData>> GetInvestmentCompanyTabDetailsAsync(InvestmentCompanyModel investmentCompanyModel, Dictionary<string, TableDataResult>? tableDataResult, List<ReportingDataCoulmn> reportingDataCoulmnsForInvestMentCompany)
        {
            try
            {
                List<SectionData> sectionData = new List<SectionData>();
                var commentries = await _unitOfWork.CloCommentriesRepository.GetFirstOrDefaultAsync(x => x.InvestmentCompanyId == investmentCompanyModel.Id);
                var objPageTabDetails = reportingDataCoulmnsForInvestMentCompany.DistinctBy(d => d.SectionName).ToList();
                if (objPageTabDetails.Count > 0 && objPageTabDetails != null)
                {
                    foreach (var item in objPageTabDetails)
                    {
                        SectionData section = new SectionData();
                        List<TabDetails> investCompanyTabDetails = new List<TabDetails>();
                        section.SectionName = item.SectionName;
                        section.AliasName = item.SectionName;
                        var pageTabDetails = reportingDataCoulmnsForInvestMentCompany.Where(s => s.TabId == item.TabId).OrderBy(o=> o.SequenceNo).ToList();
                        if (pageTabDetails != null)
                        {
                            foreach (var pageTabDetail in pageTabDetails)
                            {
                                TabDetails investCompanyTab = new TabDetails();
                                investCompanyTab.TableAliasName = pageTabDetail.TableName;
                                investCompanyTab.TableName = pageTabDetail.TableIdentifier;
                                GetInvestmentCompanyPageDetails(investmentCompanyModel, tableDataResult, commentries, item, pageTabDetail, investCompanyTab);
                                investCompanyTabDetails.Add(investCompanyTab);
                            }
                        }

                        section.Tables = investCompanyTabDetails;
                        sectionData.Add(section);
                    }
                }
                return sectionData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, ex);
                throw;
            }
        }

        private void GetInvestmentCompanyPageDetails(InvestmentCompanyModel investmentCompanyModel, Dictionary<string, TableDataResult>? tableDataResult, clo_commentries? commentries, ReportingDataCoulmn item, ReportingDataCoulmn pageTabDetail, TabDetails investCompanyTab)
        {
            if (pageTabDetail.TableIdentifier == "Company_Facts")
            {
                investCompanyTab.TableDataResult = GetCompanyFact(investmentCompanyModel);
            }
            else if (pageTabDetail.TableIdentifier == "Investment_Summary")
            {
                investCompanyTab.TableDataResult = GetInvestmentSummary(investmentCompanyModel);
            }

            else if (item.SectionName == "Commentaries" && pageTabDetail.TableIdentifier == "CLO_Commentries" && commentries != null)
            {
                investCompanyTab.TableDataResult = GetCLOGLICommentry(commentries);
            }
            else if (item.SectionName == "Commentaries" && pageTabDetail.TableIdentifier == "CLO_Commentries1" && commentries != null)
            {
                investCompanyTab.TableDataResult = GetCLOMarketCommentry(commentries);
            }
            else
            {
                if (tableDataResult != null && tableDataResult.TryGetValue(pageTabDetail?.TableObjectName, out var tableData))
                {
                    // Filter data by CompanyID using helper method
                    var filteredTableDataResult = FilterTableDataResultByCompanyId(tableData, investmentCompanyModel.Id);
                    investCompanyTab.TableDataResult = filteredTableDataResult;
                    _logger.LogInformation($"Applied CompanyID filter for {pageTabDetail?.TableObjectName}: {filteredTableDataResult.Data.Count} rows");
                }
                else
                {
                    _logger.LogWarning($"No table data found for {pageTabDetail?.TableObjectName}");
                    investCompanyTab.TableDataResult = new TableDataResult
                    {
                        Success = false,
                        Data = new List<Dictionary<string, object>>(),
                        Columns = new List<ColumnInfo>()
                    };
                }
            }
        }

  

        /// <summary>
        /// Get CLOPage Details
        /// </summary>
        /// <param name="InvestmentCompany"></param>
        /// <param name="tableDataResult"></param>
        /// <param name="reportingDataCoulmnsForCLO"></param>
        /// <returns></returns>
        public async Task<List<CLOData>> GetCLOPageDetails(InvestmentCompanyModel InvestmentCompany, Dictionary<string, TableDataResult>? tableDataResult, List<ReportingDataCoulmn> reportingDataCoulmnsForCLO)
        {
            List<CLOData> cLOData = new List<CLOData>();
            IEnumerable<CloDetails> cloRepositoryDetails = await _unitOfWork.CloDetailsRepository.GetManyAsync(i => i.CompanyID == InvestmentCompany.Id);
            if (cloRepositoryDetails != null)
            {
                List<CLOModel> cloDetail = cloRepositoryDetails.Select(i => i.ToModel()).OrderBy(i => i.CreatedOn).ToList();
                foreach (CLOModel clo in cloDetail)
                {
                    CLOData cLO = new CLOData();
                    cLO.CLO_ID = clo.CLO_ID;
                    cLO.Issuer = clo.Issuer;
                    cLO.CloSummaryTitle = $"{clo.Issuer}({InvestmentCompany.CompanyName})";
                    cLO.SectionData = await GetCLOTabDetailsAsync(InvestmentCompany.Id, tableDataResult, clo, reportingDataCoulmnsForCLO);

                    cLOData.Add(cLO);
                }
            }
            return cLOData;
        }

        /// <summary>
        /// Get CLO Tab DetailsAsync using company 
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="tableDataResult"></param>
        /// <param name="cLO"></param>
        /// <param name="reportingDataCoulmnsForCLO"></param>
        /// <returns></returns>
        public async Task<List<SectionData>> GetCLOTabDetailsAsync(int companyId, Dictionary<string, TableDataResult>? tableDataResult, CLOModel cLO, List<ReportingDataCoulmn> reportingDataCoulmnsForCLO)
        {
            try
            {

                List<SectionData> sectionData = new List<SectionData>();
                var objPageTabDetails = reportingDataCoulmnsForCLO.DistinctBy(d => d.SectionName).ToList();
                if (objPageTabDetails.Count > 0 && objPageTabDetails != null)
                {
                    foreach (var item in objPageTabDetails)
                    {
                        SectionData section = new SectionData();
                        List<TabDetails> cloTabDetails = new List<TabDetails>();
                        section.SectionName = item.SectionName;
                        section.AliasName = item.SectionName;
                        var reportingCLODataCoulmns = reportingDataCoulmnsForCLO.Where(s => s.TabId == item.TabId).OrderBy(o=> o.SequenceNo).ToList();
                        if (reportingCLODataCoulmns != null)
                        {
                            foreach (var reportingCLODataCoulmn in reportingCLODataCoulmns)
                            {
                                TabDetails cloTab = new TabDetails();
                                cloTab.TableAliasName = reportingCLODataCoulmn.TableIdentifier;
                                cloTab.TableName = reportingCLODataCoulmn.TableName;
                                if (cloTab.TableName == "Summary")
                                {
                                    cloTab.TableDataResult = GetCLOSummary(cLO);
                                }
                                else
                                {
                                    // Get table data from the dataTable dictionary based on key
                                    if (tableDataResult != null && tableDataResult.TryGetValue(reportingCLODataCoulmn.TableObjectName, out var tableData))
                                    {
                                        // Filter data by CompanyID using helper method
                                        var filteredTableDataResult = FilterTableDataResultByCompanyId(tableData, companyId, cLO.UniqueID);
                                        cloTab.TableDataResult = filteredTableDataResult;
                                        _logger.LogInformation($"Applied CompanyID filter for CLO {reportingCLODataCoulmn.TableIdentifier}: {filteredTableDataResult.Data.Count} rows");
                                    }
                                    else
                                    {
                                        _logger.LogWarning($"No CLO table data found for {reportingCLODataCoulmn.TableIdentifier}");
                                        cloTab.TableDataResult = new TableDataResult
                                        {
                                            Success = false,
                                            Data = new List<Dictionary<string, object>>(),
                                            Columns = new List<ColumnInfo>()
                                        };
                                    }
                                }
                                cloTabDetails.Add(cloTab);
                            }

                        }
                        section.Tables = cloTabDetails;
                        sectionData.Add(section);
                    }
                }
                return sectionData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, ex);
                throw;
            }
        }


        private TableDataResult GetCLOGLICommentry(clo_commentries commentries)
        {
            clo_commentries clo_Commentries = new clo_commentries();
            clo_Commentries.Glicommentry = commentries.Glicommentry;
            var tableDataResult = TableDataHelper.ConvertObjectToTableDataResult(clo_Commentries, "CLO_Commentries");
            return tableDataResult;
        }

        private TableDataResult GetCLOMarketCommentry(clo_commentries commentries)
        {
            clo_commentries clo_Commentries = new clo_commentries();
            clo_Commentries.MarketCommentry = commentries.MarketCommentry;
            var tableDataResult = TableDataHelper.ConvertObjectToTableDataResult(clo_Commentries, "CLO_Commentries1");
            return tableDataResult;
        }

        private TableDataResult GetCompanyFact(InvestmentCompanyModel investmentCompanyModel)
        {
            CompanyFact companyFact = new CompanyFact();
            companyFact.Domicile = investmentCompanyModel.Domicile;
            companyFact.IncorporationDate = investmentCompanyModel.IncorporationDate;
            companyFact.FirstClose = investmentCompanyModel.FirstClose;
            companyFact.FinalClose = investmentCompanyModel.FinalClose;
            companyFact.InvestmentPeriodEndDate = investmentCompanyModel.InvestmentPeriodEndDate;
            companyFact.MaturityDate = investmentCompanyModel.MaturityDate;
            companyFact.Commitments = investmentCompanyModel.Commitments;
            companyFact.BaseCurrency = investmentCompanyModel.BaseCurrency;
            companyFact.Custodian = investmentCompanyModel.Custodian;
            companyFact.Administrator = investmentCompanyModel.Administrator;
            companyFact.ListingAgent = investmentCompanyModel.ListingAgent;
            companyFact.LegalCounsel = investmentCompanyModel.LegalCounsel;
            companyFact.PortfolioAdvisor = investmentCompanyModel.PortfolioAdvisor; ;

            // Use the helper method to convert CompanyFact to TableDataResult
            var tableDataResult = TableDataHelper.ConvertObjectToTableDataResult(companyFact, "CompanyFact");
            return tableDataResult;
        }


        private TableDataResult GetInvestmentSummary(InvestmentCompanyModel investmentCompanyModel)
        {
            InvestmenetSummary investmenetSummary = new InvestmenetSummary();
            investmenetSummary.InvestmentSummary = investmentCompanyModel.InvestmentSummary;
            var tableDataResult = TableDataHelper.ConvertObjectToTableDataResult(investmenetSummary, "Investment_Summary");
            return tableDataResult;
        }
        /// <summary>
        /// Get CLO Summary
        /// </summary>
        /// <param name="cLO"></param>
        /// <returns></returns>
        private TableDataResult GetCLOSummary(CLOModel cLO)
        {
            CLODetails CLODetails = new CLODetails();
            CLODetails.Trustee = cLO.Trustee;
            CLODetails.Arranger = cLO.Arranger;
            CLODetails.CurrentMaturityDate = cLO.CurrentMaturityDate;
            CLODetails.Closed = cLO.Closed;
            CLODetails.CallEndDate = cLO.CallEndDate;
            CLODetails.LastRefiDate = cLO.LastRefiDate;
            CLODetails.Priced = cLO.Priced;
            CLODetails.OriginalEndOfReinvestmentDate = cLO.CurrentEndOfReinvestmentDate;
            CLODetails.CurrentEndOfReinvestmentDate = cLO.CurrentEndOfReinvestmentDate;
            var tableDataResult = TableDataHelper.ConvertObjectToTableDataResult(CLODetails, "Summary");
            return tableDataResult;
        }

        /// <summary>
        /// FilterTableDataResultByCompanyId
        /// </summary>
        /// <param name="tableDataResult"></param>
        /// <param name="companyId"></param>
        /// <param name="uniqueId"></param>
        /// <returns></returns>
        private TableDataResult FilterTableDataResultByCompanyId(TableDataResult tableDataResult, int companyId, string? uniqueId = null)
        {
            if (tableDataResult?.Data == null || tableDataResult.Data.Count == 0)
            {
                _logger.LogWarning($"No data to filter for CompanyID {companyId}");
                return new TableDataResult
                {
                    Success = false,
                    Data = new List<Dictionary<string, object>>(),
                    Columns = tableDataResult?.Columns ?? new List<ColumnInfo>()
                };
            }
            try
            {
                var filteredData = tableDataResult.Data.Where(row =>
                {
                    // Check if CompanyID column exists
                    if (!row.ContainsKey("CompanyID")){
                        return false;
                    }
                    var companyIdValue = row["CompanyID"];

                    if (companyIdValue == null)
                    {
                        return false;
                    }

                    var companyIdString = companyIdValue.ToString();
                    return (companyIdString == companyId.ToString() || companyIdString == uniqueId);
                }).ToList();

                _logger.LogInformation($"Filtered {tableDataResult.Data.Count} rows to {filteredData.Count} rows for CompanyID {companyId}");

                return new TableDataResult
                {
                    Success = tableDataResult.Success,
                    Data = filteredData,
                    Columns = tableDataResult.Columns
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error filtering TableDataResult by CompanyID {companyId}: {ex.Message}");
                return new TableDataResult
                {
                    Success = false,
                    Data = new List<Dictionary<string, object>>(),
                    Columns = tableDataResult.Columns
                };
            }
        }
    }
}