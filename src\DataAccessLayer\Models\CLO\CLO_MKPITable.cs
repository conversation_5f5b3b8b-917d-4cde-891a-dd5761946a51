﻿using DataAccessLayer.DBModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccessLayer.Models.CLO
{
    [Table("CLO_MKPITable")]
    public class CLOMKPITable : BaseModel
    {
        [Key]
        public int TableID { get; set; }
        public string TableIdentifier { get; set; }
        public string TableObjectName { get; set; }
        public string TableAliasName { get; set; }
        public int TableType { get; set; }
        public int ModuleType { get; set; }
        public int TabId { get; set; }
        public int SequenceNo { get; set; }
        public string TableName { get; set; }
    }
}
