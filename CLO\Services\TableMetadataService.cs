using Azure.Core;
using CLO.CQRS.Handlers;
using CLO.Models;
using CLO.Services.interfaces;
using Contract.BulkUpload;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace CLO.Services
{
    public class TableMetadataService : ITableMetadataService
    {
        private readonly ILogger<TableMetadataService> _logger;
        public TableMetadataService(ILogger<TableMetadataService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        // Define metadata mappings
        private Dictionary<string, TableMetadataModel> metadataMap = new Dictionary<string, TableMetadataModel>
        {
            ["NAV_Distribution"] = new TableMetadataModel
            {
                Title = "Nav & Distribution",
                TableName = "CLO_Nav_Distribution",
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "NAV Per EUR Note (Series 1,2,3 & 4)", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false,DecimalUnits = 4},
                        new ColumnMetadata { Name = "NAV per USD Note (Series 1)", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false,DecimalUnits = 4},
                        new ColumnMetadata { Name = "NAV per USD Note (Series 3)", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false,DecimalUnits = 4},
                        new ColumnMetadata { Name = "NAV per USD Note (Series 4)", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false,DecimalUnits = 4 },
                        new ColumnMetadata { Name = "Net Assets Valuation of Notes Issued", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false,DecimalUnits = 4 },
                        new ColumnMetadata { Name = "Distribution: Series 1 EUR - 2024 YTD", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false, AliasName ="Distribution: Series 1 EUR - 2025 YTD" },
                        new ColumnMetadata { Name = "Distribution: Series 1 USD - 2024 YTD", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false, AliasName ="Distribution: Series 1 USD - 2025 YTD" },
                        new ColumnMetadata { Name = "Distribution: Series 1 EUR - 2023", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false, AliasName ="Distribution: Series 1 EUR - 2024" },
                        new ColumnMetadata { Name = "Distribution: Series 1 USD - 2023", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false, AliasName ="Distribution: Series 1 USD - 2024" },
                        new ColumnMetadata { Name = "No.of distributions to date", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false,IsWholeNumber=true },
                        new ColumnMetadata { Name = "Frequency of distribution", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false },
                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
                TableType = TableType.Static
            },
            ["PE_Performance_Indicators"] = new TableMetadataModel
            {
                Title = "PE Performance Indicator",
                TableName = "CLO_PE_Performance_Indicator",
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Metrics", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false },
                        new ColumnMetadata { Name = "Quarter to 28 June 2024", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Quarter to 31 March 2025" },
                        new ColumnMetadata { Name = "YTD 28 June 2024", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false,AliasName= "YTD 31 March 2025" },
                        new ColumnMetadata { Name = "Since Inception(September 2019)", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false }
                    }.Concat(new BaseTableMetadataModel().Columns).ToList()
            },
            ["Return_Analysis"] = new TableMetadataModel
            {
                Title = "Return Analysis",
                TableName = "CLO_Return_Analysis",
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Metrics", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false },
                        new ColumnMetadata { Name = "GLI I Time weighted Net EUR Return (Including FX gain/Loss)", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "European Loans Index (CS West European Index)", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "U.S.Loans Index (Morningstar Index)", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false }
                    }.Concat(new BaseTableMetadataModel().Columns).ToList()
            },
            ["Currency_Exposure"] = new TableMetadataModel
            {
                Title = "Current Exposer",
                TableName = "CLO_CurrencyExposure",
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "USD demonination assets", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "EURO denomination assets", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false },
                         new ColumnMetadata { Name = "Total", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false }
                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
                TableType = TableType.Static
            },
            ["GLI_Portfolio_Composition"] = new TableMetadataModel
            {
                Title = "GLI Portfolio Composition",
                TableName = "CLO_GLI_Portfolio_Composition",
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Transaction", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false },
                        new ColumnMetadata { Name = "CLO Size", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false, CurrencyUnit = CurrencyUnit.Millions ,DecimalUnits = 2},
                        new ColumnMetadata { Name = "PAR Local", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false, DecimalUnits = 0 },
                        new ColumnMetadata { Name = "Amortised cost", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false},
                        new ColumnMetadata { Name = "Model valuation", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false},
                        new ColumnMetadata { Name = "NAV % (Equity)", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "Model Valuation in Euros", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false, DecimalUnits = 0},
                        new ColumnMetadata { Name = "WA Coupon", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false },
                         new ColumnMetadata { Name = "WA Cost of Capital", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "WA Arbritage", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "Distributions Annualized", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "Realised warehouse IRRs", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false },
                         new ColumnMetadata { Name = "Indicative Projected IRR", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "Indicative Projected Multiple", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "End reinvestment date", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false },
                    }.Concat(new BaseTableMetadataModel().Columns).ToList()
            },
            ["Return_Composition"] = new TableMetadataModel
            {
                Title = "Return_Composition",
                TableName = "CLO_Return_Composition",
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Metrics", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false,AliasName = "Metrics"},
                        new ColumnMetadata { Name = "Quarter#Price", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false, AliasName = "Price",Parent ="Quarter 1,2025 Total Return Composition"  },
                        new ColumnMetadata { Name = "Quarter#Income", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Income",Parent ="Quarter 1,2025 Total Return Composition"  },
                        new ColumnMetadata { Name = "Quarter#Fx", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false,AliasName="FX",Parent ="Quarter 1,2025 Total Return Composition" },
                        new ColumnMetadata { Name = "Quarter#Total", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Total",Parent ="Quarter 1,2025 Total Return Composition" },
                        new ColumnMetadata { Name = "Year#Price", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false,AliasName ="Price",Parent = "Year to Date 2025 Total Return Composition"  },
                        new ColumnMetadata { Name = "Year#Income", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false , AliasName = "Income",Parent = "Year to Date 2025 Total Return Composition" },
                        new ColumnMetadata { Name = "Year#Fx", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="FX",Parent = "Year to Date 2025 Total Return Composition"},
                        new ColumnMetadata { Name = "Year#Total", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Total",Parent = "Year to Date 2025 Total Return Composition" },
                        new ColumnMetadata { Name = "GLI_I_NetAssets", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName = "GLI I NetAssets", IsUniqueIdentifier = false, IsHidden = false},
                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
                TableType = TableType.Pivot
            },

            ["Aggregate_CLO_Metrics_EU"] = new TableMetadataModel
            {
                Title = "Aggregate CLO Metrics",
                TableName = "CLO_Aggregate_CLO_Metrics_EU",
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Transaction", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false,AliasName = "Transaction",EnableLink = true },
                        new ColumnMetadata { Name = "AsofDate", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false, AliasName = "As of Date" },
                        new ColumnMetadata { Name = "CLO_AUM", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "CLO AUM"  },
                        new ColumnMetadata { Name = "Weighted_Average_Spread", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false,AliasName="Weighted Average Spread" },
                        new ColumnMetadata { Name = "ARB", Type = ColumnType.Percentage, IsHidden = false,AliasName = "ARB"},
                        new ColumnMetadata { Name = "w1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA",Parent = "Weighted Average Rating Factor"  },
                        new ColumnMetadata { Name = "w2", Type = ColumnType.Number, DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating",Parent = "Weighted Average Rating Factor" },
                        new ColumnMetadata { Name = "c1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA 1",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c2", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating 1",Parent = "% CCC/Caa Loans" },
                        new ColumnMetadata { Name = "c3", Type = ColumnType.String, IsHidden = false,AliasName = "RA 2",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c4", Type = ColumnType.Percentage,AliasName="Rating 2",Parent ="% CCC/Caa Loans", IsHidden = false },
                        new ColumnMetadata { Name = "Percentage_Loans_trading_under80", Type = ColumnType.Percentage, IsHidden = false,AliasName ="% Loans trading under 80"  },
                        new ColumnMetadata { Name = "Weighter_Average_Purchase_Price", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false , AliasName = "Weighted Average Purchase Price" },
                        new ColumnMetadata { Name = "Diversity_Score", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Diversity Score" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Par Gain/Loss" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss_Post_HaircutORDefaults", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName="Par Gain/Loss Post Haircut/Defaults", IsHidden = false },
                        new ColumnMetadata { Name = "Defaulted Obligation PAR", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Defaulted Obligation PAR"},
                        new ColumnMetadata { Name = "Default_Haircut", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName ="Default Haircut",IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "Default_Loss_Cumulative", Type = ColumnType.Percentage,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Default Loss Cumulative"},
                        new ColumnMetadata { Name = "GLI_Par_GainORLoss", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2,AliasName="GLI Par Gain/Loss",IsUniqueIdentifier = false, IsHidden = false }

                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
                TableType = TableType.Pivot
            },
            ["Aggregate_CLO_Metrics_US"] = new TableMetadataModel
            {
                Title = "Aggregate CLO Metrics",
                TableName = "CLO_Aggregate_CLO_Metrics_US",
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Transaction", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false,AliasName = "Transaction" ,EnableLink = true},
                        new ColumnMetadata { Name = "AsofDate", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false, AliasName = "As of Date" },
                        new ColumnMetadata { Name = "CLO_AUM", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "CLO AUM"  },
                        new ColumnMetadata { Name = "Weighted_Average_Spread", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false,AliasName="Weighted Average Spread" },
                        new ColumnMetadata { Name = "ARB", Type = ColumnType.Percentage, IsHidden = false,AliasName = "ARB"},
                        new ColumnMetadata { Name = "w1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA",Parent = "Weighted Average Rating Factor"  },
                        new ColumnMetadata { Name = "w2", Type = ColumnType.Number, DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating",Parent = "Weighted Average Rating Factor" },
                        new ColumnMetadata { Name = "c1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA 1",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c2", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating 1",Parent = "% CCC/Caa Loans" },
                        new ColumnMetadata { Name = "c3", Type = ColumnType.String, IsHidden = false,AliasName = "RA 2",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c4", Type = ColumnType.Percentage,AliasName="Rating 2",Parent ="% CCC/Caa Loans", IsHidden = false },
                        new ColumnMetadata { Name = "Percentage_Loans_trading_under80", Type = ColumnType.Percentage, IsHidden = false,AliasName ="% Loans trading under 80"  },
                        new ColumnMetadata { Name = "Weighter_Average_Purchase_Price", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false , AliasName = "Weighted Average Purchase Price" },
                        new ColumnMetadata { Name = "Diversity_Score", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Diversity Score" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsHidden = false,AliasName = "Par Gain/Loss" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss_Post_HaircutORDefaults", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName="Par Gain/Loss Post Haircut/Defaults", IsHidden = false },
                        new ColumnMetadata { Name = "Defaulted_Obligation_PAR", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Defaulted Obligation PAR"},
                        new ColumnMetadata { Name = "Carrying_Valueof_Defaulted_Obligation", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName ="Carrying Value of Defaulted Obligation", IsHidden = false },
                        new ColumnMetadata { Name = "Default_Loss_Cumulative", Type = ColumnType.Percentage,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Default Loss Cumulative"},
                        new ColumnMetadata { Name = "Default_Haircut", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2,AliasName="Default Haircut", IsHidden = false }

                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
                TableType = TableType.Pivot
            },
            ["Capital_Structure"] = new TableMetadataModel
            {
                Title = "Capital Structure",
                TableName = "CLO_Capital_Structure",
                IsCLO = true,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Tranche", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false,AliasName = "Tranche" },
                        new ColumnMetadata { Name = "Type", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Type" },
                        new ColumnMetadata { Name = "OrigRatings(S&P/FT)", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false, AliasName = "Orig Ratings (S&P/FT)" },
                        new ColumnMetadata { Name = "CurrRatings(S&P/FT)", Type = ColumnType.String,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Curr Ratings (S&P/FT)"  },
                        new ColumnMetadata { Name = "OrigBalance", Type = ColumnType.Currency, DecimalUnits = 0, IsUniqueIdentifier = false, IsHidden = false,AliasName="Orig Balance" },
                        new ColumnMetadata { Name = "CurrBalance", Type = ColumnType.Currency, DecimalUnits = 0, IsHidden = false,AliasName = "Curr Balance"},
                        new ColumnMetadata { Name = "Factor", Type = ColumnType.Number, IsHidden = false,AliasName ="Factor"  },
                        new ColumnMetadata { Name = "FloaterFormula", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "Floater Formula"},
                        new ColumnMetadata { Name = "Coupon", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Coupon"},
                        new ColumnMetadata { Name = "CurrParSubord(defatMV)", Type = ColumnType.Number, IsHidden = false,AliasName = "Curr Par Subord (def at MV)"},
                        new ColumnMetadata { Name = "AccumUnrealizedWritedown", Type = ColumnType.Number,AliasName="Accum Unrealized Writedown",IsHidden = false },
                        new ColumnMetadata { Name = "AccumIntShortfall", Type = ColumnType.Number,AliasName="Accum Int Shortfall",IsHidden = false },

                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
            },
            ["Key_KPIs_US"] = new TableMetadataModel
            {
                Title = "Key KPIs",
                TableName = "Key_KPIs_US",
                IsCLO = true,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Transaction", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false,AliasName = "Transaction" , EnableLink= true },
                        new ColumnMetadata { Name = "AsofDate", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false, AliasName = "As of Date" },
                        new ColumnMetadata { Name = "CLO_AUM", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "CLO AUM"  },
                        new ColumnMetadata { Name = "Weighted_Average_Spread", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false,AliasName="Weighted Average Spread" },
                        new ColumnMetadata { Name = "ARB", Type = ColumnType.Percentage, IsHidden = false,AliasName = "ARB"},
                        new ColumnMetadata { Name = "w1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA",Parent = "Weighted Average Rating Factor"  },
                        new ColumnMetadata { Name = "w2", Type = ColumnType.Number, DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating",Parent = "Weighted Average Rating Factor" },
                        new ColumnMetadata { Name = "c1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA 1",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c2", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating 1",Parent = "% CCC/Caa Loans" },
                        new ColumnMetadata { Name = "c3", Type = ColumnType.String, IsHidden = false,AliasName = "RA 2",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c4", Type = ColumnType.Percentage,AliasName="Rating 2",Parent ="% CCC/Caa Loans", IsHidden = false },
                        new ColumnMetadata { Name = "Percentage_Loans_trading_under80", Type = ColumnType.Percentage, IsHidden = false,AliasName ="% Loans trading under 80"  },
                        new ColumnMetadata { Name = "Weighter_Average_Purchase_Price", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false , AliasName = "Weighted Average Purchase Price" },
                        new ColumnMetadata { Name = "Diversity_Score", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Diversity Score" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsHidden = false,AliasName = "Par Gain/Loss" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss_Post_HaircutORDefaults", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName="Par Gain/Loss Post Haircut/Defaults", IsHidden = false },
                        new ColumnMetadata { Name = "Defaulted_Obligation_PAR", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Defaulted Obligation PAR"},
                        new ColumnMetadata { Name = "Carrying_Valueof_Defaulted_Obligation", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName ="Carrying Value of Defaulted Obligation", IsHidden = false },
                        new ColumnMetadata { Name = "Default_Loss_Cumulative", Type = ColumnType.Percentage,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Default Loss Cumulative"},
                        new ColumnMetadata { Name = "Default_Haircut", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2,AliasName="Default Haircut", IsHidden = false }
                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
                TableType = TableType.Pivot
            },
            ["Key_KPIs_EU"] = new TableMetadataModel
            {
                Title = "Key KPIs",
                TableName = "CLO_Key_KPIs_EU",
                IsCLO = true,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Transaction", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false,AliasName = "Transaction" ,EnableLink = true },
                        new ColumnMetadata { Name = "AsofDate", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false, AliasName = "As of Date" },
                        new ColumnMetadata { Name = "CLO_AUM", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "CLO AUM"  },
                        new ColumnMetadata { Name = "Weighted_Average_Spread", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false,AliasName="Weighted Average Spread" },
                        new ColumnMetadata { Name = "ARB", Type = ColumnType.Percentage, IsHidden = false,AliasName = "ARB"},
                        new ColumnMetadata { Name = "w1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA",Parent = "Weighted Average Rating Factor"  },
                        new ColumnMetadata { Name = "w2", Type = ColumnType.Number, DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating",Parent = "Weighted Average Rating Factor" },
                        new ColumnMetadata { Name = "c1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA 1",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c2", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating 1",Parent = "% CCC/Caa Loans" },
                        new ColumnMetadata { Name = "c3", Type = ColumnType.String, IsHidden = false,AliasName = "RA 2",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c4", Type = ColumnType.Percentage,AliasName="Rating 2",Parent ="% CCC/Caa Loans", IsHidden = false },
                        new ColumnMetadata { Name = "Percentage_Loans_trading_under80", Type = ColumnType.Percentage, IsHidden = false,AliasName ="% Loans trading under 80"  },
                        new ColumnMetadata { Name = "Weighter_Average_Purchase_Price", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false , AliasName = "Weighted Average Purchase Price" },
                        new ColumnMetadata { Name = "Diversity_Score", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Diversity Score" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Par Gain/Loss" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss_Post_HaircutORDefaults", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName="Par Gain/Loss Post Haircut/Defaults", IsHidden = false },
                        new ColumnMetadata { Name = "Defaulted Obligation PAR", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Defaulted Obligation PAR"},
                        new ColumnMetadata { Name = "Default_Haircut", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName ="Default Haircut",IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "Default_Loss_Cumulative", Type = ColumnType.Percentage,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Default Loss Cumulative"},
                        new ColumnMetadata { Name = "GLI_Par_GainORLoss", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2,AliasName="GLI Par Gain/Loss",IsUniqueIdentifier = false, IsHidden = false }
                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
                TableType = TableType.Pivot
            },
            ["Collateral"] = new TableMetadataModel
            {
                Title = "Collateral",
                TableName = "CLO_Collateral",
                IsCLO = true,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "CurrBal", Type = ColumnType.Currency,  IsHidden = false,AliasName = "Curr Bal" },
                        new ColumnMetadata { Name = "Principal_Collection_Acct", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Principal Collection Acct" },
                        new ColumnMetadata { Name = "Interest_Collection_Acct", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false, AliasName = "Interest Collection Acct" },
                        new ColumnMetadata { Name = "Defaulted_Securities", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Defaulted Securities"  },
                        new ColumnMetadata { Name = "Loan_Bond", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false,AliasName="% Loan / Bond" },
                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
                TableType = TableType.Static
            },
            ["Overcollateralisation_Test"] = new TableMetadataModel
            {
                Title = "Overcollateralisation Test",
                TableName = "CLO_Overcollateralisation_Test",
                IsCLO = true,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Overcollateralisation Tests", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false },
                        new ColumnMetadata { Name = "Current", Type = ColumnType.Alphanumeric, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "Trigger", Type = ColumnType.Alphanumeric , IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "Result", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "Cushion", Type = ColumnType.Alphanumeric, IsUniqueIdentifier = false, IsHidden = false }
                    }.Concat(new BaseTableMetadataModel().Columns).ToList()
            },
            ["Collateral_Quality_Test"] = new TableMetadataModel
            {
                Title = "Collateral Quality Test",
                TableName = "CLO_Collateral_Quality_Test",
                IsCLO = true,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Collateral_Quality_Tests", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false,AliasName = "Collateral Quality Tests"},
                        new ColumnMetadata { Name = "Actual", Type = ColumnType.Alphanumeric, IsUniqueIdentifier = false, IsHidden = false, AliasName = "Actual"},
                        new ColumnMetadata { Name = "Threshold", Type = ColumnType.Alphanumeric, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Threshold"},
                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
            },
            ["CLO_Distributions_To_Date"] = new TableMetadataModel
            {
                Title = "CLO Distributions To Date",
                TableName = "CLO_Distributions_To_Date",
                IsCLO = true,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Period", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false,AliasName = "Period"},
                        new ColumnMetadata { Name = "Start_Date", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false, AliasName = "Start Date"},
                        new ColumnMetadata { Name = "End_Date_Pay_date", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false,AliasName = "End Date (Pay date)"},
                        new ColumnMetadata { Name = "Amount", Type = ColumnType.Currency,  IsUniqueIdentifier = false, IsHidden = false,AliasName="Amount"},
                        new ColumnMetadata { Name = "Percentage_Of_Cost", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false,AliasName = "% of cost (PAR)"},
                        new ColumnMetadata { Name = "Annualised_Percentage", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false,AliasName ="Annualised (PAR) %"},
                        new ColumnMetadata { Name = "Status", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false,AliasName ="Status"  },
                        new ColumnMetadata { Name = "Fee_rebate", Type = ColumnType.Currency,  IsUniqueIdentifier = false, IsHidden = false , AliasName = "Fee rebate" },
                        new ColumnMetadata { Name = "T_Inc_rebate", Type = ColumnType.Currency,IsUniqueIdentifier = false, IsHidden = false ,AliasName ="T. Inc. rebate" },
                        new ColumnMetadata { Name = "Percentage_Of_Cost_1", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false,AliasName = "% of cost (Gross)" },
                        new ColumnMetadata { Name = "Annualised_Percentage_1", Type = ColumnType.Percentage, IsUniqueIdentifier = false,  IsHidden = false , AliasName="Annualised (Gross) %"},
                        new ColumnMetadata { Name = "Percentage_Of_PAR", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="% of PAR"},
                        new ColumnMetadata { Name = "Annualised_Percentage_2", Type = ColumnType.Percentage, IsUniqueIdentifier = false, IsHidden = false, AliasName ="Annualised %",},
                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
            },
            ["KPI_History_EU"] = new TableMetadataModel
            {
                Title = "CLO KPI History",
                TableName = "CLO_KPI_History_EU",
                IsCLO = true,
                TableType = TableType.Pivot,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Transaction", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false,AliasName = "Transaction" },
                        new ColumnMetadata { Name = "AsofDate", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false, AliasName = "As of Date", IsSortable = true },
                        new ColumnMetadata { Name = "CLO_AUM", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "CLO AUM"  },
                        new ColumnMetadata { Name = "Weighted_Average_Spread", Type = ColumnType.Number, DecimalUnits= 2, IsUniqueIdentifier = false, IsHidden = false,AliasName="Weighted Average Spread" },
                        new ColumnMetadata { Name = "ARB", Type = ColumnType.Percentage, DecimalUnits= 2 , IsHidden = false,AliasName = "ARB"},
                        new ColumnMetadata { Name = "w1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA",Parent = "Weighted Average Rating Factor"  },
                        new ColumnMetadata { Name = "w2", Type = ColumnType.Number, DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating",Parent = "Weighted Average Rating Factor" },
                        new ColumnMetadata { Name = "c1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA 1",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c2", Type = ColumnType.Percentage, DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating 1",Parent = "% CCC/Caa Loans" },
                        new ColumnMetadata { Name = "c3", Type = ColumnType.String, IsHidden = false,AliasName = "RA 2",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c4", Type = ColumnType.Percentage, DecimalUnits= 2, AliasName="Rating 2",Parent ="% CCC/Caa Loans", IsHidden = false },
                        new ColumnMetadata { Name = "Percentage_Loans_trading_under80", Type = ColumnType.Percentage, DecimalUnits= 2, IsHidden = false,AliasName ="% Loans trading under 80"  },
                        new ColumnMetadata { Name = "Weighter_Average_Purchase_Price", Type = ColumnType.Percentage, DecimalUnits= 2, IsUniqueIdentifier = false, IsHidden = false , AliasName = "Weighted Average Purchase Price" },
                        new ColumnMetadata { Name = "Diversity_Score", Type = ColumnType.Number, DecimalUnits= 0, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Diversity Score" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Par Gain/Loss" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss_Post_HaircutORDefaults", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName="Par Gain/Loss Post Defaults Haircut", IsHidden = false },
                        new ColumnMetadata { Name = "Defaulted_Obligation_PAR", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Defaulted Obligation PAR"},
                        new ColumnMetadata { Name = "Default_Haircut", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName ="Default Haircut",IsUniqueIdentifier = false, IsHidden = false },
                        new ColumnMetadata { Name = "Default_Loss_Cumulative", Type = ColumnType.Percentage,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Default Loss Cumulative"},
                        new ColumnMetadata { Name = "GLI_Par_GainORLoss", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2,AliasName="GLI Par Gain/Loss",IsUniqueIdentifier = false, IsHidden = false }

                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
            },
            ["KPI_History_US"] = new TableMetadataModel
            {
                Title = "CLO KPI History",
                TableName = "CLO_KPI_History_US",
                IsCLO = true,
                TableType = TableType.Pivot,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Transaction", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false,AliasName = "Transaction" },
                        new ColumnMetadata { Name = "AsofDate", Type = ColumnType.String, IsUniqueIdentifier = true, IsHidden = false, AliasName = "As of Date", IsSortable = true },
                        new ColumnMetadata { Name = "CLO_AUM", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "CLO AUM"  },
                        new ColumnMetadata { Name = "Weighted_Average_Spread", Type = ColumnType.Number, DecimalUnits= 2, IsUniqueIdentifier = false, IsHidden = false,AliasName="Weighted Average Spread" },
                        new ColumnMetadata { Name = "ARB", Type = ColumnType.Percentage, DecimalUnits= 2 , IsHidden = false,AliasName = "ARB"},
                        new ColumnMetadata { Name = "w1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA",Parent = "Weighted Average Rating Factor"  },
                        new ColumnMetadata { Name = "w2", Type = ColumnType.Number, DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating",Parent = "Weighted Average Rating Factor" },
                        new ColumnMetadata { Name = "c1", Type = ColumnType.String, IsUniqueIdentifier = false, IsHidden = false , AliasName = "RA 1",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c2", Type = ColumnType.Percentage, DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Rating 1",Parent = "% CCC/Caa Loans" },
                        new ColumnMetadata { Name = "c3", Type = ColumnType.String, IsHidden = false,AliasName = "RA 2",Parent = "% CCC/Caa Loans"  },
                        new ColumnMetadata { Name = "c4", Type = ColumnType.Percentage, DecimalUnits= 2, AliasName="Rating 2",Parent ="% CCC/Caa Loans", IsHidden = false },
                        new ColumnMetadata { Name = "Percentage_Loans_trading_under80", Type = ColumnType.Percentage, DecimalUnits= 2, IsHidden = false,AliasName ="% Loans trading under 80"  },
                        new ColumnMetadata { Name = "Weighter_Average_Purchase_Price", Type = ColumnType.Percentage, DecimalUnits= 2, IsUniqueIdentifier = false, IsHidden = false , AliasName = "Weighted Average Purchase Price" },
                        new ColumnMetadata { Name = "Diversity_Score", Type = ColumnType.Number, DecimalUnits= 0, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Diversity Score" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Par Gain/Loss" },
                        new ColumnMetadata { Name = "Pair_Gain_Loss_Post_HaircutORDefaults", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName="Par Gain/Loss Post Defaults Haircut", IsHidden = false },
                        new ColumnMetadata { Name = "Defaulted_Obligation_PAR", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Defaulted Obligation PAR"},
                        new ColumnMetadata { Name = "Carrying_value_Defaulted_Obligation", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Carrying Value of Defaulted Obligation"},
                        new ColumnMetadata { Name = "Default_Loss_Cumulative", Type = ColumnType.Percentage,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Default Loss Cumulative"},
                        new ColumnMetadata { Name = "Default_Haircut", Type = ColumnType.Currency,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, AliasName ="Default Haircut",IsUniqueIdentifier = false, IsHidden = false },

                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
            },
            ["KPI_Summary"] = new TableMetadataModel
            {
                Title = "Summary",
                TableName = "CLO_KPI_Summary",
                IsCLO = true,
                TableType = TableType.Static,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Cost_of_Capital", Type = ColumnType.Percentage,DecimalUnits = 2, AliasName ="Cost of Capital"},
                        new ColumnMetadata { Name = "Target_PAR", Type = ColumnType.String,CurrencyUnit = CurrencyUnit.Millions,DecimalUnits = 2, IsUniqueIdentifier = false, IsHidden = false,AliasName = "Target PAR"  },
                        new ColumnMetadata { Name = "GLI_Owned_Notes", Type = ColumnType.Currency, IsUniqueIdentifier = false, IsHidden = false,AliasName="GLI Owned Notes" },
                        new ColumnMetadata { Name = "Total_Notes", Type = ColumnType.Currency, IsHidden = false,AliasName = "Total Notes"},
                        new ColumnMetadata { Name = "Reinvestment_Period_End_Date", Type = ColumnType.Date, DateFormat= "dd-MMM-yyyy", IsHidden = false,AliasName ="Reinvestment Period End Date"  },
                        new ColumnMetadata { Name = "Gross_Management_Fee", Type = ColumnType.Percentage, DecimalUnits= 2, IsUniqueIdentifier = false, IsHidden = false , AliasName = "Gross Management Fee"},
                        new ColumnMetadata { Name = "Updated_Target_PAR", Type = ColumnType.Currency,  DecimalUnits= 2, IsUniqueIdentifier = false, IsHidden = false ,AliasName ="Updated Target PAR"},
                        new ColumnMetadata { Name = "PAR_Flush", Type = ColumnType.Currency, DecimalUnits= 2 , IsHidden = false,AliasName = "PAR Flush"}

                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
            },
            ["CLO_Versus_CLO_Sector"] = new TableMetadataModel
            {
                Title = "CLO Versus CLO Sector",
                TableName = "CLO_Versus_CLO_Sector",
                IsCLO = true,
                TableType = TableType.Static,
                Columns = new List<ColumnMetadata>
                    {
                        new ColumnMetadata { Name = "Test_type", Type = ColumnType.String, AliasName ="Test type" , IsStaticTableHeader= true },
                        new ColumnMetadata { Name = "Caa1_CCC_or_Less", Type = ColumnType.String, AliasName = "Caa1/CCC or Less %"  },
                        new ColumnMetadata { Name = "Par_Build_Sen_OC", Type = ColumnType.String, AliasName="Par Build (Sen OC) %" },
                        new ColumnMetadata { Name = "Defaulted_Per", Type = ColumnType.String, AliasName = "Defaulted %"},
                        new ColumnMetadata { Name = "Curr_Collat_Spread", Type = ColumnType.String, AliasName ="Curr Collat Spread"  },
                        new ColumnMetadata { Name = "Curr_Tranche_Spread", Type = ColumnType.String, AliasName = "Curr Tranche Spread"},
                        new ColumnMetadata { Name = "Curr_Collat_Coupon", Type = ColumnType.String, AliasName ="Curr Collat Coupon"},
                        new ColumnMetadata { Name = "Curr_Tranche_Coupon", Type = ColumnType.String, AliasName ="Curr Tranche Coupon"},
                        new ColumnMetadata { Name = "WAL", Type = ColumnType.String, AliasName = "WAL"},
                        new ColumnMetadata { Name = "WARF", Type = ColumnType.String, AliasName = "WARF"},
                        new ColumnMetadata { Name = "Diversity_Score", Type = ColumnType.String, AliasName = "Diversity Score"},
                        new ColumnMetadata { Name = "Cov_Lite", Type = ColumnType.String, AliasName = "Cov-Lite %"},
                        new ColumnMetadata { Name = "Alt_Cov_Lite", Type = ColumnType.String, AliasName = "Alt Cov-Lite %"},
                        new ColumnMetadata { Name = "Sen_Secured_Loan", Type = ColumnType.String, AliasName = "Sen Secured Loan %"},
                        new ColumnMetadata { Name = "Collat_Bond", Type = ColumnType.String, AliasName = "Collat Bond %"},
                        new ColumnMetadata { Name = "MVOC_BB", Type = ColumnType.String, AliasName = "MVOC (BB)"},
                        new ColumnMetadata { Name = "MVOC_Equity", Type = ColumnType.String, AliasName = "MVOC (Equity)"},
                        new ColumnMetadata { Name = "NAV_Equity", Type = ColumnType.String, AliasName = "NAV % (Equity)"},
                        new ColumnMetadata { Name = "Total_Equity_Yld_Annualized", Type = ColumnType.String, AliasName = "Total Equity Yld (Annualized)"},
                        new ColumnMetadata { Name = "Collect_Acct_Prin", Type = ColumnType.String, AliasName = "Collect Acct Prin %"},
                        new ColumnMetadata { Name = "Equity_Leverage", Type = ColumnType.String, AliasName = "Equity Leverage"},
                        new ColumnMetadata { Name = "2nd_Lien", Type = ColumnType.String, AliasName = "2nd Lien %"},
                        new ColumnMetadata { Name = "12mo_Turnover", Type = ColumnType.String, AliasName = "12mo Turnover"},

                    }.Concat(new BaseTableMetadataModel().Columns).ToList(),
            },
            ["Leverage"] = new TableMetadataModel
            {
                Title = "Leverage ",
                TableName = "CLO_Leverage",
                IsCLO = true,
                Columns = new List<ColumnMetadata>
                    {
                       new ColumnMetadata { Name = "Metrics", Type = ColumnType.String,  IsHidden = false,AliasName = "Metrics"},
                        new ColumnMetadata { Name = "OrigBalance", Type = ColumnType.Number, IsUniqueIdentifier = false, IsHidden = false,AliasName="Orig Balance" },
                        new ColumnMetadata { Name = "CurrBalance", Type = ColumnType.Number, IsHidden = false,AliasName = "Curr Balance"},
                         }.Concat(new BaseTableMetadataModel().Columns).ToList(),
                TableType = TableType.Static
            },
        };
        public TableMetadataModel GetTableMetadata(string tableName, bool IsIncludeHiddenColumns = false)
        {
            // Convert snake_case to Title Case
            string title = Regex.Replace(tableName.Replace("_", " "), @"\b\w", m => m.Value.ToUpper(), RegexOptions.None, TimeSpan.FromMilliseconds(100));

            if (tableName == "Aggregate_CLO_Metrics_EU" || tableName == "Aggregate_CLO_Metrics_US")
            {
                var isEnableLink = metadataMap[tableName].Columns.Where(x => x.Name == "Transaction").FirstOrDefault().EnableLink;
                _logger.LogInformation($"from table metadata service EnableLink: {isEnableLink}");
            }
            return metadataMap.TryGetValue(tableName, out var metadata)
            ? new TableMetadataModel
            {
                Title = metadata.Title,
                TableName = metadata.TableName,
                IsCLO = metadata.IsCLO,
                Columns = metadata.Columns.Where(c => IsIncludeHiddenColumns || !c.IsHidden).ToList(),
                TableType = metadata.TableType,
                Identifier = tableName
            }
            : new TableMetadataModel
            {
                Title = title,
                TableName = tableName,
                IsCLO = false,
                Columns = new List<ColumnMetadata>(),
                Identifier = tableName
            };
        }

        public List<TableMetadataModel> GetTableMetadataWithColumn()
        {
            List<TableMetadataModel> metadataList = metadataMap.Select(x => new TableMetadataModel
            {
                TableName = x.Value.TableName,
                Columns = x.Value.Columns,
                Title = x.Value.Title,
                TableType = x.Value.TableType,
                IsCLO = x.Value.IsCLO,
                Identifier = x.Key
            }).ToList();
            return metadataList;
        }
        public List<string> GetAllTableNameFromMetadata(bool IsCLO = false)
        {
            List<TableMetadataModel> metadataList = new List<TableMetadataModel>();
            metadataList = metadataMap.Select(x => x.Value).ToList();
            return metadataList.Where(x => x.IsCLO == IsCLO).Select(x => x.TableName).ToList();
        }
        public List<string> GetAllTableIdentifierFromMetadata(bool IsCLO = false)
        {
            List<TableMetadataModel> metadataList = new List<TableMetadataModel>();

            // Find keys where the IsActive property is true
            return  metadataMap
     .Where(x => x.Value.IsCLO == IsCLO)
     .Select(x => x.Key)
     .ToList();
        }
    }
}