﻿using API.Controllers.DashboardTracker;
using API.Helpers;
using Contract.PortfolioCompany;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Models;
using Microsoft.AspNetCore.Mvc;
using Moq;
using System.Security.Claims;

namespace DocumentCollection.UnitTest.Controller
{
    public class DashboardTrackerControllerTest
    {
        private readonly Mock<IDashboardTrackerService> _mockService;
        private readonly Mock<IHelperService> _mockHelperService;
        private readonly DashboardTrackerController _controller;

        public DashboardTrackerControllerTest()
        {
            _mockService = new Mock<IDashboardTrackerService>();
            _mockHelperService = new Mock<IHelperService>();
            _controller = new DashboardTrackerController(_mockService.Object, _mockHelperService.Object);
        }

        [Fact]
        public async Task GetCompanies_ReturnsOkResult_WithCompanies()
        {
            // Arrange
            var userId = 1;
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel { PortfolioCompanyID = 1, CompanyName = "Company A", FundName = "Fund A" },
                new PortfolioCompanyQueryModel { PortfolioCompanyID = 2, CompanyName = "Company B", FundName = "Fund B" }
            };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetPortfolioCompanies(It.IsAny<PortfolioCompanyFilter>())).ReturnsAsync(companies);

            // Act
            var result = await _controller.GetCompanies();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedCompanies = Assert.IsType<List<PortfolioCompanyQueryModel>>(okResult.Value);
            Assert.Equal(2, returnedCompanies.Count);
        }

        [Fact]
        public async Task SaveDashboardTrackerConfig_InvalidModelState_ReturnsInternalServerError()
        {
            // Arrange
            _controller.ModelState.AddModelError("Name", "Required");
            var dto = new DashboardTrackerConfigDto();

            // Act
            var result = await _controller.SaveDashboardTrackerConfig(dto);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic value = jsonResult.Value;
            Assert.NotNull(value);
        }

        [Fact]
        public async Task SaveDashboardTrackerConfig_Insert_ReturnsOkResult()
        {
            // Arrange
            var userId = 1;
            var dto = new DashboardTrackerConfigDto { FieldType = 1, DataType = 2, Name = "Test" };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.SaveDashboardTrackerConfigAsync(It.IsAny<DashboardTrackerConfigDto>())).ReturnsAsync(10);

            // Act
            var result = await _controller.SaveDashboardTrackerConfig(dto);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic value = jsonResult.Value;
            Assert.NotNull(value);            
            Assert.Contains("added", ((string)value.Message).ToLower());
            Assert.NotNull(value.Code);            
        }

        [Fact]
        public async Task SaveDashboardTrackerConfig_Update_ReturnsOkResult()
        {
            // Arrange
            var userId = 1;
            var dto = new DashboardTrackerConfigDto { ID = 5, FieldType = 1, DataType = 2, Name = "Test" };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.SaveDashboardTrackerConfigAsync(It.IsAny<DashboardTrackerConfigDto>())).ReturnsAsync(5);

            // Act
            var result = await _controller.SaveDashboardTrackerConfig(dto);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic value = jsonResult.Value;
            Assert.NotNull(value);            
            Assert.Contains("updated", ((string)value.Message).ToLower());
            Assert.NotNull(value.Code);            
        }

        [Fact]
        public async Task SaveDashboardTrackerConfig_ServiceReturnsZero_ReturnsInternalServerError()
        {
            // Arrange
            var userId = 1;
            var dto = new DashboardTrackerConfigDto { FieldType = 1, DataType = 2, Name = "Test" };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.SaveDashboardTrackerConfigAsync(It.IsAny<DashboardTrackerConfigDto>())).ReturnsAsync(0);

            // Act
            var result = await _controller.SaveDashboardTrackerConfig(dto);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic value = jsonResult.Value;
            Assert.NotNull(value);                        
        }

        [Fact]
        public async Task SaveDashboardTrackerConfig_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var userId = 1;
            var dto = new DashboardTrackerConfigDto { FieldType = 1, DataType = 2, Name = "Test" };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.SaveDashboardTrackerConfigAsync(It.IsAny<DashboardTrackerConfigDto>())).ThrowsAsync(new System.Exception("error"));

            // Act
            var result = await _controller.SaveDashboardTrackerConfig(dto);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic value = jsonResult.Value;
            Assert.NotNull(value);                        
        }

        [Fact]
        public async Task GetDashboardTableData_ReturnsOkResult_WithValidData()
        {
            // Arrange
            var userId = 1;
            var filter = new Contract.Utility.PaginationFilter { First = 0, Rows = 100 };
            var tableData = new TableDataResultDto { Success = true, Data = new List<Dictionary<string, object>>(), Columns = new List<ColumnsDto>() };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDashboardTableDataAsync(userId, filter)).ReturnsAsync(tableData);

            // Act
            var result = await _controller.GetDashboardTableData(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(tableData, okResult.Value);
        }

        [Fact]
        public async Task GetDashboardTableData_NullFilter_UsesDefault()
        {
            // Arrange
            var userId = 1;
            var tableData = new TableDataResultDto { Success = true, TotalRecords = 100, Columns = new List<ColumnsDto>(), Data = new List<Dictionary<string, object>>() };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDashboardTableDataAsync(userId, It.IsAny<Contract.Utility.PaginationFilter>())).ReturnsAsync(tableData);

            // Act
            var result = await _controller.GetDashboardTableData(null);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(tableData, okResult.Value);
        }

        [Fact]
        public async Task GetDashboardTableData_ServiceReturnsNull_ReturnsOkWithNull()
        {
            // Arrange
            var userId = 1;
            var filter = new Contract.Utility.PaginationFilter { First = 0, Rows = 100 };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(s => s.GetDashboardTableDataAsync(userId, filter)).ReturnsAsync((TableDataResultDto)null);

            // Act
            var result = await _controller.GetDashboardTableData(filter);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Null(okResult.Value);
        }

        [Fact]
        public async Task SaveDropdownValues_ValidDto_ReturnsOkResult()
        {
            // Arrange
            var dto = new TrackerDropdownValueDto { TrackerFieldId = 2, DropdownValues = new List<string> { "A", "B" } };
            _mockService.Setup(s => s.SaveTrackerDropdownValuesAsync(dto)).ReturnsAsync(true);

            // Act
            var result = await _controller.SaveDropdownValues(dto);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            // The returned value is an anonymous object: { success = true }
            var value = okResult.Value;
            Assert.NotNull(value);
            var successProperty = value.GetType().GetProperty("success");
            Assert.NotNull(successProperty);
            Assert.True((bool)successProperty.GetValue(value));
        }

        [Fact]
        public async Task SaveDropdownValues_NullDto_ReturnsBadRequest()
        {
            // Arrange
            TrackerDropdownValueDto dto = null;

            // Act
            var result = await _controller.SaveDropdownValues(dto);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task SaveDropdownValues_EmptyDropdownValues_ReturnsBadRequest()
        {
            // Arrange
            var dto = new TrackerDropdownValueDto { DropdownValues = new List<string>() };

            // Act
            var result = await _controller.SaveDropdownValues(dto);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task SaveDropdownValues_ServiceReturnsFalse_ReturnsInternalServerError()
        {
            // Arrange
            var dto = new TrackerDropdownValueDto { DropdownValues = new List<string> { "A" } };
            _mockService.Setup(s => s.SaveTrackerDropdownValuesAsync(dto)).ReturnsAsync(false);

            // Act
            var result = await _controller.SaveDropdownValues(dto);

            // Assert
            var objectResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, objectResult.StatusCode);
        }
        [Fact]
        public async Task GetAllTrackerConfigs_ReturnsOkResult_WithConfigs()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfigDto>
    {
        new DashboardTrackerConfigDto
        {
            ID = 1,
            FieldType = 1,
            FieldTypeName = "Data",
            DataType = 4,
            DataTypeName = "Dropdown",
            Name = "Test Field",
            StartPeriod = "2023",
            MapTo = 2,
            DropdownList = new List<string> { "A", "B" }
        },
        new DashboardTrackerConfigDto
        {
            ID = 2,
            FieldType = 2,
            FieldTypeName = "TimeSeries",
            DataType = 1,
            DataTypeName = "Text",
            Name = "Another Field",
            StartPeriod = "2022",
            MapTo = 3
        }
    };
            _mockService.Setup(s => s.GetAllTrackerConfigsAsync()).ReturnsAsync(configs);

            // Act
            var result = await _controller.GetAllTrackerConfigs();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedConfigs = Assert.IsType<List<DashboardTrackerConfigDto>>(okResult.Value);
            Assert.Equal(2, returnedConfigs.Count);
            Assert.Equal("Test Field", returnedConfigs[0].Name);
            Assert.Equal("Dropdown", returnedConfigs[0].DataTypeName);
            Assert.Equal("A", returnedConfigs[0].DropdownList[0]);
        }

        [Fact]
        public async Task GetAllTrackerConfigs_ReturnsOkResult_EmptyList()
        {
            // Arrange
            _mockService.Setup(s => s.GetAllTrackerConfigsAsync()).ReturnsAsync(new List<DashboardTrackerConfigDto>());

            // Act
            var result = await _controller.GetAllTrackerConfigs();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedConfigs = Assert.IsType<List<DashboardTrackerConfigDto>>(okResult.Value);
            Assert.Empty(returnedConfigs);
        }

       
    }
}
