// CLO/Helpers/TableDataHelper.cs
using Azure.Core;
using CLO.CQRS.Results;
using CLO.Helper;
using CLO.Models;
using CLO.Models.AuditLog;
using CLO.Services;
using CLO.Services.interfaces;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace CLO.Helpers
{
    public static class TableDataHelper
    {


        /// <summary>
        /// Gets table data based on metadata and company ID
        /// </summary>
        public static async Task<DataTable> GetTableData(string connectionString, TableMetadataModel metadata, string companyId)
        {
            var dataTable = new DataTable();

            using (var connection = new SqlConnection(connectionString))
            {
                string columnList = string.Join(", ", metadata.Columns.Select(c => c.Type == ColumnType.Date ? $"FORMAT([{c.Name}], '{Constants.QueryoutputDateformat}') as [{c.Name}]" : $"[{c.Name}]"));
                using (var command = new SqlCommand(Constants.GetDynamicTableRecordByCompanyIds, connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new SqlParameter("@ColumnList", columnList));
                    command.Parameters.Add(new SqlParameter("@TableName", metadata.TableName));
                    command.Parameters.Add(new SqlParameter("@CompanyID", companyId));
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(dataTable);
                    }
                }
            }

            return dataTable;
        }

        public static async Task<Dictionary<string, TableDataResult>> GetMultipleTablesDataAsync(string connectionString, List<TableMetadataModel> metadataList, List<string> columnsList = null)
        {
            var result = new Dictionary<string, TableDataResult>();

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                foreach (var metadata in metadataList)
                {
                    var dataTable = new DataTable();

                    // Use provided columns list or default to metadata columns
                    var columnsToUse = columnsList ?? metadata.Columns.Select(c => c.Name).ToList();

                    string columnList = string.Join(", ", columnsToUse.Select(colName =>
                    {
                        var column = metadata.Columns.FirstOrDefault(c => c.Name == colName);
                        return column?.Type == ColumnType.Date
                            ? $"FORMAT([{colName}], '{Constants.QueryoutputDateformat}') as [{colName}]"
                            : $"[{colName}]";
                    }));

                    using (var command = new SqlCommand(Constants.GetInvestmentCompanyAndCLOReportingRecords, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new SqlParameter("@ColumnList", columnList));
                        command.Parameters.Add(new SqlParameter("@TableName", metadata.TableName));
                        try
                        {
                            using (var adapter = new SqlDataAdapter(command))
                            {
                                adapter.Fill(dataTable);
                            }
                        }
                        catch (Exception exc)
                        {
                            string msg = exc.GetType().FullName + ": " + exc.Message;
                        }
                    }

                    result[metadata.TableName] = GetTableCloumnDataResult(dataTable,metadata);
                }
            }

            return result;
        }

        private static TableDataResult GetTableCloumnDataResult(DataTable dataTable, TableMetadataModel tableMetadata)
        {
            if (dataTable.Rows.Count == 0)
            {
                return new TableDataResult
                {
                    Success = true,
                    Data = new List<Dictionary<string, object>>(),
                    Columns = new List<ColumnInfo>()
                };
            }

            var tableData = dataTable.AsEnumerable().Select(row => tableMetadata.Columns
                    .ToDictionary(
                        col => GetUniqueFieldName(col, tableMetadata.TableName),
                        col => ParseColumnValue(row[col.Name], col)))
                .ToList();

       
            var tableColumns = tableMetadata.Columns
                    .Select(col => new ColumnInfo
                    {
                        Header = !string.IsNullOrEmpty(col.AliasName) ? col.AliasName : col.Name,
                        Field = GetUniqueFieldName(col, tableMetadata.TableName),
                        Parent = col.Parent,
                        EnableLink = col.EnableLink,
                        IsStaticTableHeader = col.IsStaticTableHeader,
                        ActualColumnName = col.Name,
                        IsUniqueIdentifier = col.IsUniqueIdentifier
                    })
                    .ToList();

            return new TableDataResult
            {
                Success = true,
                Data = tableData,
                Columns = tableColumns
            };
        }

        /// <summary>
        /// Parses a column value based on its type
        /// </summary>
        public static object ParseColumnValue(object value, ColumnMetadata column)
        {
            if (value == DBNull.Value) return null;

            return column.Type switch
            {
                //Display
                ColumnType.Alphanumeric => ParseAlphaNumeric(value),
                ColumnType.Number => ParseNumericValue(value, column),
                ColumnType.Percentage => ParsePercentageValue(value),
                ColumnType.Currency => ParseCurrencyValue(value, column),
                ColumnType.Date => ParseDateValue(value, column.DateFormat),
                _ => value
            };
        }

        private static string ParseNumericValue(object value, ColumnMetadata column)
        {
            if (column.IsWholeNumber)
            {
                return Math.Round(Convert.ToDecimal(value)).ToString();
            }
            else
                return Math.Round(Convert.ToDecimal(value), 2, MidpointRounding.AwayFromZero).ToString("F2");
        }

        private static object ParseAlphaNumeric(object value)
        {
            var convertedValue = value?.ToString() ?? string.Empty;
            if (decimal.TryParse(convertedValue, out decimal numericValues))
            {
                return Math.Round(numericValues, 2 , MidpointRounding.AwayFromZero).ToString("F2");
            }
            else
            {
                return convertedValue;
            }
        }
        /// <summary>
        /// Formats a currency value for display
        /// </summary>
        private static object ParseCurrencyValue(object value, ColumnMetadata column)
        {
            return CurrencyHelper.FormatCurrencyForDisplay(value.ToString(),
                column.DecimalUnits,
                column.CurrencyUnit);
        }

        /// <summary>
        /// Formats a date value for display
        /// </summary>
        private static object ParseDateValue(object value, string displayDateFormat)
        {
            if (string.IsNullOrEmpty(displayDateFormat))
                return value is DateTime date ? date.ToString(Constants.DateDisplayFormat_Table) : value;
            else
                return DateTime.TryParseExact(value.ToString(), Constants.QueryoutputDateformat, null, System.Globalization.DateTimeStyles.None, out DateTime date) ? date.ToString(displayDateFormat) : value;
        }

        /// <summary>
        /// Parses a percentage value for display
        /// </summary>
        private static object ParsePercentageValue(object value)
        {
            string percentageValue = value.ToString().Replace("%", "");
            return Math.Round(decimal.Parse(percentageValue), 2, MidpointRounding.AwayFromZero).ToString("F2") + "%";
        }

        /// <summary>
        /// Generates a unique field name for a column
        /// </summary>
        public static string GetUniqueFieldName(ColumnMetadata col, string tableName)
        {
            if (string.IsNullOrEmpty(col.Parent) || string.IsNullOrEmpty(col.AliasName))
                return col.Name;

            return $"{col.Parent}_{col.AliasName}"
                .Replace(" ", "_")
                .Replace(",", "");
        }


        /// <summary>
        /// Converts any object to TableDataResult using reflection
        /// </summary>
        public static TableDataResult ConvertObjectToTableDataResult<T>(T obj, string tableName = "Data") where T : class
        {
            if (obj == null)
            {
                return new TableDataResult
                {
                    Success = false,
                    Data = new List<Dictionary<string, object>>(),
                    Columns = new List<ColumnInfo>()
                };
            }

            try
            {
                var properties = typeof(T).GetProperties();
                var data = new Dictionary<string, object>();
                var columns = new List<ColumnInfo>();

                foreach (var property in properties)
                {
                    var value = property.GetValue(obj);
                    data[property.Name] = value ?? DBNull.Value;

                    // Create column info
                    columns.Add(new ColumnInfo
                    {
                        Header = property.Name.Replace("_", " "), // Convert property name to display name
                        Field = property.Name,
                        ActualColumnName = property.Name
                    });
                }

                var tableDataResult = new TableDataResult
                {
                    Success = true,
                    Data = new List<Dictionary<string, object>> { data },
                    Columns = columns
                };
                return tableDataResult;
            }
            catch (Exception ex)
            {
                return new TableDataResult
                {
                    Success = false,
                    Data = new List<Dictionary<string, object>>(),
                    Columns = new List<ColumnInfo>()
                };
            }
        }


    }
}