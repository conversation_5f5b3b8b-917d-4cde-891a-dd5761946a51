using Contract.PortfolioCompany;
using Contract.Repository;
using Contract.Utility;
using DataAccessLayer.Models.DashboardTracker;
using DataAccessLayer.Models.Tracker;
using DataAccessLayer.UnitOfWork;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Helpers;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Models;
using PortfolioCompany.Helper;
using Workflow;

namespace DocumentCollection.DashboardTracker.Services
{
    public class DashboardTrackerService : IDashboardTrackerService
    {
        private readonly IWorkflowPCService _workflowPCService;
        private readonly IFileService _fileService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly int DEFAULT_PAGE_NUMBER = 1;
        private readonly int DEFAULT_PAGE_SIZE = 100;

        public DashboardTrackerService(IWorkflowPCService workflowPCService, IFileService fileService, IUnitOfWork unitOfWork)
        {
            _workflowPCService = workflowPCService;
            _fileService = fileService;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PortfolioCompanyQueryModel>> GetPortfolioCompanies(PortfolioCompanyFilter portfolioCompanyFilter)
        {
            var workflowResult = await _workflowPCService.GetPortfolioCompanies(portfolioCompanyFilter);

            if (workflowResult == null || workflowResult.PortfolioCompanyQueryListModel == null || workflowResult.PortfolioCompanyQueryListModel.PortfolioCompanyList.Count == 0)
            {
                return new List<PortfolioCompanyQueryModel>();
            }     
            return workflowResult.PortfolioCompanyQueryListModel.PortfolioCompanyList;
        }

        private async Task<Dictionary<int,string>> Fetchlogosforcompanies(IEnumerable<PortfolioCompanyQueryModel> portfolioCompanies)
        {
            var companiesToFetchLogo = portfolioCompanies.Take(50).ToList();
            Dictionary<int, string> result = new Dictionary<int, string>();
            foreach (var viewModel in companiesToFetchLogo)
            {
                var res = await _unitOfWork.PortfolioCompanyDetailRepository.FindFirstAsync(x => x.PortfolioCompanyId == viewModel.PortfolioCompanyID);
                if (res != null && !result.ContainsKey(viewModel.PortfolioCompanyID))
                {
                    var imgPath = await PortfolioCompanyHelper.GetCompanyLogo(_fileService, viewModel.PortfolioCompanyID, res.ImagePath);
                    result.Add(viewModel.PortfolioCompanyID,imgPath);
                }
            }

            return result;
        }

        public async Task<int> SaveDashboardTrackerConfigAsync(DashboardTrackerConfigDto dto)
        {
            var entity = new DashboardTrackerConfig
            {
                FieldType = dto.FieldType,
                DataType = dto.DataType,
                Name = dto.Name,
                FrequencyType = dto.FrequencyType,
                StartPeriod = dto.StartPeriod,
                EndPeriod = dto.EndPeriod,
                IsPrefix = dto.IsPrefix,
                TimeSeriesDateFormat = dto.TimeSeriesDateFormat,
                MapTo = dto.MapTo == 0 || dto.MapTo == null ? null : (MapWith?)dto.MapTo,
                IsActive = dto.IsActive,
                IsDeleted = dto.IsDeleted,
            };

            if (dto.ID.HasValue && dto.ID.Value > 0)
            {
                // Update existing record
                entity.ModifiedOn = dto.ModifiedOn;
                entity.ModifiedBy = dto.ModifiedBy;
                entity.ID = dto.ID.Value;
                _unitOfWork.DashboardTrackerConfigRepository.Update(entity);
            }
            else
            {
                // Insert new record
                entity.CreatedBy = dto.CreatedBy ?? 0;
                entity.CreatedOn = dto.CreatedOn ?? DateTime.UtcNow;
                _unitOfWork.DashboardTrackerConfigRepository.Insert(entity);
            }
            await _unitOfWork.SaveAsync();
            return entity.ID;
        }

        /// <summary>
        /// Gets all active dashboard tracker configurations and generates corresponding ColumnsDto array
        /// </summary>
        /// <returns>List of ColumnsDto objects</returns>
        public async Task<List<ColumnsDto>> GetDashboardColumnsAsync()
        {
            var configs = await _unitOfWork.DashboardTrackerConfigRepository.FindAllAsync(x => x.IsActive && !x.IsDeleted);
            return DashboardTrackerHelper.GenerateColumnsDtoArray(configs);
        }

        public async Task<TableDataResultDto> GetDashboardTableDataAsync(int userId, PaginationFilter filter)
        {
            var companiesAll = await GetPortfolioCompanies(new PortfolioCompanyFilter { CreatedBy = userId });

            int pageNumber = DEFAULT_PAGE_NUMBER;
            int pageSize = DEFAULT_PAGE_SIZE;
            if (filter != null)
            {
                // PrimeNG/other UI frameworks often use First/Rows for pagination
                pageSize = filter.Rows > 0 ? filter.Rows : 100;
                pageNumber = filter.First > 0 ? (filter.First / pageSize) + 1 : 1;
            }
            int totalRecords;
            var companies = ApplyPagination(companiesAll, pageNumber, pageSize, out totalRecords);

            var imagedata = await Fetchlogosforcompanies(companies);
            var columnsDto = await GetDashboardColumnsAsync();
            List<ColumnsDto> columns = AddDefaultColumns();
            columns.AddRange(columnsDto);
            Dictionary<int, List<string>> dropdownValuesDict = await GetDropdownValuesforColumn(columnsDto);

            var data = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, dropdownValuesDict);

            return new TableDataResultDto
            {
                Success = true,
                Data = data,
                Columns = columns,
                TotalRecords = totalRecords
            };
        }

        /// <summary>
        /// Paginates a list based on page number and page size.
        /// </summary>
        private static List<T> ApplyPagination<T>(IEnumerable<T> source, int pageNumber, int pageSize, out int totalRecords)
        {
            if (pageNumber < 1) pageNumber = 1;
            if (pageSize < 1) pageSize = 100;
            var list = source.ToList();
            totalRecords = list.Count;
            return list.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
        }

        private static List<ColumnsDto> AddDefaultColumns()
        {
            return new List<ColumnsDto>
            {
                new() { Name = "Fund Name", DataType = 0, FieldType = 0, IsTimeSeries = false, TimeSeriesID = string.Empty, IsDropDown = false },
                new() { Name = "Portfolio Company Name", DataType = 0, FieldType = 0, IsTimeSeries = false, TimeSeriesID = string.Empty, IsDropDown = false },
                new() { Name = "CompanyLogo", DataType = 0, FieldType = 0, IsTimeSeries = false, TimeSeriesID = string.Empty, IsDropDown = false }
            };
        }

        private async Task<Dictionary<int, List<string>>> GetDropdownValuesforColumn(List<ColumnsDto> columnsDto)
        {
            var dropdownValuesDict = new Dictionary<int, List<string>>();
            var dataType4Columns = columnsDto.Where(col => col.DataType == 4).ToList();
            foreach (var col in dataType4Columns)
            {
                // Fetch dropdown values for each DataType 4 column (if needed)
                var dropdownValues = await _unitOfWork.TrackerDropdownValueRepository.FindAllAsync(x => x.TackerFieldConfigId == col.ID);
                if (dropdownValues != null && dropdownValues.Any())
                {
                    dropdownValuesDict[col.ID] = dropdownValues.Select(x => x.DropdownValue).ToList();
                }
                else
                {
                    dropdownValuesDict[col.ID] = new List<string>();
                }
            }

            return dropdownValuesDict;
        }

        public async Task<bool> SaveTrackerDropdownValuesAsync(TrackerDropdownValueDto dto)
        {
            if (dto == null || dto.DropdownValues == null || dto.DropdownValues.Count == 0)
                return false;

            var now = DateTime.UtcNow;
            var entities = dto.DropdownValues.Select(val => new TrackerDropdownValue
            {
                TackerFieldConfigId = dto.TrackerFieldId,
                DropdownValue = val,
                CreatedOn = now
            }).ToList();

            await _unitOfWork.TrackerDropdownValueRepository.AddBulkAsyn(entities);

            await _unitOfWork.SaveAsync();

            return true;
        }

        public async Task<List<DashboardTrackerConfigDto>> GetAllTrackerConfigsAsync()
        {
            var configs = await _unitOfWork.DashboardTrackerConfigRepository.FindAllAsync(x => !x.IsDeleted);
            var configDtos = configs.Select(config => new DashboardTrackerConfigDto
            {
                ID = config.ID,
                FieldType = config.FieldType,
                FieldTypeName = Enum.GetName(typeof(FieldTypeEnum), config.FieldType),
                DataType = config.DataType,
                DataTypeName = Enum.GetName(typeof(DataTypeEnum), config.DataType),
                Name = config.Name,
                StartPeriod = config.StartPeriod,
                MapTo = config.MapTo.HasValue ? (int)config.MapTo.Value : 0,
                CreatedOn = config.CreatedOn,
            }).ToList();

            // Get all dropdown config IDs (non-nullable)
            var dropdownConfigIds = configDtos
                .Where(x => x.DataType == (int)DataTypeEnum.Dropdown && x.ID.HasValue)
                .Select(x => x.ID.Value)
                .ToList();

            if (dropdownConfigIds.Count > 0)
            {
                // Fetch all dropdown values for these config IDs in a single query
                var allDropdowns = await _unitOfWork.TrackerDropdownValueRepository
                    .FindAllAsync(x => dropdownConfigIds.Contains(x.TackerFieldConfigId));

                // Group dropdown values by config ID
                var dropdownsByConfig = allDropdowns
                    .GroupBy(d => d.TackerFieldConfigId)
                    .ToDictionary(g => g.Key, g => g.Select(d => d.DropdownValue).ToList());

                // Assign dropdown lists to each DTO
                foreach (var dto in configDtos.Where(x => x.DataType == (int)DataTypeEnum.Dropdown && x.ID.HasValue))
                {
                    if (dropdownsByConfig.TryGetValue(dto.ID.Value, out var dropdownList))
                        dto.DropdownList = dropdownList;
                    else
                        dto.DropdownList = new List<string>();
                }
            }

            return configDtos;
        }
    }
}
