﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contract.PortfolioCompany;
using DapperRepository;
using Microsoft.Extensions.Logging;
using Moq;
using PortfolioCompany.Interfaces;
using PortfolioCompany.Services;
using Xunit;
using Contract.Account;
using Shared;

namespace PortfolioCompany.UnitTest.Tests.Services
{
    public class SubFeatureAccessServiceTests
    {
        private readonly Mock<ILogger<SubFeatureAccessService>> _loggerMock;
        private readonly Mock<IDapperGenericRepository> _dapperRepositoryMock;
        private readonly ISubFeatureAccessService _subFeatureAccessService;

        public SubFeatureAccessServiceTests()
        {
            _loggerMock = new Mock<ILogger<SubFeatureAccessService>>();
            _dapperRepositoryMock = new Mock<IDapperGenericRepository>();
            _subFeatureAccessService = new SubFeatureAccessService(_loggerMock.Object, _dapperRepositoryMock.Object);
        }

        [Fact]
        public async Task GetSubFeatureAccessPermissions_WithValidInputs_ReturnsPermissions()
        {
            // Arrange
            int userId = 1;
            int companyId = 1;
            int featureId = 1;
            int moduleId = 0;
            var expectedPermissions = new List<SubFeatureAccessPermissionsModel> {
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Kpi1",
                    ModuleId = 1,
                    CanAdd = true,
                    CanExport = true,
                    CanEdit = true,
                    CanImport = true,
                    CanView = true,
                    CompanyId = 2,
                    GroupId = 3,
                    IsActive = true
                },
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Kpi2",
                    ModuleId = 2,
                    CanAdd = true,
                    CanExport = true,
                    CanEdit = true,
                    CanImport = true,
                    CanView = true,
                    CompanyId = 2,
                    GroupId = 3,
                    IsActive = true
                }
            };
            _dapperRepositoryMock.Setup(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(expectedPermissions);

            // Act
            var result = await _subFeatureAccessService.GetSubFeatureAccessPermissions(userId, companyId, featureId, moduleId);

            // Assert
            Assert.Equal(expectedPermissions, result);
        }

        [Fact]
        public async Task GetSubFeatureAccessPermissions_WithInvalidCompanyId_ReturnsEmptyList()
        {
            // Arrange
            int userId = 1;
            int companyId = 0;
            int featureId = 1;
            int moduleId = 0;

            // Act
            var result = await _subFeatureAccessService.GetSubFeatureAccessPermissions(userId, companyId, featureId, moduleId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetSubFeatureAccessPermissions_WithInvalidUserId_ReturnsEmptyList()
        {
            // Arrange
            int userId = 0;
            int companyId = 1;
            int featureId = 1;
            int moduleId = 0;

            // Act
            var result = await _subFeatureAccessService.GetSubFeatureAccessPermissions(userId, companyId, featureId, moduleId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetSubFeatureAccessPermissions_WithModuleId_ReturnsFilteredPermissions()
        {
            // Arrange
            int userId = 1;
            int companyId = 1;
            int featureId = 1;
            int moduleId = 2;
            var expectedPermissions = new List<SubFeatureAccessPermissionsModel> {
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Kpi1",
                    ModuleId = 1,
                    CanAdd = true,
                    CanExport = true,
                    CanEdit = true,
                    CanImport = true,
                    CanView = true,
                    CompanyId = 2,
                    GroupId = 3,
                    IsActive = true
                },
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Kpi2",
                    ModuleId = 2,
                    CanAdd = true,
                    CanExport = true,
                    CanEdit = true,
                    CanImport = true,
                    CanView = true,
                    CompanyId = 2,
                    GroupId = 3,
                    IsActive = true
                }
            };
            _dapperRepositoryMock.Setup(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(expectedPermissions);

            // Act
            var result = await _subFeatureAccessService.GetSubFeatureAccessPermissions(userId, companyId, featureId, moduleId);

            // Assert
            Assert.Single(result);
            Assert.Equal(2, result[0].ModuleId);
            Assert.Equal("Kpi2", result[0].SubFeature);
        }

        // New tests for GetFundSubFeatureAccessPermissions

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithInvalidUserId_ReturnsFalse()
        {
            // Arrange
            int userId = 0;
            int fundId = 1;
            string kpiType = Constants.FundFinancials;

            // Act
            var result = await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType);

            // Assert
            Assert.False(result);
            _dapperRepositoryMock.Verify(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()), Times.Never);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithInvalidFundId_ReturnsFalse()
        {
            // Arrange
            int userId = 1;
            int fundId = 0;
            string kpiType = Constants.FundFinancials;

            // Act
            var result = await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType);

            // Assert
            Assert.False(result);
            _dapperRepositoryMock.Verify(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()), Times.Never);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithNoPermissions_ReturnsFalse()
        {
            // Arrange
            int userId = 1;
            int fundId = 1;
            string kpiType = Constants.FundFinancials;
            var permissions = new List<SubFeatureAccessPermissionsModel>();

            _dapperRepositoryMock.Setup(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(permissions);

            // Act
            var result = await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType);

            // Assert
            Assert.False(result);
            _dapperRepositoryMock.Verify(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), 
                It.Is<object>(o => o.GetType().GetProperty("UserId").GetValue(o).Equals(userId) && 
                               o.GetType().GetProperty("FundId").GetValue(o).Equals(fundId) && 
                               o.GetType().GetProperty("FeatureId").GetValue(o).Equals((int)Features.Fund))), 
                Times.Once);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithFundFinancialsPermission_ReturnsTrue()
        {
            // Arrange
            int userId = 1;
            int fundId = 1;
            string kpiType = Constants.FundFinancials;
            var permissions = new List<SubFeatureAccessPermissionsModel>
            {
                new SubFeatureAccessPermissionsModel
                {
                    SubFeature = "Fund Financials",
                    ModuleId = 1001,
                    CanImport = true,
                    IsActive = true
                }
            };

            _dapperRepositoryMock.Setup(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(permissions);

            // Act
            var result = await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithFundFinancialsPermissionButNoImport_ReturnsFalse()
        {
            // Arrange
            int userId = 1;
            int fundId = 1;
            string kpiType = Constants.FundFinancials;
            var permissions = new List<SubFeatureAccessPermissionsModel>
            {
                new SubFeatureAccessPermissionsModel
                {
                    SubFeature = "Fund Financials",
                    ModuleId = 1001,
                    CanImport = false,
                    IsActive = true
                }
            };

            _dapperRepositoryMock.Setup(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(permissions);

            // Act
            var result = await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithFundKpisPermission_ReturnsTrue()
        {
            // Arrange
            int userId = 1;
            int fundId = 1;
            string kpiType = Constants.FundKpis;
            var permissions = new List<SubFeatureAccessPermissionsModel>
            {
                new SubFeatureAccessPermissionsModel
                {
                    SubFeature = "Fund KPI",
                    ModuleId = 1009,
                    CanImport = true,
                    IsActive = true
                }
            };

            _dapperRepositoryMock.Setup(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(permissions);

            // Act
            var result = await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithFundKpisPermissionButNoImport_ReturnsFalse()
        {
            // Arrange
            int userId = 1;
            int fundId = 1;
            string kpiType = Constants.FundKpis;
            var permissions = new List<SubFeatureAccessPermissionsModel>
            {
                new SubFeatureAccessPermissionsModel
                {
                    SubFeature = "Fund KPI",
                    ModuleId = 1009,
                    CanImport = false,
                    IsActive = true
                }
            };

            _dapperRepositoryMock.Setup(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(permissions);

            // Act
            var result = await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithUnknownKpiType_ReturnsFalse()
        {
            // Arrange
            int userId = 1;
            int fundId = 1;
            string kpiType = "UnknownKpiType";
            var permissions = new List<SubFeatureAccessPermissionsModel>
            {
                new SubFeatureAccessPermissionsModel
                {
                    SubFeature = "Fund KPI",
                    ModuleId = 1009,
                    CanImport = true,
                    IsActive = true
                }
            };

            _dapperRepositoryMock.Setup(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(permissions);

            // Act
            var result = await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithModuleIdZero_ReturnsFalse()
        {
            // Arrange
            int userId = 1;
            int fundId = 1;
            string kpiType = Constants.FundFinancials;
            var permissions = new List<SubFeatureAccessPermissionsModel>
            {
                new SubFeatureAccessPermissionsModel
                {
                    SubFeature = "Fund Financials",
                    ModuleId = 0, // Zero module ID
                    CanImport = true,
                    IsActive = true
                }
            };

            _dapperRepositoryMock.Setup(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(permissions);

            // Act
            var result = await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithNullResult_ReturnsFalse()
        {
            // Arrange
            int userId = 1;
            int fundId = 1;
            string kpiType = Constants.FundFinancials;

            _dapperRepositoryMock.Setup(x => x.Query<SubFeatureAccessPermissionsModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync((List<SubFeatureAccessPermissionsModel>)null);

            // Act
            var result = await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType);

            // Assert
            Assert.False(result);
        }
    }
}
