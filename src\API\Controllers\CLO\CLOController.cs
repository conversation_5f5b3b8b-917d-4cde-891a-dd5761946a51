﻿using API.Filters;
using API.Filters.CustomAuthorization;
using API.Helpers;
using CLO.CQRS.Commands;
using CLO.CQRS.Queries;
using CLO.Models;
using CLO.Services.interfaces;
using Contract.Account;
using Contract.Utility;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace API.Controllers.CLO
{
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    [Route("api/v1/[controller]")]
    public class CLOController : BaseController
    {
        private readonly ICLOService _cloService;
        private readonly IMediator _mediator;

        public CLOController(ICLOService cloService, IInjectedParameters injectedParameters, IHelperService helperService, IMediator mediator)
            : base(injectedParameters, helperService)
        {
            _cloService = cloService ?? throw new ArgumentNullException(nameof(cloService));
            _mediator = mediator;
        }
        /// <summary>
        /// get investment company data for clo page..
        /// </summary>
        /// <returns></returns>
        [HttpGet("investment-company/get")]
        [AuthorizeUserPermission(Features.CLOPage, Actions.canView)]
        public async Task<IActionResult> GetInvestmentCompanies()
        {
            var result = await _mediator.Send(new GetInvestmentCompaniesQuery());
            return result.Any() ? Ok(result) : NoContent();
        }

        /// <summary>
        /// Saves a CLO details.
        /// </summary>
        /// <param name="command">The command containing the CLO details to be saved.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains an <see cref="IActionResult"/> with the saved CLO details.</returns>
        [HttpPost("clo-details/add")]
        [AuthorizeUserPermission(Features.CLOPage, Actions.canAdd)]
        public async Task<IActionResult> SaveCLO([FromBody] SaveCloDetails command)
        {
            if (command == null)
                return BadRequest("CLO model cannot be null.");

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var validatedCommand = validateSaveCloCommand(command);
            var result = await _mediator.Send(validatedCommand);

            return Ok(result);
        }

        /// <summary>
        /// Retrieves a list of CLOs for a specific investment company.
        /// </summary>
        /// <param name="investmentCompanyID">The ID of the investment company.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains an <see cref="IActionResult"/> with the list of CLOs.</returns>
        [HttpGet("clo-details/get/{investmentCompanyID}")]
        [UserFeatureAuthorize((int)Features.CLOPage, (int)Features.CLOPageConfig)]
        public async Task<IActionResult> GetCLOList(int investmentCompanyID)
        {
            var clolistQuery = ValidatedCloListQuery(investmentCompanyID);
            var result = await _mediator.Send(clolistQuery);

            return Ok(result);
        }

        /// <summary>
        /// Deletes a CLO by its unique ID.
        /// </summary>
        /// <param name="cloUniqueID">The unique ID of the CLO to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains an <see cref="IActionResult"/> with the deletion result.</returns>
        [HttpDelete("clo-details/delete/{cloUniqueID}")]
        [AuthorizeUserPermission(Features.CLOPage, Actions.canEdit)]
        public async Task<IActionResult> DeleteCLO(string cloUniqueID)
        {
            if (string.IsNullOrEmpty(cloUniqueID))
            {
                return BadRequest("CLO unique ID cannot be null or empty.");
            }

            var result = await _mediator.Send(new DeleteCLOCommand
            {
                CloUniqueID = cloUniqueID,
                ConnectionString = AwsSecretsManagerHelper.GetConnectionString(),
                IsCLO = true
            });

            return Ok(result);
        }

        /// <summary>
        /// Retrieves a CLO by its unique ID.
        /// </summary>
        /// <param name="uniqueId">The unique ID of the CLO.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains an <see cref="IActionResult"/> with the retrieved <see cref="CLOModel"/>.</returns>        
        [HttpGet("clo-details/get-record/{cloUniqueID}")]
        [AuthorizeUserPermission(Features.CLOPage, Actions.canView)]
        public async Task<IActionResult> GetCLOById(string cloUniqueID)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var cloquery = ValidatedCLOQuery(cloUniqueID);
            var result = await _mediator.Send(cloquery);

            return result != null ? Ok(result) : NoContent();
        }

        /// <summary>
        /// Retrieves CLO details by company ID and issuer.
        /// </summary>
        [HttpGet("clo-search/{companyId}/issuer/{issuer}")]
        [AuthorizeUserPermission(Features.CLOPage, Actions.canView)]
        public async Task<IActionResult> GetCloDetailsByCompanyAndIssuer(int companyId, string issuer)
        {
            var query = new GetCloByCompanyAndIssuerQuery { CompanyId = companyId, Issuer = issuer };
            var cloDetails = await _mediator.Send(query);

            if (cloDetails == null || !cloDetails.Any())
            {
                return NotFound("CLO not found");
            }

            return Ok(cloDetails);
        }

        [AuthorizeUserPermission(Features.CLOPage, Actions.canView)]
        [HttpGet("get/{pageId}/{companyId}/{cloId}")]
        public async Task<IActionResult> GetTabDetailsByPageDetailsName(int pageId, int companyId = 0, int cloId = 0)
        {
            var result = await _mediator.Send(new GetTabDetailsByPageDetailsNameQuery
            {
                pageId = pageId,
                companyId = companyId,
                cloId = cloId
            }
                );
            return result != null ? Ok(result) : NoContent();
        }

        private GetCLOByIdQuery ValidatedCLOQuery(string id)
        {
            return new GetCLOByIdQuery() { UniqueID = id };
        }

        private GetCLOsListQuery ValidatedCloListQuery(int investmentCompanyID)
        {
            return new GetCLOsListQuery() { CompanyID = investmentCompanyID };
        }

        private SaveCloDetailsCommand validateSaveCloCommand(SaveCloDetails command)
        {
            SaveCloDetailsCommand obj = new SaveCloDetailsCommand();
            obj.CLO_ID = command.CLO_ID;
            obj.CompanyName = command.CompanyName;
            obj.CompanyID = command.CompanyID;
            obj.Domicile = command.Domicile;
            obj.Issuer = command.Issuer;
            obj.Arranger = command.Arranger;
            obj.Trustee = command.Trustee;
            obj.Priced = command.Priced;
            obj.Closed = command.Closed;
            obj.LastRefiDate = command.LastRefiDate;
            obj.LastResetDate = command.LastResetDate;
            obj.CallEndDate = command.CallEndDate;
            obj.OriginalEndOfReinvestmentDate = command.OriginalEndOfReinvestmentDate;
            obj.CurrentEndOfReinvestmentDate = command.CurrentEndOfReinvestmentDate;
            obj.CurrentMaturityDate = command.CurrentMaturityDate;
            obj.UniqueID = command.UniqueID;
            obj.CreatedBy = GetCurrentUserId();
            obj.CreatedOn = DateTime.UtcNow;
            obj.LastRefiResetArranger = command.LastRefiResetArranger;
            return obj;
        }

        /// <summary>
        /// Deletes a CLO  table by its unique ID.
        /// </summary>
        /// <param name="cloUniqueID">The unique ID of the CLO to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains an <see cref="IActionResult"/> with the deletion result.</returns>
        [HttpDelete("clo-details/deleteTable/{cloUniqueID}/{tableName}")]
        [AuthorizeUserPermission(Features.CLOPage, Actions.canEdit)]
        public async Task<IActionResult> DeleteCLOTable(string cloUniqueID,string tableName)
        {
            if (string.IsNullOrEmpty(cloUniqueID))
            {
                return BadRequest("CLO unique ID cannot be null or empty.");
            }

            var result = await _mediator.Send(new DeleteCLOCommand
            {
                CloUniqueID = cloUniqueID,
                ConnectionString = AwsSecretsManagerHelper.GetConnectionString(),
                TableName = tableName,
                IsCLO = true
            });

            return Ok(result);
        }

        /// <summary>
        /// BFB-10973
        /// </summary>
        /// <param name="cloUniqueID"></param>
        /// <returns></returns>
        [HttpGet("reporting-data")]
        [AuthorizeUserPermission(Features.CLOPage, Actions.canView)]
        public async Task<IActionResult> GetInvestmentCompanyCLODetails()
        {
            var result = await _mediator.Send(new GetInvestmentCompaniesAndCLODetailsQuery{
                ConnectionString = AwsSecretsManagerHelper.GetConnectionString()
            });
            return result.Data.Any() ? Ok(result) : NoContent();
        }
    }
}
