﻿using API.Filters.CustomAuthorization;
using API.Helpers;
using Contract.Account;
using Contract.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PortfolioCompany.Interfaces;
using System;
using System.Threading.Tasks;

namespace API.Controllers.PortfolioCompany
{
    [Route("api")]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    [ApiController]
    public class SubFeatureAccessController(
        ILogger<SubFeatureAccessController> logger,
        ISubFeatureAccessService subFeatureAccessService,
        IHelperService helperService,
        IEncryption encryption) : ControllerBase
    {
        private readonly ILogger<SubFeatureAccessController> _logger = logger;
        private readonly ISubFeatureAccessService _subFeatureAccessService = subFeatureAccessService;
        private readonly IHelperService _helperService = helperService;
        private readonly IEncryption _encryption = encryption;

        /// <summary>
        /// Retrieves the portfolio company subfeature access.
        /// </summary>
        /// <param name="encryptedCompanyId">The incrypted ID of the Company.</param>
        /// <param name="featureId">The ID of the Feature.</param>
        /// <param name="moduleId">The ID of the Module.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains an <see cref="IActionResult"/> object.</returns>
        [HttpGet("get-pc-subfeature-permissions/{encryptedCompanyId}/{featureId}/{moduleId}")]
        public async Task<IActionResult> GetSubFeatureAccessPermissions(string encryptedCompanyId, int featureId, int moduleId = 0)
        {
            _logger.LogInformation("Getting sub feature accesses");
            _ = int.TryParse(_encryption.Decrypt(encryptedCompanyId), out int companyId);
            int userId = _helperService.GetCurrentUserId(User);
            return Ok(await _subFeatureAccessService.GetSubFeatureAccessPermissions(userId, companyId, featureId, moduleId));
        }
        /// <summary>
        /// get fund permissions
        /// </summary>
        /// <param name="encryptedCompanyId"></param>
        /// <param name="featureId"></param>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        [HttpGet("fund-subfeature-permissions/{encryptedFundId}/{kpiType}")]
        public async Task<IActionResult> GetFundSubFeatureAccessPermissions(string encryptedFundId, int featureId,string kpiType)
        {
            _logger.LogInformation("Getting sub feature accesses");
            _ = int.TryParse(_encryption.Decrypt(encryptedFundId), out int fundId);
            int userId = _helperService.GetCurrentUserId(User);
            return Ok(await _subFeatureAccessService.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType));
        }

        [HttpGet("subFeature-permissions/{encryptedId}/{featureId}")]
        [UserFeatureAuthorize((int)Features.Investors, (int)Features.Deal, (int)Features.Fund, (int)Features.PortfolioCompany, (int)Features.Admin, (int)Features.Firm,
            (int)Features.BulkUpload, (int)Features.ESG, (int)Features.PageConfig, (int)Features.Pipeline, (int)Features.Repository, (int)Features.CLOPage, (int)Features.InvestmentCompany)]
        public async Task<IActionResult> GetCommonSubFeatureAccessPermissions(string encryptedId, int featureId)
        {
            int finalId;
            if (int.TryParse(encryptedId, out int tempId))
            {
                finalId = tempId;
            }
            else if (int.TryParse(_encryption.Decrypt(encryptedId), out int decryptedId))
            {
                finalId = decryptedId;
            }
            else
            {
                return BadRequest("Invalid ID format.");
            }

            var userId = _helperService.GetCurrentUserId(User);
            var permissions = await _subFeatureAccessService.GetAllSubFeatureAccessPermissions(userId, finalId, featureId);

            return Ok(permissions);
        }
        [HttpGet("feature-permissions/{featureId}")]
        [UserFeatureAuthorize((int)Features.Investors, (int)Features.Deal, (int)Features.Fund, (int)Features.PortfolioCompany, (int)Features.Admin, (int)Features.Firm,
            (int)Features.BulkUpload, (int)Features.ESG, (int)Features.PageConfig, (int)Features.Pipeline, (int)Features.Repository, (int)Features.CLOPage, (int)Features.InvestmentCompany)]
        public async Task<IActionResult> GetCommonFeatureAccessPermissions(int featureId)
        {
            var result = await _subFeatureAccessService.GetAllFeatureAccessPermissions(_helperService.GetCurrentUserId(User), featureId);
            return Ok(result);
        }
    }
}