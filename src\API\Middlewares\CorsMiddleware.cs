﻿using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Utility.Resource;
using Microsoft.Extensions.Logging;
using System;

namespace API.Middlewares;
public class CorsMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<CorsMiddleware> logger;

    public CorsMiddleware(RequestDelegate next, ILogger<CorsMiddleware> _logger)
    {
        _next = next;
        logger = _logger;

    }
    public async Task Invoke(HttpContext context, IConfiguration configuration)
    {
        var isUploadDocumentsEndpoint = context.Request.Path.Value?.Contains("upload-documents", StringComparison.OrdinalIgnoreCase) == true
            && (context.Request.Method.Equals("POST", StringComparison.OrdinalIgnoreCase)
                || context.Request.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase));

        if (isUploadDocumentsEndpoint)
        {
            logger.LogInformation("UploadDocuments endpoint hit. Path: {Path}, Method: {Method}, Origin: {Origin}",
                context.Request.Path, context.Request.Method, context.Request.Headers["Origin"].ToString());

            if (context.Request.Headers["Origin"] == "null" || context.Request.Headers["Allow-Origin"] == "null")
            {
                logger.LogInformation("Forbidden: Invalid Origin or Allow-Origin header for upload-documents. Path: {Path}, Origin: {Origin}",
                    context.Request.Path, context.Request.Headers["Origin"]);
                await HandleExceptionAsync(context);
                return;
            }

            var allowedDomains = configuration.GetValue<string>("AllowedOrigins")?.Split(',');
            var origin = context.Request.Headers["Origin"].FirstOrDefault();
            if (origin != null && !allowedDomains.Any(x => x.ToLower() == origin.ToLower()))
            {
                logger.LogInformation("Forbidden: Origin not allowed for upload-documents. Path: {Path}, Origin: {Origin}, AllowedDomains: {AllowedDomains}",
                    context.Request.Path, origin, string.Join(",", allowedDomains ?? new string[0]));
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                return;
            }
        }
        else
        {
            if (context.Request.Headers["Origin"] == "null" || context.Request.Headers["Allow-Origin"] == "null")
            {
                await HandleExceptionAsync(context);
                return;
            }
            var allowedDomains = configuration.GetValue<string>("AllowedOrigins")?.Split(',');
            var origin = context.Request.Headers["Origin"].FirstOrDefault();
            if (origin != null && !allowedDomains.Any(x => x.ToLower() == origin.ToLower()))
            {
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                return;
            }
        }

        await _next.Invoke(context);
    }
    private async Task HandleExceptionAsync(HttpContext context)
    {
        var response = context.Response;
        response.ContentType = "application/json;charset=utf-8";
        response.StatusCode = StatusCodes.Status403Forbidden;
        await response.WriteAsync(JsonConvert.SerializeObject(new { code = HttpStatusCode.Forbidden, message = Messages.SomethingWentWrong }));
    }
}

