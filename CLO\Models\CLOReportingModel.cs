﻿using CLO.CQRS.Results;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CLO.Models
{
    public class CLOReportingModel
    {
        public List<InvestmentCompanyData> Data { get; set; }
        //public InvestmentCompanyData SectionData { get; set; }
        //public  List<CLOModel> CLOs { get; set; }
        //public CLOPageConfigModel CLOPageConfig { get; set; }
    }
    public class InvestmentCompanyData
    {
        public int Id { get; set; }
        public string? CompanyName { get; set; }

        public List<SectionData>? SectionData { get; set; }
        public List<CLOData>? CLOs { get; set; }

    }

    public class CompanyFact
    {
        public string? Domicile { get; set; }
        public string? IncorporationDate { get; set; }
        public string? FirstClose { get; set; }
        public string? FinalClose { get; set; }
        public string? InvestmentPeriodEndDate { get; set; }
        public string? MaturityDate { get; set; }
        public string? Commitments { get; set; }
        public string? BaseCurrency { get; set; }
        public string? Custodian { get; set; }
        public string? Administrator { get; set; }
        public string? ListingAgent { get; set; }
        public string? LegalCounsel { get; set; }
        public string? PortfolioAdvisor { get; set; }
     
    }

    public class InvestmenetSummary
    {
        public string? InvestmentSummary { get; set; }  
    }

    public class SectionData
    {
        public string? SectionName { get; set; }
        public string? AliasName { get; set; }
        public List<TabDetails>? Tables { get; set; }
    }

    public class CLOData
    {
        public int CLO_ID { get; set; }
        public string Issuer { get; set; }
        public string CloSummaryTitle { get; set; }
        public List<SectionData>? SectionData { get; set; }
    }
    public class CLODetails
    {
        public string? Arranger { get; set; }
        public string? Trustee { get; set; }
        public string? Priced { get; set; }
        public string? Closed { get; set; }
        public string? LastRefiDate { get; set; }
        public string? LastResetDate { get; set; }
        public string? CallEndDate { get; set; }
        public string? OriginalEndOfReinvestmentDate { get; set; }
        public string? CurrentEndOfReinvestmentDate { get; set; }
        public string? CurrentMaturityDate { get; set; }
        public string? LastRefiResetArranger { get; set; }
    }
    public class TabDetails
    {
        public string? TableName { get; set; }
        public string? TableAliasName { get; set; }
        public TableDataResult TableDataResult { get; set; }
    }

    public class ReportingDataCoulmn
    {
        public int PageId { get; set; }
        public int TabId { get; set; }
        public string SectionName { get; set; }
        public string TableName { get; set; }
        public string TableIdentifier { get; set; }
        public string TableObjectName { get; set; }
        public int SequenceNo { get; set; }

    }

}
