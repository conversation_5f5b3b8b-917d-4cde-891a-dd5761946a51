IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_CompanyBalanceSheetLineItems') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_CompanyBalanceSheetLineItems') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_CompanyBalanceSheetLineItems] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_CompanyCashFlowLineItems') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_CompanyCashFlowLineItems') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_CompanyCashFlowLineItems] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_CompanyProfitAndLossLineItems') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_CompanyProfitAndLossLineItems') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_CompanyProfitAndLossLineItems] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_ImpactKPI_Order') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_ImpactKPI_Order') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_ImpactKPI_Order] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_Kpis') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_Kpis') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_Kpis] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_PortfolioCompanyKPI') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_PortfolioCompanyKPI') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_PortfolioCompanyKPI] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_PortfolioInvestmentKPI') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_PortfolioInvestmentKPI') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_PortfolioInvestmentKPI] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_PortfolioOperationalKPI') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_PortfolioOperationalKPI') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_PortfolioOperationalKPI] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MappingCapTable') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.MappingCapTable') AND name = 'IsExtraction')
    ALTER TABLE dbo.[MappingCapTable] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MappingFundSectionKpi') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.MappingFundSectionKpi') AND name = 'IsExtraction')
    ALTER TABLE dbo.[MappingFundSectionKpi] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_CompanyBalanceSheetLineItems]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_CompanyBalanceSheetLineItems] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_CompanyCashFlowLineItems]') AND name = 'Synonym') 
ALTER TABLE dbo.[Mapping_CompanyCashFlowLineItems] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_CompanyProfitAndLossLineItems]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_CompanyProfitAndLossLineItems] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_ImpactKPI_Order]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_ImpactKPI_Order] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_Kpis]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_Kpis] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_PortfolioCompanyKPI]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_PortfolioCompanyKPI] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_PortfolioInvestmentKPI]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_PortfolioInvestmentKPI] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_PortfolioOperationalKPI]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_PortfolioOperationalKPI] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[MappingCapTable]') AND name = 'Synonym')
ALTER TABLE dbo.[MappingCapTable] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[MappingFundSectionKpi]') AND name = 'Synonym')
ALTER TABLE dbo.[MappingFundSectionKpi] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_BalanceSheet_LineItems]') AND name = 'synonym')
    ALTER TABLE dbo.[M_BalanceSheet_LineItems] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_ProfitAndLoss_LineItems]') AND name = 'synonym') 
    ALTER TABLE dbo.[M_ProfitAndLoss_LineItems] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_CashFlow_LineItems]') AND name = 'synonym')
    ALTER TABLE dbo.[M_CashFlow_LineItems] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_ImpactKPI]') AND name = 'synonym')
    ALTER TABLE dbo.[M_ImpactKPI] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_MasterKpis]') AND name = 'synonym')
    ALTER TABLE dbo.[M_MasterKpis] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_InvestmentKPI]') AND name = 'synonym')
    ALTER TABLE dbo.[M_InvestmentKPI] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_SectorwiseOperationalKPI]') AND name = 'synonym')
    ALTER TABLE dbo.[M_SectorwiseOperationalKPI] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_CompanyKPI]') AND name = 'synonym')
    ALTER TABLE dbo.[M_CompanyKPI] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[MCapTable]') AND name = 'synonym')
    ALTER TABLE dbo.[MCapTable] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[MFundSectionKpi]') AND name = 'synonym')
    ALTER TABLE dbo.[MFundSectionKpi] ADD Synonym varchar(3000)
GO
ALTER PROCEDURE  [dbo].[ProcCreateDuplicateKPI]
(
@KpiType VARCHAR(100),
@KpiId INT,
@UserId INT,
@ModuleId INT=NULL,
@Id INT OUTPUT
)
AS
BEGIN
SET NOCOUNT ON
		 BEGIN TRY
		 IF(@KpiType = 'Operational KPI')
			BEGIN
				INSERT INTO M_SectorwiseOperationalKPI(SectorID,KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym) SELECT SectorID,KPI,KpiInfo,@UserId,GETDATE(),0,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym FROM M_SectorwiseOperationalKPI WHERE SectorwiseOperationalKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		  IF(@KpiType = 'Company KPI')
			BEGIN
				INSERT INTO M_CompanyKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym FROM M_CompanyKPI WHERE CompanyKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		  IF(@KpiType = 'Impact KPI')
			BEGIN
				INSERT INTO M_ImpactKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,Description,ParentID,IsParent,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,Description,ParentID,IsParent,Formula,FormulaKPIId,Synonym FROM M_ImpactKPI WHERE ImpactKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		 IF(@KpiType = 'Investment KPI')
			BEGIN
				INSERT INTO M_InvestmentKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsNumeric,OrderBy,Description,Formula,FormulaKPIId,MethodologyId,IsBoldKPI,IsHeader,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,0,0,Description,Formula,FormulaKPIId,MethodologyId,IsBoldKPI,IsHeader,Synonym FROM M_InvestmentKPI WHERE InvestmentKPIId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		 IF(@KpiType = 'Balance Sheet KPI')
			BEGIN
				INSERT INTO M_BalanceSheet_LineItems(BalanceSheetLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,IsHeader,IsBoldKPI,MethodologyID,DisplayOrder,Description,IsCalculatedValue,Formula,FormulaKPIId,Synonym) SELECT BalanceSheetLineItem,KpiInfo,@UserId,GETDATE(),0,1,IsHeader,IsBoldKPI,MethodologyID,DisplayOrder,Description,IsCalculatedValue,Formula,FormulaKPIId,Synonym FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Cashflow KPI')
			BEGIN
				INSERT INTO M_CashFlow_LineItems(CashFlowLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,IsHeader,IsBoldKPI,MethodologyID,Description,DisplayOrder,IsCalculatedValue,Formula,FormulaKPIId,Synonym) SELECT CashFlowLineItem,KpiInfo,@UserId,GETDATE(),0,1,IsHeader,IsBoldKPI,MethodologyID,Description,DisplayOrder,IsCalculatedValue,Formula,FormulaKPIId,Synonym FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Profit And Loss KPI')
			BEGIN
				INSERT INTO M_ProfitAndLoss_LineItems(ProfitAndLossLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,MethodologyID,IsHeader,IsBoldKPI,Description,DisplayOrder,IsCalculatedValue,IsHighlighted,Formula,FormulaKPIId,Synonym) SELECT ProfitAndLossLineItem,KpiInfo,@UserId,GETDATE(),0,1,MethodologyID,IsHeader,IsBoldKPI,Description,DisplayOrder,IsCalculatedValue,IsHighlighted,Formula,FormulaKPIId,Synonym FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Trading Records')
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,1,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Credit KPI')
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,2,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId  = 16)
			BEGIN
				INSERT INTO MMonthlyReport(Kpi,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKpi,Description,Formula,FormulaKpiId) SELECT Kpi,KpiInfo,@UserId,GETUTCDATE(),0,IsHeader,IsBoldKpi,Description,Formula,FormulaKpiId FROM MMonthlyReport WHERE KpiId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId IN (11,12,13,14,15))
			BEGIN
				INSERT INTO MCapTable(Kpi,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKpi,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,KpiTypeId,Synonym) SELECT KPI,KpiInfo,@UserId,GETUTCDATE(),0,IsHeader,IsBoldKPI,@ModuleId,MethodologyID,Description,Formula,FormulaKPIId,KpiTypeId,Synonym FROM MCapTable WHERE KpiId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30))
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,@ModuleId,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
         END TRY
         BEGIN CATCH
             SET @Id = 0
         END CATCH
END
go
ALTER PROCEDURE [dbo].[ProcCreateDuplicateFundKPI]
(
    @KpiId INT,
    @UserId INT,
    @Id INT OUTPUT
)
AS
BEGIN
    SET NOCOUNT ON
    BEGIN TRY
        BEGIN
            INSERT INTO MFundSectionKpi(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,MethodologyID,IsHeader,IsBoldKPI,Description,
            Formula,FormulaKPIId,ModuleId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,MethodologyID,IsHeader,IsBoldKPI,
            Description,Formula,FormulaKPIId,ModuleId,Synonym FROM MFundSectionKpi
            WHERE FundSectionKpiId = @KpiId
            SET @Id = SCOPE_IDENTITY()
        END
    END TRY
    BEGIN CATCH
        SET @Id = 0
    END CATCH
    
END
go
ALTER PROCEDURE  [dbo].[ProcFundKpiCopyKPIToFunds]
(
@ModuleId INT,
@FundId INT,
@UserId INT,
@FundIds NVARCHAR(MAX)
)
AS
BEGIN
SET NOCOUNT ON

	 BEGIN TRY
		 DECLARE @RowCount INT,@Row INT = 1;
		 DROP TABLE IF EXISTS #tempFundKpiCopyToFunds
			CREATE TABLE #tempFundKpiCopyToFunds
			(
			 Id INT IDENTITY(1,1) PRIMARY KEY,
			 FundId INT,
			 ModuleId INT
			)
			INSERT INTO #tempFundKpiCopyToFunds(FundId)
			SELECT Item AS FundId FROM[dbo].[SplitString](@FundIds,',') WHERE Item!=@FundId
			SET @RowCount = (SELECT Count(FundId) FROM #tempFundKpiCopyToFunds)
			WHILE (@Row <= @RowCount)
			BEGIN
			DECLARE @PortfolioFundId INT
			SELECT @PortfolioFundId=FundId FROM #tempFundKpiCopyToFunds Where ID= @Row
		   DECLARE @MappingFundKpi table(ID INT IDENTITY(1,1), FundId int, KpiId int, CreatedBy int, CreatedOn Datetime,
		   IsDeleted bit, ParentId int, DisplayOrder int, Formula nvarchar(max), FormulaKPIId nvarchar(max), ModuleId int ,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
	       DELETE FROM @MappingFundKpi
			BEGIN
					INSERT INTO @MappingFundKpi
					SELECT FundId, KpiId, CreatedBy, GETDATE(), IsDeleted, ParentKpiId, DisplayOrder, Formula, FormulaKPIId, ModuleId,Synonym,Definition,IsExtraction From
					MappingFundSectionKpi where FundId = @FundId AND ModuleId=@ModuleId AND IsDeleted = 0
					
					IF EXISTS(select * from MappingFundSectionKpi where  IsDeleted =0 and FundId = @PortfolioFundId and
					ModuleId=@ModuleId and KpiId IN (SELECT KpiId FROM @MappingFundKpi))
					BEGIN
					    UPDATE MappingFundSectionKpi SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiId,ParentKpiId=B.ParentId,Formula=B.Formula,
						FormulaKPIId=B.FormulaKPIId,Synonym = B.Synonym,Definition = B.Definition,IsExtraction = B.IsExtraction FROM MappingFundSectionKpi A
						Inner Join @MappingFundKpi B On A.KpiId=B.KpiId WHERE A.IsDeleted=0 and A.FundId= @PortfolioFundId and A.ModuleId=@ModuleId
					END
						INSERT INTO MappingFundSectionKpi(FundId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,Formula,FormulaKPIId,ModuleId,Synonym,Definition,IsExtraction)
						SELECT @PortfolioFundId,KpiId,CreatedBy,GETDATE(),0,ParentId,DisplayOrder,Formula,FormulaKPIId,@ModuleId,Synonym,Definition,IsExtraction From @MappingFundKpi
						where KpiId not in  (SELECT KpiId FROM MappingFundSectionKpi
							where FundId =@PortfolioFundId AND IsDeleted=0)
							and IsDeleted=0 and ModuleId=@ModuleId

			END
		
			SET @Row = @Row + 1
			END
			DROP TABLE IF EXISTS #tempFundKpiCopyToFunds		
         END TRY
         BEGIN CATCH
             
         END CATCH
	
END
go
ALTER PROC [dbo].[GetFundkpiNotMappedList](
@FundId INT,
@ModuleId INT
)
AS
BEGIN
    SELECT DISTINCT(K.FundSectionKpiId) 'Id',K.KPI 'Name',K.IsHeader 'IsHeader',K.IsBoldKPI 'IsBoldKPI',K.Formula 'Formula',
    K.FormulaKPIId 'FormulaKPIId',Synonym,Description FROM MFundSectionKpi K 
                                where K.FundSectionKpiId not in  (SELECT KpiId FROM MappingFundSectionKpi 
                                where FundId =@FundId AND IsDeleted=0) 
                                and K.IsDeleted=0 and K.ModuleId=@ModuleId  order by K.KPI
END
GO
ALTER  PROCEDURE [dbo].[GetMasterkpiNotMappedList]
(
@companyId INT,
@Type Varchar(100),
@ModuleId INT = NULL
)
AS
BEGIN  
	IF @Type = 'Company KPI' 
							BEGIN
							SELECT DISTINCT(M.CompanyKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId', M.KpiInfo 'KpiInfo',M.Synonym,M.Description   FROM M_CompanyKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioCompanyKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.CompanyKPIID = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MappingPortfolioCompanyKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Impact KPI'
							BEGIN
							SELECT DISTINCT(M.ImpactKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_ImpactKPI M 
							LEFT JOIN (SELECT *FROM Mapping_ImpactKPI_Order 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.ImpactKPIID = MAP.ImpactKPIID 
							WHERE M.IsDeleted =0  AND MAP.ImpactKPIMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Investment KPI'
							BEGIN
							SELECT DISTINCT(M.InvestmentKPIId) 'Id',M.KPI 'Name', M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_InvestmentKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioInvestmentKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.InvestmentKPIId = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MAP.MappingPortfolioInvestmentKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Balance Sheet KPI'
							BEGIN
							SELECT DISTINCT(M.BalanceSheetLineItemID) 'Id',M.BalanceSheetLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_BalanceSheet_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyBalanceSheetLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.BalanceSheetLineItemID = MAP.BalanceSheetLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyBalanceSheetLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Cashflow KPI'
							BEGIN
							SELECT DISTINCT(M.CashFlowLineItemID) 'Id',M.CashFlowLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_CashFlow_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyCashFlowLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.CashFlowLineItemID = MAP.CashFlowLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyCashFlowLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Profit & Loss KPI'
							BEGIN
							SELECT DISTINCT(M.ProfitAndLossLineItemID) 'Id',M.ProfitAndLossLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_ProfitAndLoss_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyProfitAndLossLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.ProfitAndLossLineItemID = MAP.ProfitAndLossLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyProfitAndLossLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Operational KPI'
							BEGIN
							SELECT DISTINCT(M.SectorwiseOperationalKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_SectorwiseOperationalKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioOperationalKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.SectorwiseOperationalKPIID = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MAP.MappingPortfolioOperationalKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Credit KPI'
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = 2) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = 2  AND Mapping_KpisID IS NULL order by Name
							END
	ELSE IF @Type = 'Trading Records'
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = 1) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = 1  AND Mapping_KpisID IS NULL order by Name
							END
    IF @ModuleId = 16
							BEGIN
							SELECT DISTINCT(M.KpiId) 'Id',M.Kpi 'Name',M.IsHeader 'IsHeader',M.IsBoldKpi 'IsBoldKPI',M.Formula 'Formula',M.FormulaKpiId 'FormulaKPIId',M.KpiInfo 'KpiInfo'  FROM MMonthlyReport M 
							LEFT JOIN (SELECT *FROM MappingMonthlyReport 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.KpiId = MAP.KpiId
							WHERE M.IsDeleted =0 AND MappingId IS NULL order by Name
							END
   IF @ModuleId IN (11,12,13,14,15)
							BEGIN
							SELECT DISTINCT(M.KpiId) 'Id',M.Kpi 'Name',M.IsHeader 'IsHeader',M.IsBoldKpi 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',KpiTypeId,M.Synonym,M.Description  FROM MCapTable M 
							LEFT JOIN (SELECT *FROM MappingCapTable 
							where PortfolioCompanyId = @companyId AND IsDeleted=0 AND ModuleID = @ModuleId) MAP ON M.KpiId = MAP.KpiId 
							WHERE M.IsDeleted =0 AND M.ModuleID = @ModuleId  AND MappingId IS NULL order by Name
							END
   IF @ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30)
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = @ModuleId) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = @ModuleId  AND Mapping_KpisID IS NULL order by Name
							END
END
GO
ALTER PROCEDURE [dbo].[ProcCopyKPIToCompanies]
(
@KpiType VARCHAR(100),
@CompanyId INT,
@UserId INT,
@CompanyIds NVARCHAR(MAX),
@ModuleId INT = NULL
)
AS
BEGIN
SET NOCOUNT ON
			BEGIN TRY
			DECLARE @RowCount INT,@Row INT = 1;
			DROP TABLE IF EXISTS #tempCopyToCompany
			CREATE TABLE #tempCopyToCompany
			(
				Id INT IDENTITY(1,1) PRIMARY KEY,
				PortfolioCompanyID Int
			)
			INSERT INTO #tempCopyToCompany(PortfolioCompanyID)
			SELECT Item AS PortfolioCompanyID FROM[dbo].[SplitString](@CompanyIds,',') WHERE Item!=@CompanyId
			SET @RowCount = (SELECT Count(PortfolioCompanyID) FROM #tempCopyToCompany)
			WHILE (@Row <= @RowCount)
			BEGIN
			DROP TABLE IF EXISTS #KPIHierarchy;
            DROP TABLE IF EXISTS #ExistingKPIs;
			CREATE TABLE #ExistingKPIs ( KPIName VARCHAR(500), DestKpiID INT, ParentKPIID INT, ParentKPIName VARCHAR(500), DisplayOrder INT, IsRootLevel BIT ,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			CREATE TABLE #KPIHierarchy ( SourceKpiID INT, KPIName VARCHAR(MAX), ParentKPIID INT, ParentKPIName VARCHAR(MAX), Level INT, DisplayOrder INT, HierarchyOrder VARCHAR(MAX), NewDisplayOrder INT, KPITypeID INT ,Formula NVarchar(Max),FormulaKPIId INT,CreatedBy INT,CreatedOn datetime,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @PortfolioCompanyID INT
			SELECT @PortfolioCompanyID=PortfolioCompanyID FROM #tempCopyToCompany Where ID= @Row
			DECLARE @MappingOperational table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCompany table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingImpact table(ID INT IDENTITY(1,1), PortfolioCompanyID int,ImpactKPIID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,KPIOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingInvestment table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),KPIHierarchy Nvarchar(Max),HierarchyOrder NVARCHAR(500),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingBalance table(ID INT IDENTITY(1,1), PortfolioCompanyID int,BalanceSheetLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingProfit table(ID INT IDENTITY(1,1), PortfolioCompanyID int,ProfitAndLossLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCashflow table(ID INT IDENTITY(1,1), PortfolioCompanyID int,CashFlowLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingTrading table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCredit table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCapTable table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingMonthlyReport table(ID INT IDENTITY(1,1),PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,Formula nvarchar(max),FormulaKPIId nvarchar(max));
			DELETE FROM @MappingOperational
			DELETE FROM @MappingCompany
			DELETE FROM @MappingImpact
			DELETE FROM @MappingInvestment
			DELETE FROM @MappingBalance
			DELETE FROM @MappingProfit
			DELETE FROM @MappingCashflow
			DELETE FROM @MappingTrading
			DELETE FROM @MappingCredit
			DELETE FROM @MappingCapTable
			DELETE FROM @MappingMonthlyReport
			Declare @MaxOrderId INT;
			Declare @ID INT,@ParentKPIID INT,@DestParentKPIID INT,@KpiID INT;
			Declare @KPI NVarchar(MAX)=NULL;
			IF(@KpiType = 'Operational KPI')
			BEGIN
				INSERT INTO @MappingOperational 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_PortfolioOperationalKPI where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(SELECT * FROM Mapping_PortfolioOperationalKPI  WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingOperational ))
					BEGIN
						UPDATE Mapping_PortfolioOperationalKPI SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioOperationalKPI A Inner Join @MappingOperational B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID 
					END							
				INSERT INTO Mapping_PortfolioOperationalKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction FROM  @MappingOperational WHERE  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioOperationalKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
			IF(@KpiType = 'Company KPI')
			BEGIN
			 	INSERT INTO @MappingCompany 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_PortfolioCompanyKPI where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_PortfolioCompanyKPI where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID  and KpiID IN (SELECT KpiID FROM @MappingCompany ))
					BEGIN
						UPDATE Mapping_PortfolioCompanyKPI SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioCompanyKPI A Inner Join @MappingCompany B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_PortfolioCompanyKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCompany where  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioCompanyKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
			IF(@KpiType = 'Impact KPI')
			BEGIN
			 	INSERT INTO @MappingImpact 
				SELECT PortfolioCompanyID,ImpactKPIID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_ImpactKPI_Order where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_ImpactKPI_Order where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID  and ImpactKPIID IN (SELECT ImpactKPIID FROM @MappingImpact ))
					BEGIN
						UPDATE Mapping_ImpactKPI_Order SET KPIOrder=B.KPIOrder,ImpactKPIID=B.ImpactKPIID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_ImpactKPI_Order A Inner Join @MappingImpact B On A.ImpactKPIID=B.ImpactKPIID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_ImpactKPI_Order(PortfolioCompanyID,ImpactKPIID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,ImpactKPIID,CreatedBy,GETDATE(),0,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingImpact where ImpactKPIID NOT IN (SELECT ImpactKPIID FROM Mapping_ImpactKPI_Order WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
				
			END
			IF(@KpiType = 'Investment KPI')
			BEGIN	
				SELECT @MaxOrderId = ISNULL(MAX(DisplayOrder), 0) FROM Mapping_PortfolioInvestmentKPI WHERE IsDeleted = 0 AND PortfolioCompanyID = @PortfolioCompanyID;
				;WITH KPIHierarchyCTE AS (
                    SELECT 
                        src.KpiID AS SourceKpiID,
                         CAST(srcKPI.KPI AS VARCHAR(MAX)) AS KPIName,
                        src.ParentKPIID,
                         CAST(NULL AS VARCHAR(MAX)) AS ParentKPIName,
                        0 AS Level,
                        src.DisplayOrder,
						CAST(RIGHT('000000' + CAST(DisplayOrder AS VARCHAR(6)), 6) AS VARCHAR(MAX)) AS HierarchyOrder,
						src.KPITypeID,src.Formula,src.FormulaKPIId,src.CreatedBy,src.CreatedOn,src.Synonym,src.Definition,src.IsExtraction
                    FROM Mapping_PortfolioInvestmentKPI src
                    INNER JOIN M_InvestmentKPI srcKPI 
                        ON src.KpiID = srcKPI.InvestmentKPIId
                    WHERE src.PortfolioCompanyID = @CompanyId
                    AND src.ParentKPIID IS NULL
                    AND src.IsDeleted = 0
                    UNION ALL
                    SELECT 
                        src.KpiID,
                        CAST(srcKPI.KPI AS VARCHAR(MAX)),
                        src.ParentKPIID,
                        CAST(parentKPI.KPI AS VARCHAR(MAX)),
                        h.Level + 1,
                        src.DisplayOrder,
						 CAST(h.HierarchyOrder + '.' + RIGHT('000000' + CAST(src.DisplayOrder AS VARCHAR(6)), 6) AS VARCHAR(MAX)) as HierarchyOrder,
						 src.KPITypeID,src.Formula,src.FormulaKPIId,src.CreatedBy,src.CreatedOn,src.Synonym,src.Definition,src.IsExtraction
                    FROM Mapping_PortfolioInvestmentKPI src
                    INNER JOIN M_InvestmentKPI srcKPI 
                        ON src.KpiID = srcKPI.InvestmentKPIId
                    INNER JOIN M_InvestmentKPI parentKPI 
                        ON src.ParentKPIID = parentKPI.InvestmentKPIId
                    INNER JOIN KPIHierarchyCTE h 
                        ON src.ParentKPIID = h.SourceKpiID
                    WHERE src.PortfolioCompanyID = @CompanyId
                    AND src.IsDeleted = 0
                )
				 INSERT INTO #KPIHierarchy(KPIName,ParentKPIID,ParentKPIName,Level,DisplayOrder,SourceKpiID,HierarchyOrder,NewDisplayOrder,KPITypeID,Formula,FormulaKPIId,CreatedBy,CreatedOn,Synonym,Definition,IsExtraction)
                SELECT KPIName,ParentKPIID,ParentKPIName,Level,DisplayOrder,SourceKpiID,HierarchyOrder,CASE WHEN @MaxOrderId=0 THEN
				        ROW_NUMBER() OVER (ORDER BY HierarchyOrder) 
						ELSE @MaxOrderId+1 + ROW_NUMBER() OVER (ORDER BY HierarchyOrder) END AS NewDisplayOrder,KPITypeID,Formula,FormulaKPIId,CreatedBy,CreatedOn,Synonym,Definition,IsExtraction FROM KPIHierarchyCTE;

				-- Get existing destination KPIs
                INSERT INTO #ExistingKPIs
                SELECT DISTINCT
                    destKPI.KPI,
                    dest.KpiID,
                    dest.ParentKPIID,
                    parentKPI.KPI,
                    dest.DisplayOrder,
                    CASE WHEN dest.ParentKPIID IS NULL THEN 1 ELSE 0 END,
					dest.Synonym,
					dest.Definition,
					dest.IsExtraction
                FROM Mapping_PortfolioInvestmentKPI dest
                INNER JOIN M_InvestmentKPI destKPI 
                    ON dest.KpiID = destKPI.InvestmentKPIId
                LEFT JOIN M_InvestmentKPI parentKPI 
                    ON dest.ParentKPIID = parentKPI.InvestmentKPIId
                WHERE dest.PortfolioCompanyID = @PortfolioCompanyID
                AND dest.IsDeleted = 0;

				--SELECT * FROM #KPIHierarchy

				INSERT INTO @MappingInvestment(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,HierarchyOrder,Synonym,Definition,IsExtraction) 
				SELECT 
                    kh.KPITypeID,
                    @PortfolioCompanyID,
                    kh.SourceKpiID,
                    @UserId,
                    GETDATE(),
                    0,
                    CASE 
					    WHEN kh.ParentKPIID IS NOT NULL THEN
					        CASE WHEN EXISTS (
					            SELECT 1 
					            FROM #ExistingKPIs e 
					            WHERE e.KPIName = kh.ParentKPIName
					            AND e.IsRootLevel = 1
					        )
					        THEN 
					            (SELECT TOP 1 e.DestKpiID 
					             FROM #ExistingKPIs e 
					             WHERE e.KPIName = kh.ParentKPIName
					             AND e.IsRootLevel = 1)
					        ELSE kh.ParentKPIID  -- Use ParentKPIID instead of NULL
					        END
					    ELSE kh.ParentKPIID  -- Use ParentKPIID instead of NULL
					END ParentKPIID,
					kh.NewDisplayOrder,kh.Formula,kh.FormulaKPIId,kh.HierarchyOrder,kh.Synonym,kh.Definition,kh.IsExtraction
                FROM #KPIHierarchy kh
                LEFT JOIN #ExistingKPIs existing
                    ON existing.KPIName = kh.KPIName
                    AND existing.IsRootLevel = CASE WHEN kh.ParentKPIID IS NULL THEN 1 ELSE 0 END
                WHERE NOT EXISTS (
                    SELECT 1 FROM #ExistingKPIs e
                    WHERE e.KPIName = kh.KPIName
                    AND (
                        (kh.ParentKPIID IS NULL AND e.IsRootLevel = 1) OR
                        (kh.ParentKPIID IS NOT NULL AND e.ParentKPIName = kh.ParentKPIName)
                    )
                );
			   IF EXISTS(select *from Mapping_PortfolioInvestmentKPI where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingInvestment))
				BEGIN
					UPDATE Mapping_PortfolioInvestmentKPI SET DisplayOrder=B.DisplayOrder ,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioInvestmentKPI A Inner Join @MappingInvestment B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
			   END
			   INSERT INTO Mapping_PortfolioInvestmentKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
			   SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingInvestment where  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioInvestmentKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
				
				DROP TABLE IF EXISTS #KPIHierarchy;
                DROP TABLE IF EXISTS #ExistingKPIs;
			
			END
			IF(@KpiType = 'Balance Sheet KPI')
			BEGIN
			 	INSERT INTO @MappingBalance 
				SELECT PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyBalanceSheetLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_CompanyBalanceSheetLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and BalanceSheetLineItemID IN (SELECT BalanceSheetLineItemID FROM @MappingBalance))
					BEGIN
						UPDATE Mapping_CompanyBalanceSheetLineItems SET DisplayOrder=B.DisplayOrder,BalanceSheetLineItemID=B.BalanceSheetLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyBalanceSheetLineItems A Inner Join @MappingBalance B On A.BalanceSheetLineItemID=B.BalanceSheetLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyBalanceSheetLineItems(PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingBalance where  BalanceSheetLineItemID NOT IN (SELECT BalanceSheetLineItemID FROM Mapping_CompanyBalanceSheetLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Cashflow KPI')
			BEGIN
				INSERT INTO @MappingCashflow 
				SELECT PortfolioCompanyID,CashFlowLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyCashFlowLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_CompanyCashFlowLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and CashFlowLineItemID IN (SELECT CashFlowLineItemID FROM @MappingCashflow))
					BEGIN
						UPDATE Mapping_CompanyCashFlowLineItems SET DisplayOrder=B.DisplayOrder,CashFlowLineItemID=B.CashFlowLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyCashFlowLineItems A Inner Join @MappingCashflow B On A.CashFlowLineItemID=B.CashFlowLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyCashFlowLineItems(PortfolioCompanyID,CashFlowLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,CashFlowLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCashflow where  CashFlowLineItemID NOT IN (SELECT CashFlowLineItemID FROM Mapping_CompanyCashFlowLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Profit & Loss KPI')
			BEGIN
				INSERT INTO @MappingProfit 
				SELECT PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyProfitAndLossLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_CompanyProfitAndLossLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ProfitAndLossLineItemID IN (SELECT ProfitAndLossLineItemID FROM @MappingProfit))
					BEGIN
						UPDATE Mapping_CompanyProfitAndLossLineItems SET DisplayOrder=B.DisplayOrder,ProfitAndLossLineItemID=B.ProfitAndLossLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyProfitAndLossLineItems A Inner Join @MappingProfit B On A.ProfitAndLossLineItemID=B.ProfitAndLossLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyProfitAndLossLineItems(PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingProfit where  ProfitAndLossLineItemID NOT IN (SELECT ProfitAndLossLineItemID FROM Mapping_CompanyProfitAndLossLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Trading Records')
			BEGIN
			 	INSERT INTO @MappingTrading 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_Kpis where PortfolioCompanyID = @CompanyId  AND ModuleID=1 AND IsDeleted = 0
				
				IF EXISTS(select *from Mapping_Kpis where ModuleID=1 and IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingTrading))
					BEGIN
						UPDATE Mapping_Kpis SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_Kpis A Inner Join @MappingTrading B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID AND A.ModuleID=1
					END							
				INSERT INTO Mapping_Kpis(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingTrading where  KpiID NOT IN (SELECT KpiID FROM Mapping_Kpis WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ModuleID=1 )
												
			END
		IF(@KpiType = 'Credit KPI' OR @ModuleId IN (2, 17,18,19,20,21,22,23,24,25,26,27,28,29,30))
			BEGIN
			 	INSERT INTO @MappingCredit 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_Kpis where PortfolioCompanyID = @CompanyId  AND ModuleID=@ModuleId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_Kpis where ModuleID=@ModuleId and IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingCredit))
				BEGIN
						UPDATE Mapping_Kpis SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_Kpis A Inner Join @MappingCredit B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID AND A.ModuleID=@ModuleId
				END							
				INSERT INTO Mapping_Kpis(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCredit where  KpiID NOT IN (SELECT KpiID FROM Mapping_Kpis WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ModuleID=@ModuleId )												
			END
			IF(@ModuleId = 16)
			BEGIN
			 	INSERT INTO @MappingMonthlyReport 
				SELECT PortfolioCompanyId,KpiId,CreatedBy,GETUTCDATE(),IsDeleted,ParentKpiId,DisplayOrder,IsHeader,Formula,FormulaKpiId From MappingMonthlyReport where PortfolioCompanyId = @CompanyId  AND IsDeleted = 0
				IF EXISTS(select *from MappingMonthlyReport where  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and KpiId IN (SELECT KpiID FROM @MappingMonthlyReport))
				BEGIN
						UPDATE MappingMonthlyReport SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiID,ParentKpiId=B.ParentKpiId,Formula=B.Formula,FormulaKpiId=B.FormulaKpiId FROM MappingMonthlyReport A Inner Join @MappingMonthlyReport B On A.KpiId=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyId= @PortfolioCompanyID
				END							
				INSERT INTO MappingMonthlyReport(PortfolioCompanyId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,IsHeader,Formula,FormulaKPIId) 
				SELECT @PortfolioCompanyID,KpiID,CreatedBy,GETUTCDATE(),0,ParentKPIID,DisplayOrder,IsHeader,Formula,FormulaKPIId From @MappingMonthlyReport where  KpiID NOT IN (SELECT KpiID FROM MappingMonthlyReport WHERE  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID)												
			END
		IF(@ModuleId IN (11,12,13,14,15))
			BEGIN
			 	INSERT INTO @MappingCapTable 
				SELECT 1,PortfolioCompanyId,KpiId,CreatedBy,GETDATE(),IsDeleted,ParentKpiId,DisplayOrder,IsHeader,ModuleId,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From MappingCapTable where PortfolioCompanyId = @CompanyId  AND ModuleId=@ModuleId AND IsDeleted = 0
				IF EXISTS(select *from MappingCapTable where ModuleID=@ModuleId and IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and KpiId IN (SELECT KpiID FROM @MappingCapTable))
				BEGIN
						UPDATE MappingCapTable SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiID,ParentKpiId=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM MappingCapTable A Inner Join @MappingCapTable B On A.KpiId=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyId= @PortfolioCompanyID AND A.ModuleId=@ModuleId
				END							
				INSERT INTO MappingCapTable(PortfolioCompanyId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,IsHeader,ModuleId,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCapTable where  KpiID NOT IN (SELECT KpiID FROM MappingCapTable WHERE  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and ModuleId=@ModuleId)												
			END
			SET @Row = @Row + 1
			END
			DROP TABLE IF EXISTS #tempCopyToCompany		
			END TRY
			BEGIN CATCH
              IF OBJECT_ID('tempdb..#KPIHierarchy') IS NOT NULL DROP TABLE #KPIHierarchy;
			  IF OBJECT_ID('tempdb..#ExistingKPIs') IS NOT NULL DROP TABLE #ExistingKPIs;
			END CATCH
END
GO
CREATE OR ALTER PROCEDURE [dbo].[GetAllMappedKpi]
(
    @CompanyIds NVARCHAR(MAX),
    @ModuleIds NVARCHAR(MAX)
)
AS
BEGIN
    Declare @TempKpiMapping TABLE
    (
        PortfolioCompanyID INT, KpiId INT, KpiName NVARCHAR(255), ParentKPIID INT, DisplayOrder INT, IsMapped BIT, IsHeader BIT,
        IsBoldKPI BIT, MappingKPIId INT, KpiInfo NVARCHAR(MAX), IsExtraction BIT, Synonym NVARCHAR(255), Definition NVARCHAR(MAX),
        MethodologyID INT, ModuleId INT
    )
    --BalanceSheet
    INSERT INTO @TempKpiMapping 
    SELECT
        q1.PortfolioCompanyID, q1.BalanceSheetLineItemID AS KpiId, q2.BalanceSheetLineItem AS KpiName, q1.ParentLineItemID AS ParentKPIID, 
        q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.CompanyBalanceSheetLineItemMappingID AS MappingKPIId,
        q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 8 as ModuleId
    FROM
        Mapping_CompanyBalanceSheetLineItems q1
    INNER JOIN
        M_BalanceSheet_LineItems q2 ON q1.BalanceSheetLineItemID = q2.BalanceSheetLineItemID
    WHERE
        q1.IsExtraction = 1 
        AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,','))
        AND 8 IN (SELECT Item FROM dbo.SplitString(@ModuleIds,','))
        AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
    --Cashflow
    INSERT INTO @TempKpiMapping 
    SELECT
        q1.PortfolioCompanyID, q1.CashFlowLineItemID AS KpiId, q2.CashFlowLineItem AS KpiName, q1.ParentLineItemID AS ParentKPIID, 
        q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.CompanyCashFlowLineItemMappingID AS MappingKPIId,
        q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 9 as ModuleId
    FROM
        Mapping_CompanyCashFlowLineItems q1
    INNER JOIN
        M_CashFlow_LineItems q2 ON q1.CashFlowLineItemID = q2.CashFlowLineItemID
    WHERE
        q1.IsExtraction = 1 
        AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,','))
        AND 9 IN (SELECT Item FROM dbo.SplitString(@ModuleIds,','))
        AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
    --P&L
    INSERT INTO @TempKpiMapping 
    SELECT
        q1.PortfolioCompanyID, q1.ProfitAndLossLineItemID AS KpiId, q2.ProfitAndLossLineItem AS KpiName, q1.ParentLineItemID AS ParentKPIID, 
        q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.CompanyProfitAndLossLineItemMappingID AS MappingKPIId,
        q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 7 as ModuleId
    FROM
        Mapping_CompanyProfitAndLossLineItems q1
    INNER JOIN
        M_ProfitAndLoss_LineItems q2 ON q1.ProfitAndLossLineItemID = q2.ProfitAndLossLineItemID
    WHERE
        q1.IsExtraction = 1 
        AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,','))
        AND 7 IN (SELECT Item FROM dbo.SplitString(@ModuleIds,','))
        AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
    --ImpactKpi
    INSERT INTO @TempKpiMapping 
    SELECT
        q1.PortfolioCompanyID, q1.ImpactKPIID AS KpiId, q2.KPI AS KpiName, q1.ParentKPIID AS ParentKPIID, 
        q1.KPIOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.ImpactKPIMappingID AS MappingKPIId,
        q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 6 as ModuleId
    FROM
        Mapping_ImpactKPI_Order q1
    INNER JOIN
        M_ImpactKPI q2 ON q1.ImpactKPIID = q2.ImpactKPIID
    WHERE
        q1.IsExtraction = 1 
        AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,','))
        AND 6 IN (SELECT Item FROM dbo.SplitString(@ModuleIds,','))
        AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
    --CompanyKpi
    INSERT INTO @TempKpiMapping 
    SELECT
        q1.PortfolioCompanyID, q1.KpiID AS KpiId, q2.KPI AS KpiName, q1.ParentKPIID AS ParentKPIID, 
        q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.MappingPortfolioCompanyKPIId AS MappingKPIId,
        q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 5 as ModuleId
    FROM
        Mapping_PortfolioCompanyKPI q1
    INNER JOIN
        M_CompanyKPI q2 ON q1.KpiID = q2.CompanyKPIID
    WHERE
        q1.IsExtraction = 1 
        AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,','))
        AND 5 IN (SELECT Item FROM dbo.SplitString(@ModuleIds,','))
        AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
    --InvestmentKpi
    INSERT INTO @TempKpiMapping 
    SELECT
        q1.PortfolioCompanyID, q1.KpiID AS KpiId, q2.KPI AS KpiName, q1.ParentKPIID AS ParentKPIID, 
        q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.MappingPortfolioInvestmentKPIId AS MappingKPIId,
        q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 4 as ModuleId
    FROM
        Mapping_PortfolioInvestmentKPI q1
    INNER JOIN
        M_InvestmentKpi q2 ON q1.KpiID = q2.InvestmentKPIId
    WHERE
        q1.IsExtraction = 1 
        AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,','))
        AND 4 IN (SELECT Item FROM dbo.SplitString(@ModuleIds,','))
        AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
    --OperationalKpi
    INSERT INTO @TempKpiMapping 
    SELECT
        q1.PortfolioCompanyID, q1.KpiID AS KpiId, q2.KPI AS KpiName, q1.ParentKPIID AS ParentKPIID, 
        q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.MappingPortfolioOperationalKPIId AS MappingKPIId,
        q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 3 as ModuleId
    FROM
        Mapping_PortfolioOperationalKPI q1
    INNER JOIN
        M_SectorwiseOperationalKPI q2 ON q1.KpiID = q2.SectorwiseOperationalKPIID
    WHERE
        q1.IsExtraction = 1 
        AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,','))
        AND 3 IN (SELECT Item FROM dbo.SplitString(@ModuleIds,','))
        AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
    --MasterKpis
    INSERT INTO @TempKpiMapping 
    SELECT
        q1.PortfolioCompanyID, q1.KpiID AS KpiId, q2.KPI AS KpiName, q1.ParentKpiId AS ParentKPIID, 
        q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.Mapping_KpisID AS MappingKPIId,
        q2.KpiInfo, q1.IsExtraction, 
        CASE 
            WHEN q1.Synonym IS NOT NULL AND LTRIM(RTRIM(q1.Synonym)) <> '' THEN q1.Synonym
            ELSE q2.Synonym
        END AS Synonym,
        CASE WHEN q1.Definition IS NOT NULL AND LTRIM(RTRIM(q1.Definition)) <> '' THEN q1.Definition
            ELSE q2.Description
        END AS Definition
        , q2.MethodologyID, q2.ModuleId
    FROM
        Mapping_Kpis q1
    INNER JOIN
        M_MasterKpis q2 ON q1.KpiID = q2.MasterKpiID
    WHERE
        q1.IsExtraction = 1 
        AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,','))
        AND q2.ModuleId IN (SELECT Item FROM dbo.SplitString(@ModuleIds,','))
        AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
    
    SELECT * FROM @TempKpiMapping
END
GO
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'CapTable6')
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES (
        'CapTable6', 'Capitalization Table 6', 38, NULL, 1, 0, GETDATE(), 1, 6, NULL, 0, 0, 0, 0, 0, 0, 0
    )
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'CapTable7')
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES ( 
        'CapTable7', 'Capitalization Table 7', 38, NULL, 1, 0, GETDATE(), 1, 7, NULL,0, 0, 0, 0, 0, 0, 0
    )
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'CapTable8')
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES (
        'CapTable8', 'Capitalization Table 8', 38, NULL, 1, 0, GETDATE(), 1, 8, NULL, 0, 0, 0, 0, 0, 0, 0
    )
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'CapTable9')
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES (
        'CapTable9', 'Capitalization Table 9', 38, NULL, 1, 0, GETDATE(), 1, 9, NULL, 0, 0, 0, 0, 0, 0, 0
    )
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'CapTable10')
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES (
        'CapTable10', 'Capitalization Table 10', 38, NULL, 1, 0, GETDATE(), 1, 10, NULL, 0, 0, 0, 0, 0, 0, 0
    )
END
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE NAME = 'CapTable6')
    AND NOT EXISTS (
        SELECT 1 FROM MSubSectionFields 
        WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable6')
    )
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable6'),
        'Period Type', 'Period Type', 38, 1, 0, GETDATE(), 1, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE NAME = 'CapTable7')
    AND NOT EXISTS (
        SELECT 1 FROM MSubSectionFields 
        WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable7')
    )
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable7'),
        'Period Type', 'Period Type', 38, 1, 0, GETDATE(), 1, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE NAME = 'CapTable8')
    AND NOT EXISTS (
        SELECT 1 FROM MSubSectionFields 
        WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable8')
    )
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable8'),
        'Period Type', 'Period Type', 38, 1, 0, GETDATE(), 1, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE NAME = 'CapTable9')
    AND NOT EXISTS (
        SELECT 1 FROM MSubSectionFields 
        WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable9')
    )
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable9'),
        'Period Type', 'Period Type', 38, 1, 0, GETDATE(), 1, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE NAME = 'CapTable10')
    AND NOT EXISTS (
        SELECT 1 FROM MSubSectionFields 
        WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable10')
    )
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable10'),
        'Period Type', 'Period Type', 38, 1, 0, GETDATE(), 1, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
SET IDENTITY_INSERT M_SubPageDetails ON;
IF NOT EXISTS ( SELECT 1 FROM M_SubPageDetails WHERE SubPageID = 47 AND PageID = 1)
BEGIN
    INSERT INTO M_SubPageDetails (
        SubPageID,Name, AliasName, PageID, Description, isActive, isDeleted, CreatedOn, CreatedBy,SequenceNo, PagePath, IsCustom,IsDynamicFieldSupported, IsDataType, IsDragDrop, IsFootNote)
    VALUES (47,'OtherCapTable', 'Other Capitalization Tables', 1, NULL, 1, 0, GETDATE(), 3, 8, NULL, 0, 0, 0, 1, 0);
END
SET IDENTITY_INSERT M_SubPageDetails OFF;
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable1' AND SubPageID = 47)
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES (
        'OtherCapTable1', 'Other Capitalization Table 1', 47, NULL, 1, 0, GETDATE(), 3, 1, NULL, 0, 0, 0, 0, 0, 0, 0
    )
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable1'))
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable1'),
        'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable2' AND SubPageID = 47)
BEGIN
INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable2', 'Other Capitalization Table 2', 47, NULL, 1, 0, GETDATE(), 3, 2, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable2'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable2'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 2, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable3' AND SubPageID = 47)
BEGIN
 INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable3', 'Other Capitalization Table 3', 47, NULL, 1, 0, GETDATE(), 3, 3, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable3'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable3'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 3, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable4' AND SubPageID = 47)
BEGIN
 INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable4', 'Other Capitalization Table 4', 47, NULL, 1, 0, GETDATE(), 3, 4, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable4'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable4'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 4, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable5' AND SubPageID = 47)
BEGIN
 INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable5', 'Other Capitalization Table 5', 47, NULL, 1, 0, GETDATE(), 3, 5, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable5'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable5'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 5, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable6' AND SubPageID = 47)
BEGIN
 INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable6', 'Other Capitalization Table 6', 47, NULL, 1, 0, GETDATE(), 3, 6, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable6'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable6'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 6, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable7' AND SubPageID = 47)
BEGIN
 INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable7', 'Other Capitalization Table 7', 47, NULL, 1, 0, GETDATE(), 3, 7, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable7'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable7'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 7, 'Monthly,Quarterly,Annual',  'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable8' AND SubPageID = 47)
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
    VALUES ('OtherCapTable8', 'Other Capitalization Table 8', 47, NULL, 1, 0, GETDATE(), 3, 8, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable8'))
BEGIN
    INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable8'),
        'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 8, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable9' AND SubPageID = 47)
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
    VALUES ('OtherCapTable9', 'Other Capitalization Table 9', 47, NULL, 1, 0, GETDATE(), 3, 9, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable9'))
BEGIN
    INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable9'),
        'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 9, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable10' AND SubPageID = 47)
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
    VALUES ('OtherCapTable10', 'Other Capitalization Table 10', 47, NULL, 1, 0, GETDATE(), 3, 10, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable10'))
BEGIN
    INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable10'),
        'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 10, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
GO
IF (NOT EXISTS (SELECT * FROM [dbo].[M_KpiModules] where Name='CapTable6'))
		BEGIN
		SET IDENTITY_INSERT [dbo].[M_KpiModules] ON
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(31,'CapTable6',3,GETDATE(),0,1,0,0,11,'Cap Table6','Cap Table6','CapTable6')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(32,'CapTable7',3,GETDATE(),0,1,0,0,12,'Cap Table7','Cap Table7','CapTable7')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(33,'CapTable8',3,GETDATE(),0,1,0,0,13,'Cap Table8','Cap Table8','CapTable8')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(34,'CapTable9',3,GETDATE(),0,1,0,0,14,'Cap Table9','Cap Table9','CapTable9')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(35,'CapTable10',3,GETDATE(),0,1,0,0,15,'Cap Table10','Cap Table10','CapTable10')

		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(36,'OtherCapTable1',3,GETDATE(),0,1,0,0,11,'Cap Table1','Cap Table1','OtherCapTable1')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(37,'OtherCapTable2',3,GETDATE(),0,1,0,0,12,'Cap Table2','Cap Table2','OtherCapTable2')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(38,'OtherCapTable3',3,GETDATE(),0,1,0,0,13,'Cap Table3','Cap Table3','OtherCapTable3')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(39,'OtherCapTable4',3,GETDATE(),0,1,0,0,14,'Cap Table4','Cap Table4','OtherCapTable4')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(40,'OtherCapTable5',3,GETDATE(),0,1,0,0,15,'Cap Table5','Cap Table5','OtherCapTable5')		
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(41,'OtherCapTable6',3,GETDATE(),0,1,0,0,11,'Cap Table6','Cap Table6','OtherCapTable6')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(42,'OtherCapTable7',3,GETDATE(),0,1,0,0,12,'Cap Table7','Cap Table7','OtherCapTable7')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(43,'OtherCapTable8',3,GETDATE(),0,1,0,0,13,'Cap Table8','Cap Table8','OtherCapTable8')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(44,'OtherCapTable9',3,GETDATE(),0,1,0,0,14,'Cap Table9','Cap Table9','OtherCapTable9')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(45,'OtherCapTable10',3,GETDATE(),0,1,0,0,15,'Cap Table10','Cap Table10','OtherCapTable10')
		SET IDENTITY_INSERT [dbo].[M_KpiModules] OFF
		END
GO
ALTER  PROCEDURE [dbo].[GetMasterkpiNotMappedList]
(
@companyId INT,
@Type Varchar(100),
@ModuleId INT = NULL
)
AS
BEGIN  
	IF @Type = 'Company KPI' 
							BEGIN
							SELECT DISTINCT(M.CompanyKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId', M.KpiInfo 'KpiInfo',M.Synonym,M.Description   FROM M_CompanyKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioCompanyKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.CompanyKPIID = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MappingPortfolioCompanyKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Impact KPI'
							BEGIN
							SELECT DISTINCT(M.ImpactKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_ImpactKPI M 
							LEFT JOIN (SELECT *FROM Mapping_ImpactKPI_Order 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.ImpactKPIID = MAP.ImpactKPIID 
							WHERE M.IsDeleted =0  AND MAP.ImpactKPIMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Investment KPI'
							BEGIN
							SELECT DISTINCT(M.InvestmentKPIId) 'Id',M.KPI 'Name', M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_InvestmentKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioInvestmentKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.InvestmentKPIId = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MAP.MappingPortfolioInvestmentKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Balance Sheet KPI'
							BEGIN
							SELECT DISTINCT(M.BalanceSheetLineItemID) 'Id',M.BalanceSheetLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_BalanceSheet_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyBalanceSheetLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.BalanceSheetLineItemID = MAP.BalanceSheetLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyBalanceSheetLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Cashflow KPI'
							BEGIN
							SELECT DISTINCT(M.CashFlowLineItemID) 'Id',M.CashFlowLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_CashFlow_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyCashFlowLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.CashFlowLineItemID = MAP.CashFlowLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyCashFlowLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Profit & Loss KPI'
							BEGIN
							SELECT DISTINCT(M.ProfitAndLossLineItemID) 'Id',M.ProfitAndLossLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_ProfitAndLoss_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyProfitAndLossLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.ProfitAndLossLineItemID = MAP.ProfitAndLossLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyProfitAndLossLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Operational KPI'
							BEGIN
							SELECT DISTINCT(M.SectorwiseOperationalKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_SectorwiseOperationalKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioOperationalKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.SectorwiseOperationalKPIID = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MAP.MappingPortfolioOperationalKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Credit KPI'
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = 2) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = 2  AND Mapping_KpisID IS NULL order by Name
							END
	ELSE IF @Type = 'Trading Records'
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = 1) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = 1  AND Mapping_KpisID IS NULL order by Name
							END
    IF @ModuleId = 16
							BEGIN
							SELECT DISTINCT(M.KpiId) 'Id',M.Kpi 'Name',M.IsHeader 'IsHeader',M.IsBoldKpi 'IsBoldKPI',M.Formula 'Formula',M.FormulaKpiId 'FormulaKPIId',M.KpiInfo 'KpiInfo'  FROM MMonthlyReport M 
							LEFT JOIN (SELECT *FROM MappingMonthlyReport 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.KpiId = MAP.KpiId
							WHERE M.IsDeleted =0 AND MappingId IS NULL order by Name
							END
   IF @ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45)
							BEGIN
							SELECT DISTINCT(M.KpiId) 'Id',M.Kpi 'Name',M.IsHeader 'IsHeader',M.IsBoldKpi 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',KpiTypeId,M.Synonym,M.Description  FROM MCapTable M 
							LEFT JOIN (SELECT *FROM MappingCapTable 
							where PortfolioCompanyId = @companyId AND IsDeleted=0 AND ModuleID = @ModuleId) MAP ON M.KpiId = MAP.KpiId 
							WHERE M.IsDeleted =0 AND M.ModuleID = @ModuleId  AND MappingId IS NULL order by Name
							END
   IF @ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30)
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = @ModuleId) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = @ModuleId  AND Mapping_KpisID IS NULL order by Name
							END
END
GO
ALTER PROCEDURE [dbo].[ProcCopyKPIToCompanies]
(
@KpiType VARCHAR(100),
@CompanyId INT,
@UserId INT,
@CompanyIds NVARCHAR(MAX),
@ModuleId INT = NULL
)
AS
BEGIN
SET NOCOUNT ON
			BEGIN TRY
			DECLARE @RowCount INT,@Row INT = 1;
			DROP TABLE IF EXISTS #tempCopyToCompany
			CREATE TABLE #tempCopyToCompany
			(
				Id INT IDENTITY(1,1) PRIMARY KEY,
				PortfolioCompanyID Int
			)
			INSERT INTO #tempCopyToCompany(PortfolioCompanyID)
			SELECT Item AS PortfolioCompanyID FROM[dbo].[SplitString](@CompanyIds,',') WHERE Item!=@CompanyId
			SET @RowCount = (SELECT Count(PortfolioCompanyID) FROM #tempCopyToCompany)
			WHILE (@Row <= @RowCount)
			BEGIN
			DROP TABLE IF EXISTS #KPIHierarchy;
            DROP TABLE IF EXISTS #ExistingKPIs;
			CREATE TABLE #ExistingKPIs ( KPIName VARCHAR(500), DestKpiID INT, ParentKPIID INT, ParentKPIName VARCHAR(500), DisplayOrder INT, IsRootLevel BIT ,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			CREATE TABLE #KPIHierarchy ( SourceKpiID INT, KPIName VARCHAR(MAX), ParentKPIID INT, ParentKPIName VARCHAR(MAX), Level INT, DisplayOrder INT, HierarchyOrder VARCHAR(MAX), NewDisplayOrder INT, KPITypeID INT ,Formula NVarchar(Max),FormulaKPIId INT,CreatedBy INT,CreatedOn datetime,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @PortfolioCompanyID INT
			SELECT @PortfolioCompanyID=PortfolioCompanyID FROM #tempCopyToCompany Where ID= @Row
			DECLARE @MappingOperational table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCompany table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingImpact table(ID INT IDENTITY(1,1), PortfolioCompanyID int,ImpactKPIID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,KPIOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingInvestment table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),KPIHierarchy Nvarchar(Max),HierarchyOrder NVARCHAR(500),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingBalance table(ID INT IDENTITY(1,1), PortfolioCompanyID int,BalanceSheetLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingProfit table(ID INT IDENTITY(1,1), PortfolioCompanyID int,ProfitAndLossLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCashflow table(ID INT IDENTITY(1,1), PortfolioCompanyID int,CashFlowLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingTrading table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCredit table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCapTable table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingMonthlyReport table(ID INT IDENTITY(1,1),PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,Formula nvarchar(max),FormulaKPIId nvarchar(max));
			DELETE FROM @MappingOperational
			DELETE FROM @MappingCompany
			DELETE FROM @MappingImpact
			DELETE FROM @MappingInvestment
			DELETE FROM @MappingBalance
			DELETE FROM @MappingProfit
			DELETE FROM @MappingCashflow
			DELETE FROM @MappingTrading
			DELETE FROM @MappingCredit
			DELETE FROM @MappingCapTable
			DELETE FROM @MappingMonthlyReport
			Declare @MaxOrderId INT;
			Declare @ID INT,@ParentKPIID INT,@DestParentKPIID INT,@KpiID INT;
			Declare @KPI NVarchar(MAX)=NULL;
			IF(@KpiType = 'Operational KPI')
			BEGIN
				INSERT INTO @MappingOperational 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_PortfolioOperationalKPI where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(SELECT * FROM Mapping_PortfolioOperationalKPI  WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingOperational ))
					BEGIN
						UPDATE Mapping_PortfolioOperationalKPI SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioOperationalKPI A Inner Join @MappingOperational B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID 
					END							
				INSERT INTO Mapping_PortfolioOperationalKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction FROM  @MappingOperational WHERE  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioOperationalKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
			IF(@KpiType = 'Company KPI')
			BEGIN
			 	INSERT INTO @MappingCompany 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_PortfolioCompanyKPI where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_PortfolioCompanyKPI where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID  and KpiID IN (SELECT KpiID FROM @MappingCompany ))
					BEGIN
						UPDATE Mapping_PortfolioCompanyKPI SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioCompanyKPI A Inner Join @MappingCompany B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_PortfolioCompanyKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCompany where  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioCompanyKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
			IF(@KpiType = 'Impact KPI')
			BEGIN
			 	INSERT INTO @MappingImpact 
				SELECT PortfolioCompanyID,ImpactKPIID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_ImpactKPI_Order where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_ImpactKPI_Order where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID  and ImpactKPIID IN (SELECT ImpactKPIID FROM @MappingImpact ))
					BEGIN
						UPDATE Mapping_ImpactKPI_Order SET KPIOrder=B.KPIOrder,ImpactKPIID=B.ImpactKPIID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_ImpactKPI_Order A Inner Join @MappingImpact B On A.ImpactKPIID=B.ImpactKPIID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_ImpactKPI_Order(PortfolioCompanyID,ImpactKPIID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,ImpactKPIID,CreatedBy,GETDATE(),0,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingImpact where ImpactKPIID NOT IN (SELECT ImpactKPIID FROM Mapping_ImpactKPI_Order WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
				
			END
			IF(@KpiType = 'Investment KPI')
			BEGIN	
				SELECT @MaxOrderId = ISNULL(MAX(DisplayOrder), 0) FROM Mapping_PortfolioInvestmentKPI WHERE IsDeleted = 0 AND PortfolioCompanyID = @PortfolioCompanyID;
				;WITH KPIHierarchyCTE AS (
                    SELECT 
                        src.KpiID AS SourceKpiID,
                         CAST(srcKPI.KPI AS VARCHAR(MAX)) AS KPIName,
                        src.ParentKPIID,
                         CAST(NULL AS VARCHAR(MAX)) AS ParentKPIName,
                        0 AS Level,
                        src.DisplayOrder,
						CAST(RIGHT('000000' + CAST(DisplayOrder AS VARCHAR(6)), 6) AS VARCHAR(MAX)) AS HierarchyOrder,
						src.KPITypeID,src.Formula,src.FormulaKPIId,src.CreatedBy,src.CreatedOn,src.Synonym,src.Definition,src.IsExtraction
                    FROM Mapping_PortfolioInvestmentKPI src
                    INNER JOIN M_InvestmentKPI srcKPI 
                        ON src.KpiID = srcKPI.InvestmentKPIId
                    WHERE src.PortfolioCompanyID = @CompanyId
                    AND src.ParentKPIID IS NULL
                    AND src.IsDeleted = 0
                    UNION ALL
                    SELECT 
                        src.KpiID,
                        CAST(srcKPI.KPI AS VARCHAR(MAX)),
                        src.ParentKPIID,
                        CAST(parentKPI.KPI AS VARCHAR(MAX)),
                        h.Level + 1,
                        src.DisplayOrder,
						 CAST(h.HierarchyOrder + '.' + RIGHT('000000' + CAST(src.DisplayOrder AS VARCHAR(6)), 6) AS VARCHAR(MAX)) as HierarchyOrder,
						 src.KPITypeID,src.Formula,src.FormulaKPIId,src.CreatedBy,src.CreatedOn,src.Synonym,src.Definition,src.IsExtraction
                    FROM Mapping_PortfolioInvestmentKPI src
                    INNER JOIN M_InvestmentKPI srcKPI 
                        ON src.KpiID = srcKPI.InvestmentKPIId
                    INNER JOIN M_InvestmentKPI parentKPI 
                        ON src.ParentKPIID = parentKPI.InvestmentKPIId
                    INNER JOIN KPIHierarchyCTE h 
                        ON src.ParentKPIID = h.SourceKpiID
                    WHERE src.PortfolioCompanyID = @CompanyId
                    AND src.IsDeleted = 0
                )
				 INSERT INTO #KPIHierarchy(KPIName,ParentKPIID,ParentKPIName,Level,DisplayOrder,SourceKpiID,HierarchyOrder,NewDisplayOrder,KPITypeID,Formula,FormulaKPIId,CreatedBy,CreatedOn,Synonym,Definition,IsExtraction)
                SELECT KPIName,ParentKPIID,ParentKPIName,Level,DisplayOrder,SourceKpiID,HierarchyOrder,CASE WHEN @MaxOrderId=0 THEN
				        ROW_NUMBER() OVER (ORDER BY HierarchyOrder) 
						ELSE @MaxOrderId+1 + ROW_NUMBER() OVER (ORDER BY HierarchyOrder) END AS NewDisplayOrder,KPITypeID,Formula,FormulaKPIId,CreatedBy,CreatedOn,Synonym,Definition,IsExtraction FROM KPIHierarchyCTE;

				-- Get existing destination KPIs
                INSERT INTO #ExistingKPIs
                SELECT DISTINCT
                    destKPI.KPI,
                    dest.KpiID,
                    dest.ParentKPIID,
                    parentKPI.KPI,
                    dest.DisplayOrder,
                    CASE WHEN dest.ParentKPIID IS NULL THEN 1 ELSE 0 END,
					dest.Synonym,
					dest.Definition,
					dest.IsExtraction
                FROM Mapping_PortfolioInvestmentKPI dest
                INNER JOIN M_InvestmentKPI destKPI 
                    ON dest.KpiID = destKPI.InvestmentKPIId
                LEFT JOIN M_InvestmentKPI parentKPI 
                    ON dest.ParentKPIID = parentKPI.InvestmentKPIId
                WHERE dest.PortfolioCompanyID = @PortfolioCompanyID
                AND dest.IsDeleted = 0;

				--SELECT * FROM #KPIHierarchy

				INSERT INTO @MappingInvestment(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,HierarchyOrder,Synonym,Definition,IsExtraction) 
				SELECT 
                    kh.KPITypeID,
                    @PortfolioCompanyID,
                    kh.SourceKpiID,
                    @UserId,
                    GETDATE(),
                    0,
                    CASE 
					    WHEN kh.ParentKPIID IS NOT NULL THEN
					        CASE WHEN EXISTS (
					            SELECT 1 
					            FROM #ExistingKPIs e 
					            WHERE e.KPIName = kh.ParentKPIName
					            AND e.IsRootLevel = 1
					        )
					        THEN 
					            (SELECT TOP 1 e.DestKpiID 
					             FROM #ExistingKPIs e 
					             WHERE e.KPIName = kh.ParentKPIName
					             AND e.IsRootLevel = 1)
					        ELSE kh.ParentKPIID  -- Use ParentKPIID instead of NULL
					        END
					    ELSE kh.ParentKPIID  -- Use ParentKPIID instead of NULL
					END ParentKPIID,
					kh.NewDisplayOrder,kh.Formula,kh.FormulaKPIId,kh.HierarchyOrder,kh.Synonym,kh.Definition,kh.IsExtraction
                FROM #KPIHierarchy kh
                LEFT JOIN #ExistingKPIs existing
                    ON existing.KPIName = kh.KPIName
                    AND existing.IsRootLevel = CASE WHEN kh.ParentKPIID IS NULL THEN 1 ELSE 0 END
                WHERE NOT EXISTS (
                    SELECT 1 FROM #ExistingKPIs e
                    WHERE e.KPIName = kh.KPIName
                    AND (
                        (kh.ParentKPIID IS NULL AND e.IsRootLevel = 1) OR
                        (kh.ParentKPIID IS NOT NULL AND e.ParentKPIName = kh.ParentKPIName)
                    )
                );
			   IF EXISTS(select *from Mapping_PortfolioInvestmentKPI where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingInvestment))
				BEGIN
					UPDATE Mapping_PortfolioInvestmentKPI SET DisplayOrder=B.DisplayOrder ,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioInvestmentKPI A Inner Join @MappingInvestment B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
			   END
			   INSERT INTO Mapping_PortfolioInvestmentKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
			   SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingInvestment where  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioInvestmentKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
				
				DROP TABLE IF EXISTS #KPIHierarchy;
                DROP TABLE IF EXISTS #ExistingKPIs;
			
			END
			IF(@KpiType = 'Balance Sheet KPI')
			BEGIN
			 	INSERT INTO @MappingBalance 
				SELECT PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyBalanceSheetLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_CompanyBalanceSheetLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and BalanceSheetLineItemID IN (SELECT BalanceSheetLineItemID FROM @MappingBalance))
					BEGIN
						UPDATE Mapping_CompanyBalanceSheetLineItems SET DisplayOrder=B.DisplayOrder,BalanceSheetLineItemID=B.BalanceSheetLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyBalanceSheetLineItems A Inner Join @MappingBalance B On A.BalanceSheetLineItemID=B.BalanceSheetLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyBalanceSheetLineItems(PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingBalance where  BalanceSheetLineItemID NOT IN (SELECT BalanceSheetLineItemID FROM Mapping_CompanyBalanceSheetLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Cashflow KPI')
			BEGIN
				INSERT INTO @MappingCashflow 
				SELECT PortfolioCompanyID,CashFlowLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyCashFlowLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_CompanyCashFlowLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and CashFlowLineItemID IN (SELECT CashFlowLineItemID FROM @MappingCashflow))
					BEGIN
						UPDATE Mapping_CompanyCashFlowLineItems SET DisplayOrder=B.DisplayOrder,CashFlowLineItemID=B.CashFlowLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyCashFlowLineItems A Inner Join @MappingCashflow B On A.CashFlowLineItemID=B.CashFlowLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyCashFlowLineItems(PortfolioCompanyID,CashFlowLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,CashFlowLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCashflow where  CashFlowLineItemID NOT IN (SELECT CashFlowLineItemID FROM Mapping_CompanyCashFlowLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Profit & Loss KPI')
			BEGIN
				INSERT INTO @MappingProfit 
				SELECT PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyProfitAndLossLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_CompanyProfitAndLossLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ProfitAndLossLineItemID IN (SELECT ProfitAndLossLineItemID FROM @MappingProfit))
					BEGIN
						UPDATE Mapping_CompanyProfitAndLossLineItems SET DisplayOrder=B.DisplayOrder,ProfitAndLossLineItemID=B.ProfitAndLossLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyProfitAndLossLineItems A Inner Join @MappingProfit B On A.ProfitAndLossLineItemID=B.ProfitAndLossLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyProfitAndLossLineItems(PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingProfit where  ProfitAndLossLineItemID NOT IN (SELECT ProfitAndLossLineItemID FROM Mapping_CompanyProfitAndLossLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Trading Records')
			BEGIN
			 	INSERT INTO @MappingTrading 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_Kpis where PortfolioCompanyID = @CompanyId  AND ModuleID=1 AND IsDeleted = 0
				
				IF EXISTS(select *from Mapping_Kpis where ModuleID=1 and IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingTrading))
					BEGIN
						UPDATE Mapping_Kpis SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_Kpis A Inner Join @MappingTrading B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID AND A.ModuleID=1
					END							
				INSERT INTO Mapping_Kpis(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingTrading where  KpiID NOT IN (SELECT KpiID FROM Mapping_Kpis WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ModuleID=1 )
												
			END
		IF(@KpiType = 'Credit KPI' OR @ModuleId IN (2, 17,18,19,20,21,22,23,24,25,26,27,28,29,30))
			BEGIN
			 	INSERT INTO @MappingCredit 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_Kpis where PortfolioCompanyID = @CompanyId  AND ModuleID=@ModuleId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_Kpis where ModuleID=@ModuleId and IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingCredit))
				BEGIN
						UPDATE Mapping_Kpis SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_Kpis A Inner Join @MappingCredit B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID AND A.ModuleID=@ModuleId
				END							
				INSERT INTO Mapping_Kpis(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCredit where  KpiID NOT IN (SELECT KpiID FROM Mapping_Kpis WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ModuleID=@ModuleId )												
			END
			IF(@ModuleId = 16)
			BEGIN
			 	INSERT INTO @MappingMonthlyReport 
				SELECT PortfolioCompanyId,KpiId,CreatedBy,GETUTCDATE(),IsDeleted,ParentKpiId,DisplayOrder,IsHeader,Formula,FormulaKpiId From MappingMonthlyReport where PortfolioCompanyId = @CompanyId  AND IsDeleted = 0
				IF EXISTS(select *from MappingMonthlyReport where  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and KpiId IN (SELECT KpiID FROM @MappingMonthlyReport))
				BEGIN
						UPDATE MappingMonthlyReport SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiID,ParentKpiId=B.ParentKpiId,Formula=B.Formula,FormulaKpiId=B.FormulaKpiId FROM MappingMonthlyReport A Inner Join @MappingMonthlyReport B On A.KpiId=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyId= @PortfolioCompanyID
				END							
				INSERT INTO MappingMonthlyReport(PortfolioCompanyId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,IsHeader,Formula,FormulaKPIId) 
				SELECT @PortfolioCompanyID,KpiID,CreatedBy,GETUTCDATE(),0,ParentKPIID,DisplayOrder,IsHeader,Formula,FormulaKPIId From @MappingMonthlyReport where  KpiID NOT IN (SELECT KpiID FROM MappingMonthlyReport WHERE  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID)												
			END
		IF(@ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45))
			BEGIN
			 	INSERT INTO @MappingCapTable 
				SELECT 1,PortfolioCompanyId,KpiId,CreatedBy,GETDATE(),IsDeleted,ParentKpiId,DisplayOrder,IsHeader,ModuleId,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From MappingCapTable where PortfolioCompanyId = @CompanyId  AND ModuleId=@ModuleId AND IsDeleted = 0
				IF EXISTS(select *from MappingCapTable where ModuleID=@ModuleId and IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and KpiId IN (SELECT KpiID FROM @MappingCapTable))
				BEGIN
						UPDATE MappingCapTable SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiID,ParentKpiId=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM MappingCapTable A Inner Join @MappingCapTable B On A.KpiId=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyId= @PortfolioCompanyID AND A.ModuleId=@ModuleId
				END							
				INSERT INTO MappingCapTable(PortfolioCompanyId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,IsHeader,ModuleId,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCapTable where  KpiID NOT IN (SELECT KpiID FROM MappingCapTable WHERE  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and ModuleId=@ModuleId)												
			END
			SET @Row = @Row + 1
			END
			DROP TABLE IF EXISTS #tempCopyToCompany		
			END TRY
			BEGIN CATCH
              IF OBJECT_ID('tempdb..#KPIHierarchy') IS NOT NULL DROP TABLE #KPIHierarchy;
			  IF OBJECT_ID('tempdb..#ExistingKPIs') IS NOT NULL DROP TABLE #ExistingKPIs;
			END CATCH
END
GO
IF NOT EXISTS (
    SELECT * FROM sys.columns 
    WHERE object_id = OBJECT_ID('M_SubPageFields') 
    AND name = 'IsPcLink'
)
BEGIN
    ALTER TABLE M_SubPageFields
    ADD IsPcLink BIT NOT NULL DEFAULT 0;
    
    PRINT 'IsPcLink column added to M_SubPageFields table';
END
ELSE
BEGIN
    PRINT 'IsPcLink column already exists in M_SubPageFields table';
END 
go
UPDATE M_SubPageFields SET IsPcLink =0 WHERE IsPcLink IS NULL
GO
IF (NOT EXISTS (SELECT *
                 FROM [dbo].[M_SubPageDetails] 
				 where Name='Fund Key Performance Indicator'    
                ))
BEGIN
SET IDENTITY_INSERT [dbo].[M_SubPageDetails] ON 
INSERT [dbo].[M_SubPageDetails] ([SubPageID], [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported],IsDragDrop,IsDataType) VALUES (49, N'Fund Key Performance Indicator', N'Fund Key Performance Indicator', 2, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 7, NULL, 0, 0,1,0)
SET IDENTITY_INSERT [dbo].[M_SubPageDetails] OFF
END
GO

IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials1'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials1', N'FundFinancials1', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 2, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials2'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials2', N'FundFinancials2', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 3, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials3'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials3', N'FundFinancials3', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 4, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials4'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials4', N'FundFinancials4', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 5, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials5'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials5', N'FundFinancials5', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 6, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials6'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials6', N'FundFinancials6', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 7, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials7'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials7', N'FundFinancials7', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 8, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials8'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials8', N'FundFinancials8', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 8, NULL, 0)
END
GO
/* Fund Financial Section */
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKPI1'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKPI1', N'FundKPI1', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKPI2'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKPI2', N'FundKPI2', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 2, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKPI3'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKPI3', N'FundKPI3', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 3, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKPI4'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKPI4', N'FundKPI4', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 4, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKPI5'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKPI5', N'FundKPI5', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 5, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKPI6'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKPI6', N'FundKPI6', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 6, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKPI7'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKPI7', N'FundKPI7', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 7, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKPI8'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKPI8', N'FundKPI8', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 8, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials8'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go

IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI1'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI2'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI3'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI4'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI5'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI6'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI7'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKPI8'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
GO
IF (NOT EXISTS (SELECT *
                 FROM [dbo].[MFundKpiModules] where Name='FundFinancials1'
                ))
		BEGIN
		SET IDENTITY_INSERT [dbo].[MFundKpiModules] ON
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1002,'FundFinancials1',3,GETDATE(),0,1,0,2,'Fund Financials1','Fund Financials1','FundFinancials1')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1003,'FundFinancials2',3,GETDATE(),0,1,0,3,'Fund Financials2','Fund Financials2','FundFinancials2')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1004,'FundFinancials3',3,GETDATE(),0,1,0,4,'Fund Financials3','Fund Financials3','FundFinancials3')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1005,'FundFinancials4',3,GETDATE(),0,1,0,5,'Fund Financials4','Fund Financials4','FundFinancials4')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1006,'FundFinancials5',3,GETDATE(),0,1,0,6,'Fund Financials5','Fund Financials5','FundFinancials5')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1007,'FundFinancials6',3,GETDATE(),0,1,0,7,'Fund Financials6','Fund Financials6','FundFinancials6')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1008,'FundFinancials7',3,GETDATE(),0,1,0,8,'Fund Financials7','Fund Financials7','FundFinancials7')
		SET IDENTITY_INSERT [dbo].[MFundKpiModules] OFF
		END
GO
IF (NOT EXISTS (SELECT *
                 FROM [dbo].[MFundKpiModules] where Name='FundKPI1'
                ))
		BEGIN
		SET IDENTITY_INSERT [dbo].[MFundKpiModules] ON
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1009,'FundKPI1',3,GETDATE(),0,1,0,11,'FundKPI1','FundKPI1','FundKPI1')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1010,'FundKPI2',3,GETDATE(),0,1,0,12,'FundKPI2','FundKPI2','FundKPI2')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1011,'FundKPI3',3,GETDATE(),0,1,0,13,'FundKPI3','FundKPI3','FundKPI3')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1012,'FundKPI4',3,GETDATE(),0,1,0,14,'FundKPI4','FundKPI4','FundKPI4')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1013,'FundKPI5',3,GETDATE(),0,1,0,15,'FundKPI5','FundKPI5','FundKPI5')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1014,'FundKPI6',3,GETDATE(),0,1,0,16,'FundKPI6','FundKPI6','FundKPI6')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1015,'FundKPI7',3,GETDATE(),0,1,0,17,'FundKPI7','FundKPI7','FundKPI7')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1016,'FundKPI8',3,GETDATE(),0,1,0,18,'FundKPI8','FundKPI8','FundKPI8')
		SET IDENTITY_INSERT [dbo].[MFundKpiModules] OFF
		END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'M_SubPageFields')
    AND NOT EXISTS (
        SELECT * FROM sys.columns 
        WHERE object_id = OBJECT_ID('dbo.M_SubPageFields') 
        AND name = 'IsTrend'
    )
BEGIN
    ALTER TABLE dbo.M_SubPageFields ADD IsTrend BIT NOT NULL DEFAULT 0;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[GetCurrencyRates]'))
BEGIN
	 DROP PROCEDURE GetCurrencyRates
END
GO
CREATE PROCEDURE GetCurrencyRates
    @FromDate DATE = NULL,
    @ToDate DATE = NULL,
    @FilterSource NVARCHAR(500) = NULL,
    @FromCurrencyCode NVARCHAR(10) = NULL,
    @ToCurrencyCode NVARCHAR(10) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SELECT  FromCurrencyId,
            FromCurrencyCode,
            ToCurrencyId,
            ToCurrencyCode,
            CONVERT(varchar, Rate) AS Rate,
            FORMAT(CAST(Date AS date), 'MMM yyyy') AS [Date],
            Source
    FROM (
        SELECT 
            cr.FromCurrencyId,
            fc.CurrencyCode AS FromCurrencyCode,
            cr.ToCurrencyId,
            tc.CurrencyCode AS ToCurrencyCode,
            Rate,
            Date,
            'Bulk Upload' AS Source
        FROM 
            CurrencyRates cr 
            LEFT JOIN M_Currency fc ON cr.FromCurrencyId = fc.CurrencyID
            LEFT JOIN M_Currency tc ON cr.ToCurrencyId = tc.CurrencyID
        WHERE 
            cr.IsDeleted = 0

        UNION ALL

        SELECT 
            cr.FromCurrencyId,
            fc.CurrencyCode AS FromCurrencyCode,
            cr.ToCurrencyId,
            tc.CurrencyCode AS ToCurrencyCode,
            CONVERT(varchar, cr.Rate) AS Rate,
            CONVERT(varchar, cr.Date) AS [Date],
            'System API' AS Source
        FROM 
            ApiCurrencyRates cr
            LEFT JOIN M_Currency fc ON cr.FromCurrencyId = fc.CurrencyID
            LEFT JOIN M_Currency tc ON cr.ToCurrencyId = tc.CurrencyID
        WHERE 
            cr.IsDeleted = 0
    ) AS AllRates
    WHERE
        (@FromDate IS NULL OR @FromDate = '' OR  AllRates.Date >= @FromDate)
        AND (@ToDate IS NULL OR @ToDate = '' OR AllRates.Date <= @ToDate)
        AND (@FromCurrencyCode IS NULL OR @FromCurrencyCode = '' OR AllRates.FromCurrencyCode = @FromCurrencyCode)
        AND (@ToCurrencyCode IS NULL OR @ToCurrencyCode = '' OR AllRates.ToCurrencyCode = @ToCurrencyCode)
        AND (
            @FilterSource IS NULL OR @FilterSource = ''
            OR EXISTS (
                SELECT 1
                FROM STRING_SPLIT(REPLACE(@FilterSource, ' ', ''), ',')
                WHERE value = REPLACE(AllRates.Source, ' ', '')
            )
        )
END
GO
ALTER PROCEDURE   [dbo].[spDeleteKpi]
(
@KpiType VARCHAR(500),
@ModuleId INT = NULL,
@KPIId INT
)
AS BEGIN
	SET NOCOUNT ON;
	IF(@KpiType = 'TradingRecords' OR @KpiType = 'CreditKPI' OR @KpiType LIKE '%Custom%' OR @KpiType LIKE '%OtherKpi%')
	BEGIN
		UPDATE M_MasterKpis SET IsDeleted = 1 WHERE MasterKpiID = @KPIId AND ModuleID= @ModuleId
		UPDATE Mapping_Kpis SET ParentKPIID = NULL WHERE ParentKPIID = @KPIId
		UPDATE Mapping_Kpis SET IsDeleted = 1 WHERE KpiID = @KPIId
	END
	ELSE IF(@KpiType = 'Operational')
	BEGIN
		UPDATE M_SectorwiseOperationalKPI SET IsDeleted = 1 WHERE SectorwiseOperationalKPIID = @KPIId
		UPDATE Mapping_PortfolioOperationalKPI SET IsDeleted = 1 WHERE KpiID = @KPIId
		UPDATE Mapping_PortfolioOperationalKPI SET ParentKPIID = NULL WHERE ParentKPIID = @KPIId
	END
	ELSE IF(@KpiType = 'Investment')
	BEGIN
		UPDATE M_InvestmentKPI SET IsDeleted = 1 WHERE InvestmentKPIId = @KPIId
		UPDATE Mapping_PortfolioInvestmentKPI SET ParentKPIID = NULL WHERE ParentKPIID = @KPIId
		UPDATE Mapping_PortfolioInvestmentKPI SET IsDeleted = 1 WHERE KpiID = @KPIId
	END
	ELSE IF(@KpiType = 'Company')
	BEGIN
		UPDATE M_CompanyKPI SET IsDeleted = 1 WHERE CompanyKPIID = @KPIId
		UPDATE Mapping_PortfolioCompanyKPI SET ParentKPIID = NULL WHERE ParentKPIID = @KPIId
		UPDATE Mapping_PortfolioCompanyKPI SET IsDeleted = 1 WHERE KpiID = @KPIId
	END
	ELSE IF(@KpiType = 'Impact')
	BEGIN
		UPDATE M_ImpactKPI SET IsDeleted = 1 WHERE ImpactKPIID = @KPIId
		UPDATE Mapping_ImpactKPI_Order SET ParentKPIID = NULL WHERE ParentKPIID = @KPIId
		UPDATE Mapping_ImpactKPI_Order SET IsDeleted = 1 WHERE ImpactKPIID = @KPIId
	END
	ELSE IF(@KpiType = 'ProfitAndLoss')
	BEGIN
		UPDATE M_ProfitAndLoss_LineItems SET IsDeleted = 1 WHERE ProfitAndLossLineItemID = @KPIId
		UPDATE Mapping_CompanyProfitAndLossLineItems SET ParentLineItemID = NULL WHERE ParentLineItemID = @KPIId
		UPDATE Mapping_CompanyProfitAndLossLineItems SET IsDeleted = 1 WHERE ProfitAndLossLineItemID = @KPIId
	END
	ELSE IF(@KpiType = 'BalanceSheet')
	BEGIN
		UPDATE M_BalanceSheet_LineItems SET IsDeleted = 1 WHERE BalanceSheetLineItemID = @KPIId
		UPDATE Mapping_CompanyBalanceSheetLineItems SET ParentLineItemID = NULL WHERE ParentLineItemID = @KPIId
		UPDATE Mapping_CompanyBalanceSheetLineItems SET IsDeleted = 1 WHERE BalanceSheetLineItemID = @KPIId
	END
	ELSE IF(@KpiType = 'CashFlow')
	BEGIN
		UPDATE M_CashFlow_LineItems SET IsDeleted = 1 WHERE CashFlowLineItemID = @KPIId
	    UPDATE Mapping_CompanyCashFlowLineItems SET ParentLineItemID = NULL WHERE ParentLineItemID = @KPIId
		UPDATE Mapping_CompanyCashFlowLineItems SET IsDeleted = 1 WHERE CashFlowLineItemID = @KPIId
	END	
	ELSE IF(@KpiType LIKE '%CapTable%')
	BEGIN
		UPDATE MCapTable SET IsDeleted = 1 WHERE KpiId = @KPIId AND ModuleId = @ModuleId
	    UPDATE MappingCapTable SET ParentKpiId = NULL WHERE ParentKpiId = @KPIId AND ModuleId = @ModuleId
		UPDATE MappingCapTable SET IsDeleted = 1 WHERE KpiId = @KPIId AND ModuleId = @ModuleId
	END	
	ELSE IF(@KpiType = 'MonthlyReport')
	BEGIN
		UPDATE MMonthlyReport SET IsDeleted = 1 WHERE KpiId = @KPIId
	    UPDATE MappingMonthlyReport SET ParentKpiId = NULL WHERE ParentKpiId = @KPIId
		UPDATE MappingMonthlyReport SET IsDeleted = 1 WHERE KpiId = @KPIId
	END	
END
GO
IF (NOT EXISTS (SELECT  [PageID] 

                 FROM [dbo].[M_PageDetails] 

				 where Name IN('Data Ingestion')				          

                ))

BEGIN

INSERT [dbo].[M_PageDetails] ( [Name], [AliasName], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES 

(N'Data Ingestion', N'Data Ingestion', NULL, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, NULL, NUll, 0)

END

GO

IF NOT EXISTS(SELECT * FROM M_SubPageDetails WHERE PageID in(

SELECT PageID FROM M_PageDetails  WHERE Name='Data Ingestion'))

BEGIN

  INSERT INTO M_SubPageDetails(Name,AliasName,PageID,isActive,isDeleted,CreatedOn,CreatedBy,SequenceNo,IsCustom,IsDynamicFieldSupported)

  SELECT 'Data Ingestion','Data Ingestion',(SELECT PageID FROM M_PageDetails  WHERE Name='Data Ingestion'),1,0,Getdate(),1,1,0,0
 
END

GO

IF NOT EXISTS(Select * from M_SubPageFields Where SubPageID in(

SELECT SubPageID FROM M_SubPageDetails WHERE PageID in(

SELECT PageID FROM M_PageDetails  WHERE Name='Data Ingestion') AND Name='Data Ingestion'))

BEGIN
 
INSERT INTO M_SubPageFields(Name,AliasName,SubPageID,isActive,isDeleted,CreatedBy,CreatedOn,IsCustom,SequenceNo,IsListData,ShowOnList)

SELECT 'Specific Kpi Extraction','Specific Kpi',(SELECT SubPageID FROM M_SubPageDetails WHERE PageID 

in(SELECT PageID FROM M_PageDetails  WHERE Name='Data Ingestion') AND Name='Data Ingestion'),1,0,1,getdate(),0,1,0,0
 
 
INSERT INTO M_SubPageFields(Name,AliasName,SubPageID,isActive,isDeleted,CreatedBy,CreatedOn,IsCustom,SequenceNo,IsListData,ShowOnList)

SELECT 'AsIs Extraction','AsIs Extraction',(SELECT SubPageID FROM M_SubPageDetails WHERE PageID 

in(SELECT PageID FROM M_PageDetails  WHERE Name='Data Ingestion') AND Name='Data Ingestion'),1,0,1,getdate(),0,2,0,0

END
GO
ALTER PROCEDURE  [dbo].[ProcCreateDuplicateKPI]
(
@KpiType VARCHAR(100),
@KpiId INT,
@UserId INT,
@ModuleId INT=NULL,
@Id INT OUTPUT
)
AS
BEGIN
SET NOCOUNT ON
		 BEGIN TRY
		 IF(@KpiType = 'Operational KPI')
			BEGIN
				INSERT INTO M_SectorwiseOperationalKPI(SectorID,KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym) SELECT SectorID,KPI,KpiInfo,@UserId,GETDATE(),0,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym FROM M_SectorwiseOperationalKPI WHERE SectorwiseOperationalKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		  IF(@KpiType = 'Company KPI')
			BEGIN
				INSERT INTO M_CompanyKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym FROM M_CompanyKPI WHERE CompanyKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		  IF(@KpiType = 'Impact KPI')
			BEGIN
				INSERT INTO M_ImpactKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,Description,ParentID,IsParent,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,Description,ParentID,IsParent,Formula,FormulaKPIId,Synonym FROM M_ImpactKPI WHERE ImpactKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		 IF(@KpiType = 'Investment KPI')
			BEGIN
				INSERT INTO M_InvestmentKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsNumeric,OrderBy,Description,Formula,FormulaKPIId,MethodologyId,IsBoldKPI,IsHeader,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,0,0,Description,Formula,FormulaKPIId,MethodologyId,IsBoldKPI,IsHeader,Synonym FROM M_InvestmentKPI WHERE InvestmentKPIId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		 IF(@KpiType = 'Balance Sheet KPI')
			BEGIN
				INSERT INTO M_BalanceSheet_LineItems(BalanceSheetLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,IsHeader,IsBoldKPI,MethodologyID,DisplayOrder,Description,IsCalculatedValue,Formula,FormulaKPIId,Synonym) SELECT BalanceSheetLineItem,KpiInfo,@UserId,GETDATE(),0,1,IsHeader,IsBoldKPI,MethodologyID,DisplayOrder,Description,IsCalculatedValue,Formula,FormulaKPIId,Synonym FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Cashflow KPI')
			BEGIN
				INSERT INTO M_CashFlow_LineItems(CashFlowLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,IsHeader,IsBoldKPI,MethodologyID,Description,DisplayOrder,IsCalculatedValue,Formula,FormulaKPIId,Synonym) SELECT CashFlowLineItem,KpiInfo,@UserId,GETDATE(),0,1,IsHeader,IsBoldKPI,MethodologyID,Description,DisplayOrder,IsCalculatedValue,Formula,FormulaKPIId,Synonym FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Profit And Loss KPI')
			BEGIN
				INSERT INTO M_ProfitAndLoss_LineItems(ProfitAndLossLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,MethodologyID,IsHeader,IsBoldKPI,Description,DisplayOrder,IsCalculatedValue,IsHighlighted,Formula,FormulaKPIId,Synonym) SELECT ProfitAndLossLineItem,KpiInfo,@UserId,GETDATE(),0,1,MethodologyID,IsHeader,IsBoldKPI,Description,DisplayOrder,IsCalculatedValue,IsHighlighted,Formula,FormulaKPIId,Synonym FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Trading Records')
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,1,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Credit KPI')
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,2,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId  = 16)
			BEGIN
				INSERT INTO MMonthlyReport(Kpi,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKpi,Description,Formula,FormulaKpiId) SELECT Kpi,KpiInfo,@UserId,GETUTCDATE(),0,IsHeader,IsBoldKpi,Description,Formula,FormulaKpiId FROM MMonthlyReport WHERE KpiId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45))
			BEGIN
				INSERT INTO MCapTable(Kpi,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKpi,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,KpiTypeId,Synonym) SELECT KPI,KpiInfo,@UserId,GETUTCDATE(),0,IsHeader,IsBoldKPI,@ModuleId,MethodologyID,Description,Formula,FormulaKPIId,KpiTypeId,Synonym FROM MCapTable WHERE KpiId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30))
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,@ModuleId,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
         END TRY
         BEGIN CATCH
             SET @Id = 0
         END CATCH
END
GO
CREATE OR ALTER PROCEDURE [dbo].[spBulkUploadFundFinancials]  
	 @TableName VARCHAR(100)
	,@UserID INT
	,@DocumentId INT = NULL
    ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
    ,@CommentId INT = NULL
	,@FundId INT
	,@IsIngestion BIT = 0
	,@ProcessId NVARCHAR(1000) = NULL
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @SQLString NVARCHAR(MAX);
	DECLARE @uploadType NVARCHAR(MAX)
	IF @IsIngestion = 1
		SET @uploadType = 'Ingestion'
	ELSE
		SET @uploadType = 'File Upload'
	DECLARE @KpiTable TABLE (
    [Id] [INT],
	[KpiId][int], 
	[Quarter] [nvarchar](10) NULL,
	[Year] [int] NOT NULL,
	[Month] [int] NULL,
	[KpiValue] NVARCHAR(MAX) NULL,
	[ValueTypeId] INT NULL,
	[ModuleId] [INT])
	SET @SQLString = 'SELECT Id,KpiId,Quarter,Year,Month,KpiValue,ValueTypeId,ModuleId FROM ' + @TableName + '';
	INSERT INTO @KpiTable
	EXEC sys.sp_executesql @SQLString;
	BEGIN TRY
	BEGIN TRANSACTION
	--Audit Log Entry for Update--
	---First entry for audit log OLD values for prod data
	INSERT INTO FundKpiAuditLog (ModuleId,FundId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy,V.DocumentId,V.SupportingDocumentsId,V.CommentId)
	SELECT DISTINCT KPI.ModuleId,@FundId,V.FundMasterKpiValueId,NULL,V.KPIValue,V.KPIInfo,M.KpiInfo,@uploadType,0,GETDATE(),@UserID,V.DocumentId,V.SupportingDocumentsId,V.CommentId 
	FROM @KpiTable KPI
	INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
	INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND MAP.KpiID = KPI.KpiId AND MAP.IsDeleted = 0  
	INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
	INNER JOIN FundMasterKpiValues V ON V.FundId = @FundId AND V.ModuleID = KPI.ModuleId  AND V.MappingId =MAP.MappingFundSectionKpiID 
		AND V.MappingId = Map.MappingFundSectionKpiID AND V.FundId = @FundId AND V.IsDeleted = 0
		AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = T.ValueTypeID
	LEFT JOIN FundKpiAuditLog A ON A.AttributeId = V.FundMasterKpiValueId AND A.ModuleId =KPI.ModuleId AND A.FundId = @FundId
	WHERE Map.FundId = @FundId  
	AND V.ModuleID = KPI.ModuleId
	AND V.MappingId IS NOT NULL 
	AND KPI.KpiValue IS NOT NULL AND A.AuditId IS NULL AND A.AttributeId IS NULL AND V.FundMasterKpiValueId IS NOT NULL 

	---insert audit log data before update for old values
	INSERT INTO FundKpiAuditLog (ModuleId,FundId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy,DocumentId,SupportingDocumentsId,CommentId)
	SELECT KPI.ModuleId,@FundId,V.FundMasterKpiValueId,V.KPIValue,KPI.KpiValue,V.KPIInfo,M.KpiInfo,@uploadType,0,GETDATE(),@UserID,@DocumentId,@SupportingDocumentsId,@CommentId 
	FROM @KpiTable KPI
	INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
	INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
INNER JOIN FundMasterKpiValues V ON V.FundId = Map.FundId AND V.MappingId = Map.MappingFundSectionKpiID AND V.IsDeleted = 0
		AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = T.ValueTypeID
	WHERE 
	Map.FundId = @FundId 
	AND V.ModuleID = KPI.ModuleId
	AND V.MappingId IS NOT NULL 
	AND KPI.KpiValue IS NOT NULL

	--Update existing data--
	UPDATE V
	SET V.KPIValue = KPI.KpiValue,V.DocumentId = @DocumentId,V.SupportingDocumentsId = @SupportingDocumentsId,V.CommentId =@CommentId, V.ProcessId = @ProcessId
	FROM @KpiTable KPI
	INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
	INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND Map.IsDeleted = 0
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
INNER JOIN FundMasterKpiValues V ON V.FundId = Map.FundId AND V.MappingId = Map.MappingFundSectionKpiID AND V.IsDeleted = 0
		AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = T.ValueTypeID
	WHERE 
	 Map.FundId = @FundId 
	AND V.ModuleID = KPI.ModuleId
	AND V.MappingId IS NOT NULL 
    AND KPI.KpiValue IS NOT NULL

	--Insert New Data---
	INSERT INTO FundMasterKpiValues(FundId, MappingId, KPIValue, KPIInfo, Month, Year, Quarter, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, ValueTypeID,[ModuleID],DocumentId,SupportingDocumentsId,CommentId,ProcessId)
	SELECT @FundId, Map.MappingFundSectionKpiID, KPI.KpiValue, M.KpiInfo, KPI.Month, KPI.Year, KPI.Quarter, GETDATE(), @UserID, NULL, NULL, 0, T.ValueTypeID,KPI.ModuleId,@DocumentId,@SupportingDocumentsId,@CommentId,@ProcessId
	from @KpiTable KPI
	INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
	INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND Map.IsDeleted = 0
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
LEFT JOIN FundMasterKpiValues V ON V.FundId = Map.FundId AND V.MappingId = Map.MappingFundSectionKpiID AND V.IsDeleted = 0
		AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = T.ValueTypeID
	WHERE Map.FundId = @FundId 
	AND V.MappingId IS NULL 
	AND KPI.KpiValue IS NOT NULL;

	---insert audit log data after insert
	INSERT INTO FundKpiAuditLog (ModuleId,FundId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy,V.DocumentId,V.SupportingDocumentsId,V.CommentId)
	SELECT DISTINCT KPI.ModuleId,@FundId,V.FundMasterKpiValueId,NULL,V.KPIValue,V.KPIInfo,M.KpiInfo,@uploadType,0,GETDATE(),@UserID,V.DocumentId,V.SupportingDocumentsId,V.CommentId 
	FROM @KpiTable KPI
	INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
	INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND MAP.KpiID = KPI.KpiId AND MAP.IsDeleted = 0  
	INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
	INNER JOIN FundMasterKpiValues V ON V.FundId = @FundId AND V.ModuleID = KPI.ModuleId  AND V.MappingId =MAP.MappingFundSectionKpiID 
		AND V.MappingId = Map.MappingFundSectionKpiID AND V.FundId = @FundId AND V.IsDeleted = 0
		AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = T.ValueTypeID
	LEFT JOIN FundKpiAuditLog A ON A.AttributeId = V.FundMasterKpiValueId AND A.ModuleId =KPI.ModuleId AND A.FundId = @FundId
	WHERE Map.FundId = @FundId  
	AND V.ModuleID = KPI.ModuleId
	AND V.MappingId IS NOT NULL 
	AND KPI.KpiValue IS NOT NULL AND A.AuditId IS NULL AND A.AttributeId IS NULL AND V.FundMasterKpiValueId IS NOT NULL 

	COMMIT TRANSACTION
	END TRY
	BEGIN CATCH

		DECLARE @Message varchar(MAX) = ERROR_MESSAGE(),
        @Number int = ERROR_NUMBER(),
        @State smallint = ERROR_STATE();
		
		THROW 50000, @Message, @State;


	ROLLBACK TRANSACTION
	END CATCH
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Cap Table 6')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (94, N'Cap Table 6', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'CapTable6')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Cap Table 7')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (95, N'Cap Table 7', 14, NULL, 1, 0,getdate(), 49, NULL,	NULL, NULL, 0, 0, N'CapTable7')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Cap Table 8')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], 
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (96, N'Cap Table 8', 14, NULL,1, 0, getdate(), 49, NULL, NULL, NULL, 0, 0, N'CapTable8')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Cap Table 9')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (97, N'Cap Table 9', 14, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'CapTable9')
END
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Cap Table 10')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted],
	[CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (98, N'Cap Table 10', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'CapTable10')
END
GO
-- Cap Table 6
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Cap Table 7
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Cap Table 8
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Cap Table 9
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Cap Table 10
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- othercap table

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 1')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (99, N'Other Cap Table 1', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'OtherCapTable1')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 2')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (100, N'Other Cap Table 2', 14, NULL, 1, 0,getdate(), 49, NULL,	NULL, NULL, 0, 0, N'OtherCapTable2')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 3')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], 
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (101, N'Other Cap Table 3', 14, NULL,1, 0, getdate(), 49, NULL, NULL, NULL, 0, 0, N'OtherCapTable3')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 4')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (102, N'Other Cap Table 4', 14, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'OtherCapTable4')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 5')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted],
	[CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (103, N'Other Cap Table 5', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'OtherCapTable5')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 6')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (104, N'Other Cap Table 6', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'OtherCapTable6')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 7')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (105, N'Other Cap Table 7', 14, NULL, 1, 0,getdate(), 49, NULL,	NULL, NULL, 0, 0, N'OtherCapTable7')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 8')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (106, N'Other Cap Table 8', 14, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'OtherCapTable8')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 9')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted],
	[CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (107, N'Other Cap Table 9', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'OtherCapTable9')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 10')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted],
	[CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (108, N'Other Cap Table 10', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'OtherCapTable10')
END
GO

-- Other Cap Table 1
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 2
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 3
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 4
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 5
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO


-- Other Cap Table 6
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO


-- Other Cap Table 7
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 8
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 9
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO


-- Other Cap Table 10
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_MasterAllKPIsByModuleIds]'))
BEGIN
	 DROP PROCEDURE sp_MasterAllKPIsByModuleIds
END
GO
CREATE PROCEDURE [dbo].[sp_MasterAllKPIsByModuleIds](
    @ModuleIds  varchar(max)=NULL)
AS
BEGIN

    DECLARE @ModuleIdsCTE TABLE (ModuleId INT);
    IF @ModuleIds IS NOT NULL
    BEGIN
        INSERT INTO @ModuleIdsCTE(ModuleId)
        SELECT Item FROM dbo.SplitString(@ModuleIds, ',');
    END
    ELSE
    BEGIN
        INSERT INTO @ModuleIdsCTE(ModuleId)
        SELECT ModuleID FROM M_KpiModules WHERE IsDeleted=0 AND Name NOT IN('StaticInformation','MonthlyReport');
		INSERT INTO @ModuleIdsCTE(ModuleId)
		SELECT SubPageID FROM M_SubPageDetails WHERE PageID IN(SELECT PageID FROM M_PageDetails WHERE Name='ESG')
    END

    -- Investment KPI
    SELECT 
        Mst.KPI,
        Mst.InvestmentKPIId KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        4 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'InvestmentKPIs') AliasName,
		(SELECT Top 1 Name FROM M_SubPageFields where name = 'InvestmentKPIs') Name
    FROM M_InvestmentKPI Mst
     LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 4)

    UNION ALL

    -- Master KPI
    SELECT 
        Mst.KPI,
        Mst.MasterKpiID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
		NULL ParentKpi,
        Mst.ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
	SPF.AliasName,
	SPF.Name
    FROM M_MasterKpis Mst
	INNER JOIN M_KpiModules kpi on kpi.ModuleID=Mst.ModuleID
	LEFT JOIN M_SubPageFields SPF ON SPF.name = kpi.PageConfigFieldName
    LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID	
    WHERE Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text') AND Mst.ModuleID IN(SELECT ModuleID FROM @ModuleIdsCTE)
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT Distinct ModuleID FROM M_KpiModules WHERE IsDeleted=0))

    UNION ALL

    -- Impact KPI
    SELECT 
        Mst.KPI,
        Mst.ImpactKPIID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        6 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'ImpactKPIs') AliasName,
		(SELECT Top 1 Name FROM M_SubPageFields where name = 'ImpactKPIs') Name
    FROM M_ImpactKPI Mst
     LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 6)

    UNION ALL

    -- Operational KPI
    SELECT 
        Mst.KPI,
        Mst.SectorwiseOperationalKPIID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        3 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'OperationalKPIs') AliasName,
		(SELECT Top 1 Name FROM M_SubPageFields where name = 'OperationalKPIs') Name
    FROM M_SectorwiseOperationalKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 3)

    UNION ALL

    -- Company KPI
    SELECT 
        Mst.KPI,
        Mst.CompanyKPIID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        5 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'CompanyKPIs') AliasName,
		(SELECT Top 1 Name FROM M_SubPageFields where name = 'CompanyKPIs') Name
    FROM M_CompanyKPI Mst
     LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 5)

    UNION ALL

    -- Profit and Loss
    SELECT 
        Mst.ProfitAndLossLineItem KPI,
        Mst.ProfitAndLossLineItemID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        7 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'ProfitLoss') AliasName,
		(SELECT Top 1 Name FROM M_SubPageFields where name = 'ProfitLoss') Name
    FROM M_ProfitAndLoss_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 7)

    UNION ALL

    -- Balance Sheet
    SELECT 
        Mst.BalanceSheetLineItem KPI,
        Mst.BalanceSheetLineItemID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        8 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'BalanceSheet') AliasName,
		(SELECT Top 1 Name FROM M_SubPageFields where name = 'BalanceSheet') Name
    FROM M_BalanceSheet_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 8)

    UNION ALL

    -- Cash Flow
    SELECT 
        Mst.CashFlowLineItem KPI,
        Mst.CashFlowLineItemID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        9 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'CashFlow') AliasName,
		(SELECT Top 1 Name FROM M_SubPageFields where name = 'CashFlow') Name
    FROM M_CashFlow_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 9)

		  UNION ALL
		-- cap table
		SELECT 
        Mst.KPI,
        Mst.KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
		NULL ParentKpi,
        Mst.ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		Msf.AliasName,
		Msf.Name
    FROM MCapTable Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
	INNER JOIN M_KpiModules Mk on Mk.ModuleID =Mst.ModuleId
	INNER JOIN M_SubPageFields Msf on Msf.Name=Mk.PageConfigFieldName
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text') AND Mst.ModuleID IN(SELECT ModuleID FROM @ModuleIdsCTE)
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT ModuleID FROM M_KpiModules WHERE Name IN ('CapTable1', 'CapTable2', 'CapTable3', 'CapTable4', 'CapTable5') AND IsDeleted=0))

		  UNION ALL
		  -- ESG
		  SELECT 
        Mst.KPI,
        Mst.ESGKpiId as KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
		NULL ParentKpi,
        Mst.SubPageId ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		Msb.AliasName,
		Msb.Name
    FROM M_ESGKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
	INNER JOIN M_SubPageDetails Msb on Msb.SubPageID=Mst.SubPageId
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text') AND Mst.SubPageId IN(SELECT ModuleId FROM @ModuleIdsCTE)
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT SubPageID FROM M_SubPageDetails WHERE PageID IN(
SELECT PageID FROM M_PageDetails WHERE Name='ESG')))
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_MappingAllKPIsByModuleIds]'))
BEGIN
	 DROP PROCEDURE sp_MappingAllKPIsByModuleIds
END
GO
CREATE PROCEDURE [dbo].[sp_MappingAllKPIsByModuleIds](
    @ModuleIds  varchar(max)=NULL,
	@CompanyIds varchar(max)=NULL )
AS
BEGIN
	DECLARE @CompanyDetails TABLE (PortfolioCompanyId INT,CompanyName varchar(max))	
	IF(@CompanyIds IS NULL OR @CompanyIds='')
	BEGIN
		INSERT INTO @CompanyDetails(CompanyName,PortfolioCompanyId)
		SELECT pcd.CompanyName, pcd.PortfolioCompanyID 
		FROM PortfolioCompanyDetails pcd
		WHERE    pcd.IsDeleted = 0;
	END
	ELSE 
	 BEGIN	
		INSERT INTO @CompanyDetails(CompanyName,PortfolioCompanyId)
		SELECT pcd.CompanyName, pcd.PortfolioCompanyID 
			FROM PortfolioCompanyDetails pcd INNER JOIN  dbo.SplitString(@CompanyIds, ',') ss 
			ON pcd.PortfolioCompanyID = ss.Item WHERE    pcd.IsDeleted = 0;
	 END

    DECLARE @ModuleIdsCTE TABLE (ModuleId INT);
    IF @ModuleIds IS NOT NULL
    BEGIN
        INSERT INTO @ModuleIdsCTE(ModuleId)
        SELECT Item FROM dbo.SplitString(@ModuleIds, ',');
    END
    ELSE
    BEGIN
        INSERT INTO @ModuleIdsCTE(ModuleId)
        SELECT ModuleID FROM M_KpiModules WHERE IsDeleted=0 AND Name NOT IN('StaticInformation','MonthlyReport');
		INSERT INTO @ModuleIdsCTE(ModuleId)
		SELECT SubPageID FROM M_SubPageDetails WHERE PageID IN(SELECT PageID FROM M_PageDetails WHERE Name='ESG')
    END
	
	-- Investment KPI
SELECT 
    Mst.KPI,
    Mst.InvestmentKPIId AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKPIID AS ParentId,
    Mst.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    4 AS ModuleId,
    CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_InvestmentKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_PortfolioInvestmentKPI Map ON Map.KpiID = Mst.InvestmentKPIId
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_InvestmentKPI ParentKpi ON ParentKpi.InvestmentKPIID = Map.ParentKPIID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'InvestmentKPIs') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 4)

UNION ALL

-- Master KPI
SELECT 
    Mst.KPI,
    Mst.MasterKpiID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKPIID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    Mst.ModuleId,
    CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    SPF.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_MasterKpis Mst
	INNER JOIN M_KpiModules Kpi on Kpi.ModuleID=Mst.ModuleID
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_Kpis Map ON Map.KpiID = Mst.MasterKpiID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_MasterKpis ParentKpi ON ParentKpi.MasterKpiID = Map.ParentKPIID AND ParentKpi.IsDeleted = 0 AND ParentKpi.ModuleID = Mst.ModuleID
    LEFT JOIN M_SubPageFields SPF ON SPF.name = kpi.PageConfigFieldName
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0 
    AND Mst.ModuleID IN (SELECT ModuleID FROM @ModuleIdsCTE)
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT Distinct ModuleID FROM M_KpiModules WHERE IsDeleted=0))

UNION ALL

-- Impact KPI
SELECT 
    Mst.KPI,
    Mst.ImpactKPIID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKPIID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    6 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.KPIOrder AS DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_ImpactKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_ImpactKPI_Order Map ON Map.ImpactKPIID = Mst.ImpactKPIID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_ImpactKPI ParentKpi ON ParentKpi.ImpactKPIID = Map.ParentKPIID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'ImpactKPIs') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 6)

UNION ALL

-- Operational KPI
SELECT 
    Mst.KPI,
    Mst.SectorwiseOperationalKPIID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKPIID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    3 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_SectorwiseOperationalKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_PortfolioOperationalKPI Map ON Map.KpiID = Mst.SectorwiseOperationalKPIID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_SectorwiseOperationalKPI ParentKpi ON ParentKpi.SectorwiseOperationalKPIID = Map.ParentKPIID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'OperationalKPIs') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 3)

UNION ALL

-- Company KPI
SELECT 
    Mst.KPI,
    Mst.CompanyKPIID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKPIID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    5 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_CompanyKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_PortfolioCompanyKPI Map ON Map.KpiID = Mst.CompanyKPIID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_CompanyKPI ParentKpi ON ParentKpi.CompanyKPIID = Map.ParentKPIID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'CompanyKPIs') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 5)

UNION ALL

-- Profit and Loss
SELECT 
    Mst.ProfitAndLossLineItem AS KPI,
    Mst.ProfitAndLossLineItemID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentLineItemID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.ProfitAndLossLineItem AS ParentKpi,
    7 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_ProfitAndLoss_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_CompanyProfitAndLossLineItems Map ON Map.ProfitAndLossLineItemID = Mst.ProfitAndLossLineItemID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_ProfitAndLoss_LineItems ParentKpi ON ParentKpi.ProfitAndLossLineItemID = Map.ParentLineItemID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'ProfitLoss') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 7)

UNION ALL

-- Balance Sheet
SELECT 
    Mst.BalanceSheetLineItem AS KPI,
    Mst.BalanceSheetLineItemID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentLineItemID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.BalanceSheetLineItem AS ParentKpi,
    8 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_BalanceSheet_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_CompanyBalanceSheetLineItems Map ON Map.BalanceSheetLineItemID = Mst.BalanceSheetLineItemID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_BalanceSheet_LineItems ParentKpi ON ParentKpi.BalanceSheetLineItemID = Map.ParentLineItemID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'BalanceSheet') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 8)

UNION ALL

-- Cash Flow
SELECT 
    Mst.CashFlowLineItem AS KPI,
    Mst.CashFlowLineItemID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentLineItemID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.CashFlowLineItem AS ParentKpi,
    9 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_CashFlow_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_CompanyCashFlowLineItems Map ON Map.CashFlowLineItemID = Mst.CashFlowLineItemID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_CashFlow_LineItems ParentKpi ON ParentKpi.CashFlowLineItemID = Map.ParentLineItemID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'CashFlow') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 9)

UNION ALL

-- Cap Table
SELECT 
    Mst.KPI,
    Mst.KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKpiId AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.Kpi AS ParentKpi,
    Mst.ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    Msf.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    MCapTable Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN MappingCapTable Map ON Map.KpiId = Mst.KpiId
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    INNER JOIN M_KpiModules Mk ON Mk.ModuleID = Mst.ModuleId
    INNER JOIN M_SubPageFields Msf ON Msf.Name = Mk.PageConfigFieldName
    LEFT JOIN MCapTable ParentKpi ON ParentKpi.KpiId = Map.ParentKpiId AND ParentKpi.IsDeleted = 0 AND ParentKpi.ModuleId = Map.ModuleId
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0 
    AND Mst.ModuleId IN (SELECT ModuleID FROM @ModuleIdsCTE)
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT ModuleID FROM M_KpiModules WHERE Name IN ('CapTable1', 'CapTable2', 'CapTable3', 'CapTable4', 'CapTable5') AND IsDeleted = 0))

UNION ALL

-- ESG
SELECT 
    Mst.KPI,
    Mst.ESGKpiId AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentId,
    Mlg.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    Mst.SubPageId AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    Msb.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_ESGKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN M_SubPageDetails Msb ON Msb.SubPageID = Mst.SubPageId
    INNER JOIN Mapping_ESGKpi Map ON Map.EsgKpiID = Mst.ESGKpiId
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_ESGKPI ParentKpi ON ParentKpi.ESGKpiID = Map.ParentId AND ParentKpi.IsDeleted = 0 AND ParentKpi.SubPageId = Map.SubPageId
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0 
    AND Mst.SubPageId IN (SELECT ModuleId FROM @ModuleIdsCTE)
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT SubPageID FROM M_SubPageDetails WHERE PageID IN (SELECT PageID FROM M_PageDetails WHERE Name = 'ESG')));

END
GO

IF EXISTS(select *from MSubSectionFields where SubPageID in (46,49) AND Options='Monthly,Quarterly,Annual')
	BEGIN
		UPDATE  MSubSectionFields SET Options='Monthly,Quarterly,Annual,Half-Annual',ChartValue='Monthly,Quarterly,Annual,Half-Annual' where SubPageID in (46,49)
	END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'M_SubPageFields')
    AND NOT EXISTS (
        SELECT * FROM sys.columns 
        WHERE object_id = OBJECT_ID('dbo.M_SubPageFields') 
        AND name = 'IsExtraction'
    )
BEGIN
    ALTER TABLE dbo.M_SubPageFields ADD IsExtraction BIT NOT NULL  CONSTRAINT DF_M_SubPageFields_IsExtraction DEFAULT 0;
END
GO
ALTER PROCEDURE [dbo].[ProcGetAuditLogs](
	@KpiId INT
	,@ModuleId INT
	,@AttributeId INT
	,@CompanyId INT
	,@ValueType VARCHAR(50)
	)
AS
BEGIN
	SET NOCOUNT ON;

	IF @ModuleId = 1
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'TradingRecords'
				) 'ModuleName'
			,MasterKpiID 'KpiId'
			,KPI 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_MasterKpis
		WHERE MasterKpiID = @KpiId
			AND ModuleID = @ModuleId

		IF EXISTS (
				SELECT TOP 1 *
				FROM MasterKpiAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			SELECT A.AuditId
				,AttributeId
				,OldValue
				,NewValue
				,AuditType 'Source'
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM MasterKpiAuditLog A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND ModuleId = @ModuleId
				AND AttributeId = @AttributeId
			ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId'
				,PCMasterKpiValueID 'AttributeId'
				,NULL 'OldValue'
				,KPIValue 'NewValue'
				,'Bulk Upload' Source
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,SupportingDocumentsId
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PCMasterKpiValues A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND ModuleId = @ModuleId
				AND PCMasterKpiValueID = @AttributeId
			ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId = 2
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'CreditKPI'
				) 'ModuleName'
			,MasterKpiID 'KpiId'
			,KPI 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_MasterKpis
		WHERE MasterKpiID = @KpiId
			AND ModuleID = @ModuleId

		IF EXISTS (
				SELECT TOP 1 *
				FROM MasterKpiAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			SELECT A.AuditId
				,AttributeId
				,OldValue
				,NewValue
				,AuditType 'Source'
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM MasterKpiAuditLog A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND ModuleId = @ModuleId
				AND AttributeId = @AttributeId
			ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId'
				,PCMasterKpiValueID 'AttributeId'
				,NULL 'OldValue'
				,KPIValue 'NewValue'
				,'Bulk Upload' Source
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,SupportingDocumentsId
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PCMasterKpiValues A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND ModuleId = @ModuleId
				AND PCMasterKpiValueID = @AttributeId
			ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId = 3
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'OperationalKPIs'
				) 'ModuleName'
			,SectorwiseOperationalKPIID 'KpiId'
			,KPI 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_SectorwiseOperationalKPI
		WHERE SectorwiseOperationalKPIID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM DataAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND AttributeName = 'Operational KPIs'
				)
		BEGIN
			SELECT A.AuditId
				,AttributeId
				,OldValue
				,NewValue
				,A.Description 'Source'
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM DataAuditLog A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND AttributeId = @AttributeId
				AND AttributeName = 'Operational KPIs'
			ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId'
				,SectorwiseOperationalKPIID 'AttributeId'
				,NULL 'OldValue'
				,KPIValue 'NewValue'
				,'Bulk Upload' Source
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,SupportingDocumentsId
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PortfolioCompanyOperationalKPIValues A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyOperationalKPIValueID = @AttributeId
			ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId = 4
	BEGIN
		SELECT (SELECT AliasName FROM M_SubPageFields WHERE Name='InvestmentKPIs')'ModuleName',InvestmentKPIId 'KpiId', KPI'Kpi',KpiInfo,(SELECT C.CurrencyCode FROM PortfolioCompanyDetails P INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
		WHERE P.PortfolioCompanyID =@CompanyId)AS CurrencyCode FROM M_InvestmentKPI WHERE InvestmentKPIId=@KpiId
	    IF EXISTS(SELECT TOP 1* FROM DataAuditLog WHERE  AttributeId = @AttributeId AND  PortfolioCompanyId = @CompanyId)
		BEGIN
			SELECT A.AuditId,AttributeId,OldValue,NewValue,Description'Source',D.DocumentId,D.DocumentName,SupportingDocumentsId,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension,CommentId,U.FirstName +' ' + U.LastName 'UploadedBy',FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn' FROM DataAuditLog A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE  PortfolioCompanyId =@CompanyId AND AttributeId = @AttributeId AND Comments = @ValueType ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0'AuditId',PCInvestmentKPIQuarterlyValueID 'AttributeId',NULL'OldValue',KPIActualValue'NewValue','Bulk Upload'Source,D.DocumentId,D.DocumentName,SupportingDocumentsId,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension,SupportingDocumentsId,CommentId,U.FirstName +' ' + U.LastName 'UploadedBy',FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt')'CreatedOn' FROM PCInvestmentKPIQuarterlyValue A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE  PortfolioCompanyId =@CompanyId AND PCInvestmentKPIQuarterlyValueID = @AttributeId ORDER BY AuditId DESC
		END 
	END
	ELSE IF @ModuleId = 5
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'CompanyKPIs'
				) 'ModuleName'
			,CompanyKPIID 'KpiId'
			,KPI 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_CompanyKPI
		WHERE CompanyKPIID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM DataAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND AttributeName = 'Company KPIs'
				)
		BEGIN
			IF @ValueType = 'Actual'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,Description AS 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM DataAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND AttributeName = 'Company KPIs'
					AND AttributeId = @AttributeId
					AND Comments = 'actual'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Budget'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,Description AS 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM DataAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND AttributeName = 'Company KPIs'
					AND AttributeId = @AttributeId
					AND Comments = 'budget'
				ORDER BY AuditId DESC
			END
			ELSE
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,Description AS 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM DataAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND AttributeName = 'Company KPIs'
					AND AttributeId = @AttributeId
					AND Comments = 'forecast'
				ORDER BY AuditId DESC
			END
		END
		ELSE
		BEGIN
			IF @ValueType = 'Actual'
			BEGIN
				SELECT 0 'AuditId'
					,PCCompanyKPIMonthlyValueID 'AttributeId'
					,NULL 'OldValue'
					,KPIActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM PCCompanyKPIMonthlyValue A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND PCCompanyKPIMonthlyValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Budget'
			BEGIN
				SELECT 0 'AuditId'
					,PCCompanyKPIMonthlyValueID 'AttributeId'
					,NULL 'OldValue'
					,KPIBudgetValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM PCCompanyKPIMonthlyValue A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND PCCompanyKPIMonthlyValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT 0 'AuditId'
					,CompanyKPIForecastValueId 'AttributeId'
					,NULL 'OldValue'
					,KPIValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM CompanyKPIForecastValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND CompanyKPIForecastValueId = @AttributeId
				ORDER BY AuditId DESC
			END
		END
	END
	ELSE IF @ModuleId = 6
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'ImpactKPIs'
				) 'ModuleName'
			,ImpactKPIID 'KpiId'
			,KPI 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_ImpactKPI
		WHERE ImpactKPIID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM ImpactKpiAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
				)
		BEGIN
			SELECT A.AuditId
				,AttributeId
				,OldValue
				,NewValue
				,A.AuditType 'Source'
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM ImpactKpiAuditLog A
			--LEFT JOIN  PCImpactKPIQuarterlyValue P ON P.
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND AttributeId = @AttributeId
			ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId'
				,ImpactKPIID 'AttributeId'
				,NULL 'OldValue'
				,KPIActualValue 'NewValue'
				,'Bulk Upload' Source
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,SupportingDocumentsId
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PCImpactKPIQuarterlyValue A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PCImpactKPIQuarterlyValueID = @AttributeId
			ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId = 7
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'ProfitLoss'
				) 'ModuleName'
			,ProfitAndLossLineItemID 'KpiId'
			,ProfitAndLossLineItem 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_ProfitAndLoss_LineItems
		WHERE ProfitAndLossLineItemID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM FinancialAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'budget'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'actual'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'forecast'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'ic'
				ORDER BY AuditId DESC
			END
			ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = @ValueType
				ORDER BY AuditId DESC
			END
		END
		ELSE
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT 0 'AuditId'
					,ProfitAndLossValueID 'AttributeId'
					,NULL 'OldValue'
					,BudgetValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM ProfitAndLossValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE ProfitAndLossValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT 0 'AuditId'
					,ProfitAndLossValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM ProfitAndLossValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE ProfitAndLossValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT 0 'AuditId'
					,ProfitAndLossForecastValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM ProfitAndLoss_ForecastData A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE ProfitAndLossForecastValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT 0 'AuditId'
					,ProfitAndLossICCaseValueID 'AttributeId'
					,NULL 'OldValue'
					,BaseValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM ProfitAndLossICCaseValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE ProfitAndLossICCaseValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				DECLARE @ValueTypeId INT;
				SET @ValueTypeID = (SELECT TOP 1 ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType)
				SELECT 0 'AuditId'
					,PCFinancialsValuesId 'AttributeId'
					,NULL 'OldValue'
					,KPIValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM PCFinancialsValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PCFinancialsValuesId = @AttributeId AND ValueTypeId = @ValueTypeID
				ORDER BY AuditId DESC
			END
		END
	END
	ELSE IF @ModuleId = 8
	BEGIN
		SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'BalanceSheet'
				) 'ModuleName'
			,BalanceSheetLineItemID 'KpiId'
			,BalanceSheetLineItem 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_BalanceSheet_LineItems
		WHERE BalanceSheetLineItemID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM FinancialAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'budget'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'actual'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'forecast'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'ic'
				ORDER BY AuditId DESC
			END
			ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = @ValueType
				ORDER BY AuditId DESC
			END
		END
		ELSE
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT 0 'AuditId'
					,BalanceSheetValueID 'AttributeId'
					,NULL 'OldValue'
					,BudgetValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM BalanceSheetValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE BalanceSheetValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT 0 'AuditId'
					,BalanceSheetValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM BalanceSheetValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE BalanceSheetValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT 0 'AuditId'
					,BalanceSheetForecastValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM BalanceSheet_ForecastData A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE BalanceSheetForecastValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT 0 'AuditId'
					,BalanceSheetICCaseValueID 'AttributeId'
					,NULL 'OldValue'
					,BaseValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM BalanceSheetICCaseValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE BalanceSheetICCaseValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			  ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				SET @ValueTypeID = (SELECT TOP 1 ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType)
				SELECT 0 'AuditId'
					,PCFinancialsValuesId 'AttributeId'
					,NULL 'OldValue'
					,KPIValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM PCFinancialsValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PCFinancialsValuesId = @AttributeId AND ValueTypeId = @ValueTypeID
				ORDER BY AuditId DESC
			END
		END
	END
	ELSE IF @ModuleId = 9
	BEGIN
	SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = 'CashFlow'
				) 'ModuleName'
			,CashFlowLineItemID 'KpiId'
			,CashFlowLineItem 'Kpi'
			,KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM M_CashFlow_LineItems
		WHERE CashFlowLineItemID = @KpiId

		IF EXISTS (
				SELECT TOP 1 *
				FROM FinancialAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'budget'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'actual'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'forecast'
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = 'ic'
				ORDER BY AuditId DESC
			END
			ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				SELECT A.AuditId
					,AttributeId
					,OldValue
					,NewValue
					,AuditType 'Source'
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM FinancialAuditLog A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
					AND AttributeId = @AttributeId
					AND ValueType = @ValueType
				ORDER BY AuditId DESC
			END
		END
		ELSE
		BEGIN
			IF @ValueType = 'Budget'
			BEGIN
				SELECT 0 'AuditId'
					,CashFlowValueID 'AttributeId'
					,NULL 'OldValue'
					,BudgetValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM CashFlowValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE CashFlowValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Actual'
			BEGIN
				SELECT 0 'AuditId'
					,CashFlowValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM CashFlowValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE CashFlowValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Forecast'
			BEGIN
				SELECT 0 'AuditId'
					,CashFlowForecastValueID 'AttributeId'
					,NULL 'OldValue'
					,ActualValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM CashFlow_ForecastData A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE CashFlowForecastValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF @ValueType = 'Ic'
			BEGIN
				SELECT 0 'AuditId'
					,CashFlowICCaseValueID 'AttributeId'
					,NULL 'OldValue'
					,BaseValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM CashFlowICCaseValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE CashFlowICCaseValueID = @AttributeId
				ORDER BY AuditId DESC
			END
			ELSE IF CHARINDEX('YTD', @ValueType) > 0 OR CHARINDEX('LTM', @ValueType) > 0
			BEGIN
				SET @ValueTypeID = (SELECT TOP 1 ValueTypeID FROM M_ValueTypes WHERE HeaderValue = @ValueType)
				SELECT 0 'AuditId'
					,PCFinancialsValuesId 'AttributeId'
					,NULL 'OldValue'
					,KPIValue 'NewValue'
					,'Bulk Upload' Source
					,D.DocumentId
					,D.DocumentName
					,SupportingDocumentsId
					,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
					,SupportingDocumentsId
					,CommentId
					,U.FirstName + ' ' + U.LastName 'UploadedBy'
					,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
				FROM PCFinancialsValues A
				LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
				LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
				WHERE PCFinancialsValuesId = @AttributeId AND ValueTypeId = @ValueTypeID
				ORDER BY AuditId DESC
			END
		END
	END
	ELSE IF @ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45)
	BEGIN
			SELECT (
				SELECT AliasName
				FROM M_SubPageFields
				WHERE NAME = MstM.Name
				) 'ModuleName'
			,Mst.KpiId 'KpiId'
			,Mst.KPI 'Kpi'
			,Mst.KpiInfo
			,(
				SELECT C.CurrencyCode
				FROM PortfolioCompanyDetails P
				INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID
				WHERE P.PortfolioCompanyID = @CompanyId
				) AS CurrencyCode
		FROM MCapTable Mst INNER JOIN M_KpiModules MstM on Mst.ModuleId=MstM.ModuleID
		WHERE Mst.KpiId = @KpiId
			AND Mst.ModuleID = @ModuleId

		IF EXISTS (
				SELECT TOP 1 *
				FROM CapTableAuditLog
				WHERE AttributeId = @AttributeId
					AND PortfolioCompanyId = @CompanyId
					AND ModuleId = @ModuleId
				)
		BEGIN
			SELECT A.AuditId
				,AttributeId
				,OldValue
				,NewValue
				,AuditType 'Source'
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM CapTableAuditLog A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND A.ModuleId = @ModuleId
				AND A.AttributeId = @AttributeId
			ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId'
				,A.PcCapTableValueId 'AttributeId'
				,NULL 'OldValue'
				,KPIValue 'NewValue'
				,'Bulk Upload' Source
				,D.DocumentId
				,D.DocumentName
				,SupportingDocumentsId
				,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension
				,SupportingDocumentsId
				,CommentId
				,U.FirstName + ' ' + U.LastName 'UploadedBy'
				,FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PcCapTableValues A
			LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID
			LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId
				AND A.ModuleId = @ModuleId
				AND A.PcCapTableValueId = @AttributeId
			ORDER BY AuditId DESC
		END
	END
	ELSE IF @ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30)
	BEGIN
		SELECT (SELECT AliasName FROM M_SubPageFields WHERE NAME = MstM.Name) 'ModuleName',MasterKpiID 'KpiId',KPI 'Kpi',KpiInfo,(
				SELECT C.CurrencyCode FROM PortfolioCompanyDetails P INNER JOIN M_Currency C ON P.ReportingCurrencyID = C.CurrencyID WHERE P.PortfolioCompanyID = @CompanyId) AS CurrencyCode
		FROM M_MasterKpis Mst INNER JOIN M_KpiModules MstM on Mst.ModuleId=MstM.ModuleID
		WHERE Mst.MasterKpiID = @KpiId AND Mst.ModuleID = @ModuleId

		IF EXISTS (SELECT TOP 1 * FROM MasterKpiAuditLog WHERE AttributeId = @AttributeId AND PortfolioCompanyId = @CompanyId AND ModuleId = @ModuleId)
		BEGIN
			SELECT A.AuditId,AttributeId,OldValue,NewValue,AuditType 'Source',D.DocumentId,D.DocumentName,SupportingDocumentsId,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension,CommentId,U.FirstName + ' ' + U.LastName 'UploadedBy',FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM MasterKpiAuditLog A LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id
			WHERE PortfolioCompanyId = @CompanyId AND ModuleId = @ModuleId AND AttributeId = @AttributeId ORDER BY AuditId DESC
		END
		ELSE
		BEGIN
			SELECT 0 'AuditId',PCMasterKpiValueID 'AttributeId',NULL 'OldValue',KPIValue 'NewValue','Bulk Upload' Source,D.DocumentId,D.DocumentName,SupportingDocumentsId
			,RIGHT(D.DocumentId, CHARINDEX('.', REVERSE(D.DocumentId)) - 1) AS Extension,SupportingDocumentsId,CommentId,U.FirstName + ' ' + U.LastName 'UploadedBy',FORMAT(A.CreatedOn, 'dd-MMM-yyyy hh:mm:ss tt') 'CreatedOn'
			FROM PCMasterKpiValues A LEFT JOIN UserDetails U ON A.CreatedBy = U.UserID LEFT JOIN DocumentsInformation D ON A.DocumentId = D.Id ORDER BY AuditId DESC
		END
	END
	SET NOCOUNT OFF;
END
GO
-- Update SubPage names if they exist
IF EXISTS (SELECT 1 FROM M_SubPageDetails WHERE SubPageID=49 and Name!='Fund Key Performance Indicator')
BEGIN
    UPDATE M_SubPageDetails SET Name='Fund Key Performance Indicator' WHERE SubPageID=49
END

IF EXISTS (SELECT 1 FROM M_SubPageDetails WHERE SubPageID=46 and Name!='Fund Financials')
BEGIN
    UPDATE M_SubPageDetails SET Name='Fund Financials' WHERE SubPageID=46
END

-- Update SubPage fields if they exist
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials')
BEGIN
    UPDATE M_SubPageFields SET Name='FundFinancials8' WHERE SubPageID=46 AND Name='FundFinancials'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI1' WHERE SubPageID=49 AND Name='FundKeyFinancials1'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI2' WHERE SubPageID=49 AND Name='FundKeyFinancials2'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI3' WHERE SubPageID=49 AND Name='FundKeyFinancials3'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI4' WHERE SubPageID=49 AND Name='FundKeyFinancials4'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI5' WHERE SubPageID=49 AND Name='FundKeyFinancials5'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI6' WHERE SubPageID=49 AND Name='FundKeyFinancials6'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI7' WHERE SubPageID=49 AND Name='FundKeyFinancials7'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI8' WHERE SubPageID=49 AND Name='FundKeyFinancials8'
END

-- Update MFundKpiModules if they exist
IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundFinancials')
BEGIN
    UPDATE MFundKpiModules SET Name='FundFinancials8', PageConfigFieldName='FundFinancials8' WHERE Name='FundFinancials'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials1')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI1', PageConfigFieldName='FundKPI1' WHERE Name='FundKeyFinancials1'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials2')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI2', PageConfigFieldName='FundKPI2' WHERE Name='FundKeyFinancials2'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials3')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI3', PageConfigFieldName='FundKPI3' WHERE Name='FundKeyFinancials3'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials4')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI4', PageConfigFieldName='FundKPI4' WHERE Name='FundKeyFinancials4'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials5')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI5', PageConfigFieldName='FundKPI5' WHERE Name='FundKeyFinancials5'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials6')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI6', PageConfigFieldName='FundKPI6' WHERE Name='FundKeyFinancials6'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials7')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI7', PageConfigFieldName='FundKPI7' WHERE Name='FundKeyFinancials7'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials8')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI8', PageConfigFieldName='FundKPI8' WHERE Name='FundKeyFinancials8'
END
GO
------------------------BPF-18977 SQL changes------------------------
IF OBJECT_ID('[dbo].[ProcGetCapTablesKpis]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcGetCapTablesKpis]
END
GO
CREATE PROCEDURE [dbo].[ProcGetCapTablesKpis]
    @CompanyIds NVARCHAR(MAX),
	@ModuleId INT ,
    @KpiTypeId INT 
AS
BEGIN
    SET NOCOUNT ON;

	Declare @KpiType Varchar(5000)=NULL;
						SET @KpiType=(SELECT Top 1 AliasName FROM M_SubPageFields where name in(SELECT PageConfigFieldName FROM M_KpiModules WHERE ModuleID=@ModuleId));


	DECLARE @CompanyDetails TABLE (PortfolioCompanyId INT)
				INSERT INTO @CompanyDetails(PortfolioCompanyId)
				SELECT Item FROM dbo.SplitString(@CompanyIds,',')
				DROP TABLE IF EXISTS #ChildKpiTable
				CREATE TABLE #ChildKpiTable(KpiId INT,KpiDisplayName NVARCHAR(3000),Kpi NVARCHAR(3000),ModuleId INT,ModuleName NVARCHAR(1000),ParentKpiId INT)
				
				Declare @KpisAliasTable Table(Name Varchar(max),AliasName  Varchar(max))
				INSERT INTO @KpisAliasTable(Name,AliasName)
				SELECT Name,AliasName FROM M_SubPageFields WHERE SubPageID IN(SELECT SubPageID FROM M_SubPageDetails WHERE Name IN('Key Performance Indicator','Company Financials','CapTable','Other KPIs'))


    -- Temporary table to hold mapping KPIs
    DECLARE @C8MappingKpis TABLE (KpiID INT, ParentKPIID INT);

    -- Insert relevant KPI mappings
    INSERT INTO @C8MappingKpis (KpiID, ParentKPIID)
    SELECT DISTINCT Map.KpiId, ISNULL(Map.ParentKpiId, 0)
    FROM MappingCapTable Map
    INNER JOIN @CompanyDetails Com 
        ON Com.PortfolioCompanyId = Map.PortfolioCompanyID
    WHERE Map.IsDeleted = 0 AND Map.ModuleId = @ModuleId;

    -- CTE to get KPI names
    WITH C2Kpis AS (
        SELECT KpiId, Kpi
        FROM MCapTable
        WHERE IsDeleted = 0 AND ModuleId = @ModuleId AND KpiTypeId = @KpiTypeId
    )

    -- Insert into final KPI table
    INSERT INTO #ChildKpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId)
    SELECT DISTINCT 
        M.KpiId AS KpiId,
        M.Kpi AS KpiDisplayName,
        CASE  
            WHEN Map.ParentKpiId > 0 
                THEN ISNULL((SELECT Kpi FROM C2Kpis WHERE KpiId = Map.ParentKpiId) + '-', '') + M.Kpi
            ELSE M.Kpi  
        END AS Kpi,
        @ModuleId AS ModuleId,
        @KpiType AS ModuleName,
        Map.ParentKPIID
    FROM @C8MappingKpis Map
    INNER JOIN C2Kpis M ON Map.KpiId = M.KpiId;

	select * from  #ChildKpiTable
	drop table  #ChildKpiTable
END
GO
IF OBJECT_ID('[dbo].[ProcGetAnalyticsKpis]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcGetAnalyticsKpis]
END
GO
CREATE PROCEDURE [dbo].[ProcGetAnalyticsKpis] 
(
@CompanyIds NVARCHAR(MAX),
@ModuleIds NVARCHAR(100)
)
AS
BEGIN
SET NOCOUNT ON;
				DECLARE @CompanyDetails TABLE (PortfolioCompanyId INT)
				INSERT INTO @CompanyDetails(PortfolioCompanyId)
				SELECT Item FROM dbo.SplitString(@CompanyIds,',')

				DECLARE @ModuleIdTable TABLE (ModuleId INT)
				INSERT INTO @ModuleIdTable(ModuleId)
				SELECT Item FROM dbo.SplitString(@ModuleIds,',')
				DROP TABLE IF EXISTS #KpiTable
				CREATE TABLE #KpiTable(KpiId INT,KpiDisplayName NVARCHAR(3000),Kpi NVARCHAR(3000),ModuleId INT,ModuleName NVARCHAR(1000),ParentKpiId INT)
				
				Declare @KpisAliasTable Table(Name Varchar(max),AliasName  Varchar(max))
				INSERT INTO @KpisAliasTable(Name,AliasName)
				SELECT Name,AliasName FROM M_SubPageFields WHERE SubPageID IN(SELECT SubPageID FROM M_SubPageDetails WHERE Name IN('Key Performance Indicator','Company Financials','CapTable','Other KPIs'))
			  IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 1)
					BEGIN
					    DECLARE @TradingMappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @TradingMappingKpis(KpiID,ParentKPIID)
						SELECT DISTINCT Map.KpiID,ISNULL(Map.ParentKPIID,0) FROM Mapping_Kpis Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.ModuleID=1 AND Map.IsDeleted=0;
						
						WITH MasterKpis AS (
						SELECT MasterKpiID, KPI
						FROM M_MasterKpis
						WHERE IsDeleted = 0 AND ModuleID=1)

						INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
						SELECT DISTINCT  M.MasterKpiID AS KpiId, M.KPI,CASE 
						WHEN Map.ParentKPIID > 0 THEN  ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
						ELSE M.KPI 
						END AS Kpi,
						1 AS ModuleId,
						(SELECT AliasName FROM @KpisAliasTable WHERE Name='TradingRecords') AS ModuleName, Map.ParentKPIID
						FROM @TradingMappingKpis Map INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
					END
			  IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 2)
					BEGIN
						 DECLARE @CreditMappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @CreditMappingKpis(KpiID,ParentKPIID)
						SELECT  DISTINCT Map.KpiID,ISNULL(Map.ParentKPIID,0) FROM Mapping_Kpis Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.ModuleID=2 AND Map.IsDeleted=0;

						WITH MasterKpis AS (
						SELECT MasterKpiID, KPI
						FROM M_MasterKpis
						WHERE IsDeleted = 0 AND ModuleID=2)

						INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
						SELECT DISTINCT  M.MasterKpiID AS KpiId, M.KPI,CASE 
						WHEN Map.ParentKPIID > 0 THEN  ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
						ELSE M.KPI 
						END AS Kpi,
						2 AS ModuleId,
						(SELECT AliasName FROM @KpisAliasTable WHERE Name='CreditKPI') AS ModuleName, Map.ParentKPIID
						FROM @CreditMappingKpis Map INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
					END
			IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 17)
					BEGIN
						 DECLARE @CustomTable1MappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @CustomTable1MappingKpis(KpiID,ParentKPIID)
						SELECT  DISTINCT Map.KpiID,ISNULL(Map.ParentKPIID,0) FROM Mapping_Kpis Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.ModuleID=17 AND Map.IsDeleted=0;

						WITH MasterKpis AS (
						SELECT MasterKpiID, KPI
						FROM M_MasterKpis
						WHERE IsDeleted = 0 AND ModuleID=17)

						INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
						SELECT DISTINCT  M.MasterKpiID AS KpiId, M.KPI,CASE 
						WHEN Map.ParentKPIID > 0 THEN  ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
						ELSE M.KPI 
						END AS Kpi,
						17 AS ModuleId,
						(SELECT AliasName FROM @KpisAliasTable WHERE Name='CustomTable1') AS ModuleName, Map.ParentKPIID
						FROM @CustomTable1MappingKpis Map INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
					END
			IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 18)
					BEGIN
						 DECLARE @CustomTable2MappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @CustomTable2MappingKpis(KpiID,ParentKPIID)
						SELECT  DISTINCT Map.KpiID,ISNULL(Map.ParentKPIID,0) FROM Mapping_Kpis Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.ModuleID=18 AND Map.IsDeleted=0;

						WITH MasterKpis AS (
						SELECT MasterKpiID, KPI
						FROM M_MasterKpis
						WHERE IsDeleted = 0 AND ModuleID=18)

						INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
						SELECT DISTINCT  M.MasterKpiID AS KpiId, M.KPI,CASE 
						WHEN Map.ParentKPIID > 0 THEN  ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
						ELSE M.KPI 
						END AS Kpi,
						18 AS ModuleId,
						(SELECT AliasName FROM @KpisAliasTable WHERE Name='CustomTable2') AS ModuleName, Map.ParentKPIID
						FROM @CustomTable2MappingKpis Map INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
					END
			IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 19)
					BEGIN
						 DECLARE @CustomTable3MappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @CustomTable3MappingKpis(KpiID,ParentKPIID)
						SELECT  DISTINCT Map.KpiID,ISNULL(Map.ParentKPIID,0) FROM Mapping_Kpis Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.ModuleID=19 AND Map.IsDeleted=0;

						WITH MasterKpis AS (
						SELECT MasterKpiID, KPI
						FROM M_MasterKpis
						WHERE IsDeleted = 0 AND ModuleID=19)

						INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
						SELECT DISTINCT  M.MasterKpiID AS KpiId, M.KPI,CASE 
						WHEN Map.ParentKPIID > 0 THEN  ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
						ELSE M.KPI 
						END AS Kpi,
						19 AS ModuleId,
						(SELECT AliasName FROM @KpisAliasTable WHERE Name='CustomTable3') AS ModuleName, Map.ParentKPIID
						FROM @CustomTable3MappingKpis Map INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
					END
			IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 20)
					BEGIN
						 DECLARE @CustomTable4MappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @CustomTable4MappingKpis(KpiID,ParentKPIID)
						SELECT  DISTINCT Map.KpiID,ISNULL(Map.ParentKPIID,0) FROM Mapping_Kpis Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.ModuleID=20 AND Map.IsDeleted=0;

						WITH MasterKpis AS (
						SELECT MasterKpiID, KPI
						FROM M_MasterKpis
						WHERE IsDeleted = 0 AND ModuleID=20)

						INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
						SELECT DISTINCT  M.MasterKpiID AS KpiId, M.KPI,CASE 
						WHEN Map.ParentKPIID > 0 THEN  ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
						ELSE M.KPI 
						END AS Kpi,
						20 AS ModuleId,
						(SELECT AliasName FROM @KpisAliasTable WHERE Name='CustomTable4') AS ModuleName, Map.ParentKPIID
						FROM @CustomTable4MappingKpis Map INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
					END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 21)
		BEGIN
			DECLARE @OtherKPI1MappingKpis TABLE (KpiID INT, ParentKPIID INT)
			INSERT INTO @OtherKPI1MappingKpis(KpiID, ParentKPIID)
			SELECT DISTINCT Map.KpiID, ISNULL(Map.ParentKPIID, 0) 
			FROM Mapping_Kpis Map 
			INNER JOIN @CompanyDetails Com 
				ON Com.PortfolioCompanyId = Map.PortfolioCompanyID 
				AND Map.ModuleID = 21 
				AND Map.IsDeleted = 0;

			WITH MasterKpis AS (
				SELECT MasterKpiID, KPI
				FROM M_MasterKpis
				WHERE IsDeleted = 0 AND ModuleID = 21
			)
			INSERT INTO #KpiTable(KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId) 
			SELECT DISTINCT M.MasterKpiID AS KpiId, M.KPI,
				CASE 
					WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI 
				END AS Kpi,
				21 AS ModuleId,
				(SELECT AliasName FROM @KpisAliasTable WHERE Name = 'OtherKPI1') AS ModuleName, 
				Map.ParentKPIID
			FROM @OtherKPI1MappingKpis Map 
			INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
		END

		IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 22)
		BEGIN
			DECLARE @OtherKPI2MappingKpis TABLE (KpiID INT, ParentKPIID INT)
			INSERT INTO @OtherKPI2MappingKpis(KpiID, ParentKPIID)
			SELECT DISTINCT Map.KpiID, ISNULL(Map.ParentKPIID, 0) 
			FROM Mapping_Kpis Map 
			INNER JOIN @CompanyDetails Com 
				ON Com.PortfolioCompanyId = Map.PortfolioCompanyID 
				AND Map.ModuleID = 22 
				AND Map.IsDeleted = 0;

			WITH MasterKpis AS (
				SELECT MasterKpiID, KPI
				FROM M_MasterKpis
				WHERE IsDeleted = 0 AND ModuleID = 22
			)

			INSERT INTO #KpiTable(KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId) 
			SELECT DISTINCT M.MasterKpiID AS KpiId, M.KPI,
				CASE 
					WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI 
				END AS Kpi,
				22 AS ModuleId,
				(SELECT AliasName FROM @KpisAliasTable WHERE Name = 'OtherKPI2') AS ModuleName, 
				Map.ParentKPIID
			FROM @OtherKPI2MappingKpis Map 
			INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
		END

		IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 23)
		BEGIN
			DECLARE @OtherKPI3MappingKpis TABLE (KpiID INT, ParentKPIID INT)
			INSERT INTO @OtherKPI3MappingKpis(KpiID, ParentKPIID)
			SELECT DISTINCT Map.KpiID, ISNULL(Map.ParentKPIID, 0) 
			FROM Mapping_Kpis Map 
			INNER JOIN @CompanyDetails Com 
				ON Com.PortfolioCompanyId = Map.PortfolioCompanyID 
				AND Map.ModuleID = 23 
				AND Map.IsDeleted = 0;

			WITH MasterKpis AS (
				SELECT MasterKpiID, KPI
				FROM M_MasterKpis
				WHERE IsDeleted = 0 AND ModuleID = 23
			)

			INSERT INTO #KpiTable(KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId) 
			SELECT DISTINCT M.MasterKpiID AS KpiId, M.KPI,
				CASE 
					WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI 
				END AS Kpi,
				23 AS ModuleId,
				(SELECT AliasName FROM @KpisAliasTable WHERE Name = 'OtherKPI3') AS ModuleName, 
				Map.ParentKPIID
			FROM @OtherKPI3MappingKpis Map 
			INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
		END

		IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 24)
		BEGIN
			DECLARE @OtherKPI4MappingKpis TABLE (KpiID INT, ParentKPIID INT)
			INSERT INTO @OtherKPI4MappingKpis(KpiID, ParentKPIID)
			SELECT DISTINCT Map.KpiID, ISNULL(Map.ParentKPIID, 0) 
			FROM Mapping_Kpis Map 
			INNER JOIN @CompanyDetails Com 
				ON Com.PortfolioCompanyId = Map.PortfolioCompanyID 
				AND Map.ModuleID = 24 
				AND Map.IsDeleted = 0;

			WITH MasterKpis AS (
				SELECT MasterKpiID, KPI
				FROM M_MasterKpis
				WHERE IsDeleted = 0 AND ModuleID = 24
			)

			INSERT INTO #KpiTable(KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId) 
			SELECT DISTINCT M.MasterKpiID AS KpiId, M.KPI,
				CASE 
					WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI 
				END AS Kpi,
				24 AS ModuleId,
				(SELECT AliasName FROM @KpisAliasTable WHERE Name = 'OtherKPI4') AS ModuleName, 
				Map.ParentKPIID
			FROM @OtherKPI4MappingKpis Map 
			INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
		END

		IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 25)
		BEGIN
			DECLARE @OtherKPI5MappingKpis TABLE (KpiID INT, ParentKPIID INT)
			INSERT INTO @OtherKPI5MappingKpis(KpiID, ParentKPIID)
			SELECT DISTINCT Map.KpiID, ISNULL(Map.ParentKPIID, 0) 
			FROM Mapping_Kpis Map 
			INNER JOIN @CompanyDetails Com 
				ON Com.PortfolioCompanyId = Map.PortfolioCompanyID 
				AND Map.ModuleID = 25 
				AND Map.IsDeleted = 0;

			WITH MasterKpis AS (
				SELECT MasterKpiID, KPI
				FROM M_MasterKpis
				WHERE IsDeleted = 0 AND ModuleID = 25
			)

			INSERT INTO #KpiTable(KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId) 
			SELECT DISTINCT M.MasterKpiID AS KpiId, M.KPI,
				CASE 
					WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI 
				END AS Kpi,
				25 AS ModuleId,
				(SELECT AliasName FROM @KpisAliasTable WHERE Name = 'OtherKPI5') AS ModuleName, 
				Map.ParentKPIID
			FROM @OtherKPI5MappingKpis Map 
			INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
		END

		IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 26)
		BEGIN
			DECLARE @OtherKPI6MappingKpis TABLE (KpiID INT, ParentKPIID INT)
			INSERT INTO @OtherKPI6MappingKpis(KpiID, ParentKPIID)
			SELECT DISTINCT Map.KpiID, ISNULL(Map.ParentKPIID, 0) 
			FROM Mapping_Kpis Map 
			INNER JOIN @CompanyDetails Com 
				ON Com.PortfolioCompanyId = Map.PortfolioCompanyID 
				AND Map.ModuleID = 26 
				AND Map.IsDeleted = 0;

			WITH MasterKpis AS (
				SELECT MasterKpiID, KPI
				FROM M_MasterKpis
				WHERE IsDeleted = 0 AND ModuleID = 26
			)

			INSERT INTO #KpiTable(KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId) 
			SELECT DISTINCT M.MasterKpiID AS KpiId, M.KPI,
				CASE 
					WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI 
				END AS Kpi,
				26 AS ModuleId,
				(SELECT AliasName FROM @KpisAliasTable WHERE Name = 'OtherKPI6') AS ModuleName, 
				Map.ParentKPIID
			FROM @OtherKPI6MappingKpis Map 
			INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
		END

		IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 27)
		BEGIN
			DECLARE @OtherKPI7MappingKpis TABLE (KpiID INT, ParentKPIID INT)
			INSERT INTO @OtherKPI7MappingKpis(KpiID, ParentKPIID)
			SELECT DISTINCT Map.KpiID, ISNULL(Map.ParentKPIID, 0) 
			FROM Mapping_Kpis Map 
			INNER JOIN @CompanyDetails Com 
				ON Com.PortfolioCompanyId = Map.PortfolioCompanyID 
				AND Map.ModuleID = 27 
				AND Map.IsDeleted = 0;

			WITH MasterKpis AS (
				SELECT MasterKpiID, KPI
				FROM M_MasterKpis
				WHERE IsDeleted = 0 AND ModuleID = 27
			)

			INSERT INTO #KpiTable(KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId) 
			SELECT DISTINCT M.MasterKpiID AS KpiId, M.KPI,
				CASE 
					WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI 
				END AS Kpi,
				27 AS ModuleId,
				(SELECT AliasName FROM @KpisAliasTable WHERE Name = 'OtherKPI7') AS ModuleName, 
				Map.ParentKPIID
			FROM @OtherKPI7MappingKpis Map 
			INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
		END

		IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 28)
		BEGIN
			DECLARE @OtherKPI8MappingKpis TABLE (KpiID INT, ParentKPIID INT)
			INSERT INTO @OtherKPI8MappingKpis(KpiID, ParentKPIID)
			SELECT DISTINCT Map.KpiID, ISNULL(Map.ParentKPIID, 0) 
			FROM Mapping_Kpis Map 
			INNER JOIN @CompanyDetails Com 
				ON Com.PortfolioCompanyId = Map.PortfolioCompanyID 
				AND Map.ModuleID = 28 
				AND Map.IsDeleted = 0;

			WITH MasterKpis AS (
				SELECT MasterKpiID, KPI
				FROM M_MasterKpis
				WHERE IsDeleted = 0 AND ModuleID = 28
			)

			INSERT INTO #KpiTable(KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId) 
			SELECT DISTINCT M.MasterKpiID AS KpiId, M.KPI,
				CASE 
					WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI 
				END AS Kpi,
				28 AS ModuleId,
				(SELECT AliasName FROM @KpisAliasTable WHERE Name = 'OtherKPI8') AS ModuleName, 
				Map.ParentKPIID
			FROM @OtherKPI8MappingKpis Map 
			INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
		END

		IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 29)
		BEGIN
			DECLARE @OtherKPI9MappingKpis TABLE (KpiID INT, ParentKPIID INT)
			INSERT INTO @OtherKPI9MappingKpis(KpiID, ParentKPIID)
			SELECT DISTINCT Map.KpiID, ISNULL(Map.ParentKPIID, 0) 
			FROM Mapping_Kpis Map 
			INNER JOIN @CompanyDetails Com 
				ON Com.PortfolioCompanyId = Map.PortfolioCompanyID 
				AND Map.ModuleID = 29 
				AND Map.IsDeleted = 0;

			WITH MasterKpis AS (
				SELECT MasterKpiID, KPI
				FROM M_MasterKpis
				WHERE IsDeleted = 0 AND ModuleID = 29
			)

			INSERT INTO #KpiTable(KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId) 
			SELECT DISTINCT M.MasterKpiID AS KpiId, M.KPI,
				CASE 
					WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI 
				END AS Kpi,
				29 AS ModuleId,
				(SELECT AliasName FROM @KpisAliasTable WHERE Name = 'OtherKPI9') AS ModuleName, 
				Map.ParentKPIID
			FROM @OtherKPI9MappingKpis Map 
			INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
		END

		IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 30)
		BEGIN
			DECLARE @OtherKPI10MappingKpis TABLE (KpiID INT, ParentKPIID INT)
			INSERT INTO @OtherKPI10MappingKpis(KpiID, ParentKPIID)
			SELECT DISTINCT Map.KpiID, ISNULL(Map.ParentKPIID, 0) 
			FROM Mapping_Kpis Map 
			INNER JOIN @CompanyDetails Com 
				ON Com.PortfolioCompanyId = Map.PortfolioCompanyID 
				AND Map.ModuleID = 30 
				AND Map.IsDeleted = 0;

			WITH MasterKpis AS (
				SELECT MasterKpiID, KPI
				FROM M_MasterKpis
				WHERE IsDeleted = 0 AND ModuleID = 30
			)

			INSERT INTO #KpiTable(KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId) 
			SELECT DISTINCT M.MasterKpiID AS KpiId, M.KPI,
				CASE 
					WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI 
				END AS Kpi,
				30 AS ModuleId,
				(SELECT AliasName FROM @KpisAliasTable WHERE Name = 'OtherKPI10') AS ModuleName, 
				Map.ParentKPIID
			FROM @OtherKPI10MappingKpis Map 
			INNER JOIN MasterKpis M ON Map.KpiID = M.MasterKpiID
		END
			 IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 3)
					BEGIN

					    DECLARE @OperationalMappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @OperationalMappingKpis(KpiID,ParentKPIID)
						SELECT DISTINCT Map.KpiID,ISNULL(Map.ParentKPIID,0) FROM Mapping_PortfolioOperationalKPI Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0;

						WITH OperationalKpis AS (
						SELECT SectorwiseOperationalKPIID, KPI
						FROM M_SectorwiseOperationalKPI
						WHERE IsDeleted = 0)

						INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
						SELECT DISTINCT  M.SectorwiseOperationalKPIID AS KpiId, M.KPI,
						 CASE WHEN Map.ParentKPIID > 0 THEN  ISNULL((SELECT KPI FROM OperationalKpis WHERE SectorwiseOperationalKPIID = Map.ParentKPIID) + '-', '') + M.KPI
						        ELSE  M.KPI 
						END AS Kpi,
						3 AS ModuleId, (SELECT AliasName FROM @KpisAliasTable WHERE Name='OperationalKPIs') AS ModuleName, Map.ParentKPIID 
						FROM   Mapping_PortfolioOperationalKPI Map
						INNER JOIN   OperationalKpis M ON Map.KpiID = M.SectorwiseOperationalKPIID
							INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID
						WHERE  Map.IsDeleted = 0

					END
			 IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 4)
					BEGIN
					 
					    DECLARE @InvestmentMappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @InvestmentMappingKpis(KpiID,ParentKPIID)
						SELECT DISTINCT Map.KpiID,ISNULL(Map.ParentKPIID,0) FROM Mapping_PortfolioInvestmentKPI Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0;

						WITH InvestmentKpis AS (
						SELECT InvestmentKPIId, KPI
						FROM M_InvestmentKPI
						WHERE IsDeleted = 0)

						   INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId)	
						   SELECT DISTINCT  M.InvestmentKPIId AS KpiId, M.KPI,
						   CASE   WHEN Map.ParentKPIID > 0 THEN 
						               ISNULL((SELECT KPI FROM InvestmentKpis WHERE InvestmentKPIID = Map.ParentKPIID) + '-', '') + M.KPI
						        ELSE  M.KPI 
						   END AS Kpi,
						   4 AS ModuleId,
						   (SELECT AliasName FROM @KpisAliasTable WHERE Name='InvestmentKPIs') AS ModuleName, Map.ParentKPIID 
						   FROM   @InvestmentMappingKpis Map
						   INNER JOIN  InvestmentKpis M ON Map.KpiID = M.InvestmentKPIId
					END
			 IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 5)
					BEGIN
						 DECLARE @CompanyMappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @CompanyMappingKpis(KpiID,ParentKPIID)
						SELECT DISTINCT Map.KpiID,ISNULL(Map.ParentKPIID,0) FROM Mapping_PortfolioCompanyKPI Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0;

						WITH CompanyKpis AS (
						SELECT CompanyKPIID, KPI
						FROM M_CompanyKPI
						WHERE IsDeleted = 0)						
						INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
						SELECT DISTINCT M.CompanyKPIID AS KpiId, M.KPI,
						    CASE  WHEN Map.ParentKPIID > 0 THEN 
						            ISNULL((SELECT KPI FROM CompanyKpis WHERE CompanyKPIID = Map.ParentKPIID) + '-', '') + M.KPI
						   ELSE M.KPI 
						    END AS Kpi,
						    5 AS ModuleId,
						    (SELECT AliasName FROM @KpisAliasTable WHERE Name='CompanyKPIs') AS ModuleName, Map.ParentKPIID
						FROM   @CompanyMappingKpis Map
						INNER JOIN   CompanyKpis M ON Map.KpiID = M.CompanyKPIID
					END
			IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 6)
					BEGIN
					 DECLARE @ImpactMappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @ImpactMappingKpis(KpiID,ParentKPIID)
						SELECT DISTINCT Map.ImpactKPIID,ISNULL(Map.ParentKPIID,0) FROM Mapping_ImpactKPI_Order Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0;

					WITH ImpactKpis AS (
						SELECT ImpactKPIID, KPI
						FROM M_ImpactKPI
						WHERE IsDeleted = 0)

					INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId)
					SELECT DISTINCT M.ImpactKPIID AS KpiId, M.KPI, CASE WHEN Map.ParentKPIID > 0 THEN ISNULL((SELECT KPI FROM ImpactKpis WHERE ImpactKPIID = Map.ParentKPIID) + '-', '') + M.KPI
					ELSE M.KPI  END AS Kpi, 6 AS ModuleId,(SELECT AliasName FROM @KpisAliasTable WHERE Name='ImpactKPIs') AS ModuleName, Map.ParentKPIID 
					FROM  @ImpactMappingKpis Map
					INNER JOIN ImpactKpis M ON Map.KpiID = M.ImpactKPIID
					END
			 IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 7)
					BEGIN
					    DECLARE @PLMappingKpis TABLE (ProfitAndLossLineItemID INT,ParentLineItemID INT)
						INSERT INTO @PLMappingKpis(ProfitAndLossLineItemID,ParentLineItemID)
						SELECT DISTINCT Map.ProfitAndLossLineItemID,ISNULL(Map.ParentLineItemID,0) FROM Mapping_CompanyProfitAndLossLineItems Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0;

						WITH PLKpis AS (
						SELECT ProfitAndLossLineItemID, ProfitAndLossLineItem
						FROM M_ProfitAndLoss_LineItems
						WHERE IsDeleted = 0)

						INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
						SELECT DISTINCT M.ProfitAndLossLineItemID AS KpiId, M.ProfitAndLossLineItem, CASE  WHEN Map.ParentLineItemID > 0 THEN  ISNULL((SELECT ProfitAndLossLineItem FROM PLKpis WHERE ProfitAndLossLineItemID = Map.ParentLineItemID) + '-', '') + M.ProfitAndLossLineItem
						ELSE  M.ProfitAndLossLineItem  END AS Kpi, 7 AS ModuleId, (SELECT AliasName FROM @KpisAliasTable WHERE Name='ProfitLoss') AS ModuleName, Map.ParentLineItemID 
						FROM  @PLMappingKpis Map
						INNER JOIN  PLKpis M ON Map.ProfitAndLossLineItemID = M.ProfitAndLossLineItemID
					END
			IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 8)
					BEGIN

					 DECLARE @BSMappingKpis TABLE (BalanceSheetLineItemID INT,ParentLineItemID INT)
						INSERT INTO @BSMappingKpis(BalanceSheetLineItemID,ParentLineItemID)
						SELECT DISTINCT Map.BalanceSheetLineItemID,ISNULL(Map.ParentLineItemID,0) FROM Mapping_CompanyBalanceSheetLineItems Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0;

					WITH BSKpis AS (
						SELECT BalanceSheetLineItemID, BalanceSheetLineItem
						FROM M_BalanceSheet_LineItems
						WHERE IsDeleted = 0)

				INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId)						
				SELECT DISTINCT  M.BalanceSheetLineItemID AS KpiId, M.BalanceSheetLineItem,  CASE WHEN Map.ParentLineItemID > 0 THEN   ISNULL((SELECT BalanceSheetLineItem FROM BSKpis WHERE BalanceSheetLineItemID = Map.ParentLineItemID) + '-', '') + M.BalanceSheetLineItem
			    ELSE   M.BalanceSheetLineItem  END AS Kpi,8 AS ModuleId, (SELECT AliasName FROM @KpisAliasTable WHERE Name='BalanceSheet') AS ModuleName, Map.ParentLineItemID 
				FROM  @BSMappingKpis Map INNER JOIN  BSKpis M ON Map.BalanceSheetLineItemID = M.BalanceSheetLineItemID

					END
			IF EXISTS (SELECT *FROM @ModuleIdTable WHERE ModuleId = 9)
					BEGIN

					 DECLARE @CFMappingKpis TABLE (CashFlowLineItemID INT,ParentLineItemID INT)
						INSERT INTO @CFMappingKpis(CashFlowLineItemID,ParentLineItemID)
						SELECT DISTINCT Map.CashFlowLineItemID,ISNULL(Map.ParentLineItemID,0) FROM Mapping_CompanyCashFlowLineItems Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0;


					WITH CFKpis AS (
						SELECT CashFlowLineItemID, CashFlowLineItem
						FROM M_CashFlow_LineItems
						WHERE IsDeleted = 0)
			INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
			SELECT DISTINCT  M.CashFlowLineItemID AS KpiId, M.CashFlowLineItem, CASE  WHEN Map.ParentLineItemID > 0 THEN  ISNULL((SELECT CashFlowLineItem FROM CFKpis WHERE CashFlowLineItemID = Map.ParentLineItemID) + '-', '') + M.CashFlowLineItem
			ELSE  M.CashFlowLineItem  END AS Kpi,9 AS ModuleId, (SELECT AliasName FROM @KpisAliasTable WHERE Name='CashFlow') AS ModuleName, Map.ParentLineItemID 
			FROM  @CFMappingKpis Map
			INNER JOIN CFKpis M ON Map.CashFlowLineItemID = M.CashFlowLineItemID

					END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 11)
					BEGIN
					 DECLARE @C1MappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @C1MappingKpis(KpiID,ParentKPIID)
						SELECT DISTINCT Map.KpiId,ISNULL(Map.ParentKpiId,0) FROM MappingCapTable Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0 AND Map.ModuleId = 11;
					WITH C1Kpis AS (
						SELECT KpiId, Kpi
						FROM MCapTable
						WHERE IsDeleted = 0 AND ModuleId=11 AND KpiTypeId=1)
			INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
			SELECT DISTINCT  M.KpiId AS KpiId, M.KPI, CASE  WHEN Map.ParentKpiId > 0 THEN  ISNULL((SELECT Kpi FROM C1Kpis WHERE KpiId = Map.ParentKpiId) + '-', '') + M.Kpi
			ELSE  M.Kpi  END AS Kpi,11 AS ModuleId, (SELECT AliasName FROM @KpisAliasTable WHERE Name='CapTable1') AS ModuleName, Map.ParentKPIID 
			FROM  @C1MappingKpis Map
			INNER JOIN C1Kpis M ON Map.KpiId = M.KpiId
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 12)
					BEGIN
					 DECLARE @C2MappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @C2MappingKpis(KpiID,ParentKPIID)
						SELECT DISTINCT Map.KpiId,ISNULL(Map.ParentKpiId,0) FROM MappingCapTable Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0 AND Map.ModuleId = 12;
					WITH C2Kpis AS (
						SELECT KpiId, Kpi
						FROM MCapTable
						WHERE IsDeleted = 0 AND ModuleId=12 AND KpiTypeId=1)
			INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
			SELECT DISTINCT  M.KpiId AS KpiId, M.KPI, CASE  WHEN Map.ParentKpiId > 0 THEN  ISNULL((SELECT Kpi FROM C2Kpis WHERE KpiId = Map.ParentKpiId) + '-', '') + M.Kpi
			ELSE  M.Kpi  END AS Kpi,12 AS ModuleId, (SELECT AliasName FROM @KpisAliasTable WHERE Name='CapTable2') AS ModuleName, Map.ParentKPIID 
			FROM  @C2MappingKpis Map
			INNER JOIN C2Kpis M ON Map.KpiId = M.KpiId
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 13)
					BEGIN
					 DECLARE @C3MappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @C3MappingKpis(KpiID,ParentKPIID)
						SELECT DISTINCT Map.KpiId,ISNULL(Map.ParentKpiId,0) FROM MappingCapTable Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0 AND Map.ModuleId = 13;
					WITH C3Kpis AS (
						SELECT KpiId, Kpi
						FROM MCapTable
						WHERE IsDeleted = 0 AND ModuleId=13 AND KpiTypeId=1)
			INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
			SELECT DISTINCT  M.KpiId AS KpiId, M.KPI, CASE  WHEN Map.ParentKpiId > 0 THEN  ISNULL((SELECT Kpi FROM C3Kpis WHERE KpiId = Map.ParentKpiId) + '-', '') + M.Kpi
			ELSE  M.Kpi  END AS Kpi,13 AS ModuleId, (SELECT AliasName FROM @KpisAliasTable WHERE Name='CapTable3') AS ModuleName, Map.ParentKPIID 
			FROM  @C3MappingKpis Map
			INNER JOIN C3Kpis M ON Map.KpiId = M.KpiId
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 14)
					BEGIN
					 DECLARE @C4MappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @C4MappingKpis(KpiID,ParentKPIID)
						SELECT DISTINCT Map.KpiId,ISNULL(Map.ParentKpiId,0) FROM MappingCapTable Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0 AND Map.ModuleId = 14;
					WITH C4Kpis AS (
						SELECT KpiId, Kpi
						FROM MCapTable
						WHERE IsDeleted = 0 AND ModuleId=14 AND KpiTypeId=1)
			INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
			SELECT DISTINCT  M.KpiId AS KpiId, M.KPI, CASE  WHEN Map.ParentKpiId > 0 THEN  ISNULL((SELECT Kpi FROM C4Kpis WHERE KpiId = Map.ParentKpiId) + '-', '') + M.Kpi
			ELSE  M.Kpi  END AS Kpi,14 AS ModuleId, (SELECT AliasName FROM @KpisAliasTable WHERE Name='CapTable4') AS ModuleName, Map.ParentKPIID 
			FROM  @C4MappingKpis Map
			INNER JOIN C4Kpis M ON Map.KpiId = M.KpiId
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 15)
					BEGIN
					 DECLARE @C5MappingKpis TABLE (KpiID INT,ParentKPIID INT)
						INSERT INTO @C5MappingKpis(KpiID,ParentKPIID)
						SELECT DISTINCT Map.KpiId,ISNULL(Map.ParentKpiId,0) FROM MappingCapTable Map 
						INNER JOIN @CompanyDetails Com on Com.PortfolioCompanyId=Map.PortfolioCompanyID AND Map.IsDeleted=0 AND Map.ModuleId = 15;
					WITH C5Kpis AS (
						SELECT KpiId, Kpi
						FROM MCapTable
						WHERE IsDeleted = 0 AND ModuleId=15 AND KpiTypeId=1)
			INSERT INTO  #KpiTable(KpiId,KpiDisplayName,Kpi,ModuleId,ModuleName,ParentKpiId) 
			SELECT DISTINCT  M.KpiId AS KpiId, M.KPI, CASE  WHEN Map.ParentKpiId > 0 THEN  ISNULL((SELECT Kpi FROM C5Kpis WHERE KpiId = Map.ParentKpiId) + '-', '') + M.Kpi
			ELSE  M.Kpi  END AS Kpi,15 AS ModuleId, (SELECT AliasName FROM @KpisAliasTable WHERE Name='CapTable5') AS ModuleName, Map.ParentKPIID 
			FROM  @C5MappingKpis Map
			INNER JOIN C5Kpis M ON Map.KpiId = M.KpiId
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 31)
					BEGIN
			INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId)
			EXEC ProcGetCapTablesKpis @CompanyIds, 31, 1;

			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 32)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,32,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 33)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,33,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 34)
					BEGIN
					INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					EXEC ProcGetCapTablesKpis @CompanyIds,34,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 35)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,35,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 36)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,36,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 37)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,37,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 38)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,38,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 39)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,39,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 40)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,40,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 41)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,41,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 42)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,42,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 43)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,43,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 44)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,44,1
			END
			IF EXISTS (SELECT * FROM @ModuleIdTable WHERE ModuleId = 45)
					BEGIN
					 INSERT INTO #KpiTable (KpiId, KpiDisplayName, Kpi, ModuleId, ModuleName, ParentKpiId ) 
					 EXEC ProcGetCapTablesKpis @CompanyIds,45,1
			END

	CREATE NONCLUSTERED INDEX IX_KpiTable_KpiId ON #KpiTable (ModuleId,KpiId);
	SELECT * FROM #KpiTable ORDER BY ModuleId
	DROP TABLE IF EXISTS #KpiTable;
END
GO
IF OBJECT_ID('[dbo].[View_GetAnalyticsCapTable]', 'V') IS NOT NULL
BEGIN
    DROP VIEW  [dbo].[View_GetAnalyticsCapTable]
END
GO
CREATE VIEW [dbo].[View_GetAnalyticsCapTable]
As
Select Map.KpiID KpiId,A.KpiValue KpiValue,A.Year,
CASE 
	WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  Cast(A.Year as varchar)
	WHEN (Month IS NULL or Month = 0) THEN A.Quarter+' '+Cast(A.Year as varchar)
	WHEN Quarter IS NULL THEN  [dbo].[GetMonthName](A.Month)+' '+Cast(A.Year as varchar)
	END
	Period,
CASE 
	WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  CAST(Cast(A.Year as varchar) +'-12-31' AS VARCHAR)
	WHEN (Month IS NULL or Month = 0) THEN  CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter,2,1),A.Year)AS VARCHAR)
	WHEN Quarter IS NULL THEN  CAST(dbo.GetLastDateOfMonth(A.Month,A.Year)AS VARCHAR)
	END
	PeriodDate,
CASE 
	WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  'Yearly'
	WHEN (Month IS NULL or Month = 0) THEN 'Quarterly'
	WHEN Quarter IS NULL THEN  'Monthly'
	END
	PeriodType,
CASE
	WHEN (SELECT TOP 1 KpiID FROM MappingCapTable  WHERE EXISTS (SELECT DISTINCT * FROM MCapTable b inner join MappingCapTable m ON m.KpiID = b.KpiId WHERE b.KPI = mst.KPI and m.KpiID!=mst.KpiId and mst.ModuleId = 11) and PortfolioCompanyID=Map.PortfolioCompanyID and map.ModuleId = 11) is not null and Map.ParentKPIID > 0 THEN (SELECT KPI FROM MCapTable WHERE KpiId = Map.ParentKPIID AND ModuleId = 11) + '-' + mst.KPI
	ELSE mst.KPI
END
Kpi,
--(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'CapTable1') 'KpiType',
A.PortfolioCompanyID PortfolioCompanyId,Month,Quarter,
mst.KpiInfo KpiInfo,v.HeaderValue ValueType,mst.MethodologyID, Map.ParentKPIID 'ParentId',
A.ModuleId
FROM MappingCapTable Map 
INNER JOIN PcCapTableValues A on
Map.PortfolioCompanyID=A.PortfolioCompanyID AND Map.MappingId=A.MappingId 
INNER JOIN MCapTable mst on mst.KpiId=Map.KpiID 
INNER JOIN M_ValueTypes V on a.ValueTypeID = v.ValueTypeID
WHERE A.IsDeleted=0 and Map.IsDeleted=0 and mst.IsDeleted=0  ;
--and mst.ModuleId = 11 and map.ModuleId = 11 and A.ModuleId = 11;
GO
IF OBJECT_ID('[dbo].[sp_AnalyticsCapTableValues]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[sp_AnalyticsCapTableValues]
END
GO
CREATE Procedure sp_AnalyticsCapTableValues
@CurrencyRateType VARCHAR(50)=NULL,
@ToCurrencyId INT=0,
@Fx BIT = 0,
@FromYear INT=0,
@ToYear INT=0,
@CompanyIds Varchar(Max),
@KpiId Varchar(Max),
@ModuleId INT
AS 
BEGIN
DECLARE @CompanyDetails TABLE (PortfolioCompanyId INT)
INSERT INTO @CompanyDetails(PortfolioCompanyId)
SELECT Item FROM dbo.SplitString(@CompanyIds,',')

DECLARE @KpiIds TABLE (KpiId INT)
INSERT INTO @KpiIds(KpiId)
SELECT Item FROM dbo.SplitString(@KpiId,',')

Declare @KpiType Varchar(5000)=NULL;
SET @KpiType=(SELECT Top 1 AliasName FROM M_SubPageFields where name in(SELECT PageConfigFieldName FROM M_KpiModules WHERE ModuleID=@ModuleId));

Select 
CASE
	WHEN (SELECT TOP 1 KpiID FROM MappingCapTable WHERE EXISTS (SELECT DISTINCT * FROM MCapTable b inner join MappingCapTable m ON m.KpiID = b.KpiId WHERE b.KPI = mst.KPI and m.KpiID!=mst.KpiId and m.ModuleId = @ModuleId) and PortfolioCompanyID=Map.PortfolioCompanyID and Map.ModuleId = @ModuleId) is not null and Map.ParentKPIID > 0 THEN (SELECT KPI FROM MCapTable WHERE KpiId = Map.ParentKPIID and ModuleId = @ModuleId) + '-' + mst.KPI
	ELSE mst.KPI
END
Kpi,
Map.KpiID KpiId,mst.KpiInfo KpiInfo,@KpiType 'KpiType',
(CASE WHEN @Fx=1 AND Mst.KpiInfo='$' AND IsNumeric(KpiValue) = 1 AND @ToCurrencyId > 0 AND P.FromCurrencyId <> @ToCurrencyId THEN CAST((SELECT dbo.CalculateFxConversion(P.FinancialYearEnd,A.Year,A.Month,A.Quarter,@CurrencyRateType,'$',mst.MethodologyId,
				CASE 
			WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  'Yearly'
			WHEN (Month IS NULL or Month = 0) THEN 'Quarterly'
			WHEN Quarter IS NULL THEN  'Monthly'
			END,@ToCurrencyId,P.FromCurrencyId,A.KpiValue)) as nvarchar(Max))
			ELSE KpiValue   END) KpiValue,
CASE 
	WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  Cast(A.Year as varchar)
	WHEN (Month IS NULL or Month = 0) THEN A.Quarter+' '+Cast(A.Year as varchar)
	WHEN Quarter IS NULL THEN  [dbo].[GetMonthName](A.Month)+' '+Cast(A.Year as varchar)
	END
	Period,
CASE 
	WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  CAST(Cast(A.Year as varchar) +'-15-31' AS VARCHAR)
	WHEN (Month IS NULL or Month = 0) THEN  CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter,2,1),A.Year)AS VARCHAR)
	WHEN Quarter IS NULL THEN  CAST(dbo.GetLastDateOfMonth(A.Month,A.Year)AS VARCHAR)
	END
	PeriodDate,
CASE 
	WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  'Yearly'
	WHEN (Month IS NULL or Month = 0) THEN 'Quarterly'
	WHEN Quarter IS NULL THEN  'Monthly'
	END
	PeriodType,Quarter,Month,Year, Map.ParentKPIID 'ParentId',A.PortfolioCompanyID PortfolioCompanyId,Map.DisplayOrder,
	v.HeaderValue ValueType,mst.MethodologyID'MethodologyId',
	p.FinancialYearEnd,
P.FromCurrencyId,
V.HeaderValue
FROM MappingCapTable Map 
INNER JOIN PcCapTableValues A on Map.PortfolioCompanyID=A.PortfolioCompanyID AND Map.MappingId=A.MappingId  AND Map.PortfolioCompanyID IN (SELECT PortfolioCompanyId from @CompanyDetails) AND Map.KpiID IN (SELECT KpiId FROM @KpiIds) AND A.PortfolioCompanyID IN (SELECT PortfolioCompanyId from @CompanyDetails)
INNER JOIN MCapTable mst on mst.KpiId=Map.KpiID AND  Map.KpiID IN (SELECT KpiId FROM @KpiIds)
INNER JOIN M_ValueTypes V on a.ValueTypeID = v.ValueTypeID
LEFT JOIN  (
SELECT TOP 1 
    P.CompanyName,
    C.CurrencyCode,
    P.PortfolioCompanyID,
    P.ReportingCurrencyID,
    F.CurrencyID FromCurrencyId,
    dbo.GetFinancialYearEnd(P.FinancialYearEnd) AS FinancialYearEnd
FROM 
    PortfolioCompanyDetails P
LEFT JOIN 
    DealDetails D ON P.PortfolioCompanyID = D.PortfolioCompanyID
LEFT JOIN 
    FundDetails F ON D.FundID = F.FundID
LEFT JOIN  
    M_Currency C ON F.CurrencyID = C.CurrencyID
WHERE 
    P.IsDeleted = 0 
    AND P.PortfolioCompanyID IN (SELECT PortfolioCompanyId FROM @CompanyDetails)
ORDER BY 
    P.PortfolioCompanyID
) P ON A.PortfolioCompanyID = P.PortfolioCompanyID
WHERE A.IsDeleted=0 and Map.IsDeleted=0 and mst.IsDeleted=0  AND Map.KpiID IN (SELECT KpiId FROM @KpiIds) AND mst.KpiId IN (SELECT KpiId FROM @KpiIds) AND
A.PortfolioCompanyID IN (SELECT PortfolioCompanyId from @CompanyDetails) AND A.KpiValue IS NOT NULL AND A.Year BETWEEN @FromYear AND @ToYear and mst.ModuleId = @ModuleId and map.ModuleId = @ModuleId and A.ModuleId = @ModuleId
END
GO
IF OBJECT_ID('[dbo].[GetAnalyticsAllKpiValues]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[GetAnalyticsAllKpiValues]
END
GO
CREATE PROCEDURE [dbo].[GetAnalyticsAllKpiValues] 
(
		@CompanyIds nvarchar(MAX),
        @ModuleId nvarchar(200),
        @KpiIds NVARCHAR(MAX), 
        @FromYear nvarchar(200),
		@ToYear nvarchar(200),
		@SubPageIds NVARCHAR(MAX),
		@FieldIds NVARCHAR(MAX),
		@ToCurrencyId INT = NULL,
		@CurrencyRateType NVARCHAR(50)
)
AS 
BEGIN
SET NOCOUNT OFF;
DECLARE @finalColumns NVARCHAR(MAX),@columnsDealStaticWithAliases NVARCHAR(MAX),@columnsPcWithAliases NVARCHAR(MAX),@columnsFundWithAliases NVARCHAR(MAX),@columnsCustom NVARCHAR(MAX),@pivotQuery NVARCHAR(MAX),@moduleQuery NVARCHAR(1000),@moduleIdQuery NVARCHAR(500);
	DROP TABLE IF EXISTS #TEMPCOMP
	SELECT *INTO #TEMPCOMP  FROM dbo.SplitString(@CompanyIds,',')
	DROP TABLE IF EXISTS #TempKpi
	SELECT *INTO #TempKpi  FROM dbo.SplitString(@KpiIds,',')
	DROP TABLE IF EXISTS #TEMPFields
	SELECT *INTO #TEMPFields  FROM dbo.SplitString(@FieldIds,',')
	DROP TABLE IF EXISTS #TEMPSubPageIds
	SELECT *INTO #TEMPSubPageIds  FROM dbo.SplitString(@SubPageIds,',')

	DROP TABLE IF EXISTS #TempFinancialFx
	CREATE TABLE #TempFinancialFx (
		UpdateId INT IDENTITY(1,1) PRIMARY KEY,
		Kpi NVARCHAR(3000),
		KpiId INT,
		KpiInfo NVARCHAR(100),
		KpiType NVARCHAR(500),
		KpiValue NVARCHAR(MAX),
		Period NVARCHAR(100),
		PeriodDate NVARCHAR(100),
		PeriodType NVARCHAR(100),
		Quarter NVARCHAR(100),
		Month INT,
		Year INT,
		ParentId INT,
		PortfolioCompanyId INT,
		DisplayOrder INT,
		ValueType NVARCHAR(100),
		MethodologyId INT,
		FinancialYearEnd INT,
		FromCurrencyId INT,
		HeaderValue NVARCHAR(100)
	);
	IF NOT EXISTS(SELECT ITEM FROM  #TEMPSubPageIds WHERE Item = 1)
		BEGIN
			INSERT INTO #TEMPSubPageIds VALUES(1)
		END
	IF NOT EXISTS(SELECT ITEM FROM  #TEMPSubPageIds WHERE Item = 7)
		BEGIN
			INSERT INTO #TEMPSubPageIds VALUES(7)
		END
	IF NOT EXISTS(SELECT ITEM FROM  #TEMPFields WHERE Item = (SELECT FieldID FROM M_SubPageFields WHERE Name='CompanyName' AND SubPageID = 1))
		BEGIN
			INSERT INTO #TEMPFields  SELECT FieldID FROM M_SubPageFields WHERE Name='CompanyName' AND SubPageID = 1
		END
	IF NOT EXISTS(SELECT ITEM FROM  #TEMPFields WHERE Item = (SELECT FieldID FROM M_SubPageFields WHERE Name='FundName' AND SubPageID = 7))
		BEGIN
			INSERT INTO #TEMPFields SELECT FieldID FROM M_SubPageFields WHERE Name='FundName' AND SubPageID =7
		END

	 SELECT @columnsDealStaticWithAliases = STRING_AGG(Name + ' AS ' + QUOTENAME(AliasName), ', ')
		FROM M_SubPageFields
		WHERE SubPageID = 5 AND FieldID IN (SELECT ITEM FROM #TEMPFields) AND IsCustom = 0 AND isDeleted = 0 AND isActive=1 AND NAME NOT IN ('CompanyName','FundName');

	SELECT @columnsPcWithAliases = STRING_AGG(Name + ' AS ' + QUOTENAME(AliasName), ', ')
		FROM M_SubPageFields
		WHERE SubPageID IN (1,10,11) AND IsCustom = 0 AND FieldID IN (SELECT ITEM FROM #TEMPFields) AND isDeleted = 0 AND Name NOT IN ('DealId','FundId','CompanyLogo') AND isActive=1;

	SELECT @columnsFundWithAliases = STRING_AGG(Name + ' AS ' + QUOTENAME(AliasName), ', ')
		FROM M_SubPageFields
		WHERE SubPageID in (7,8,12) AND FieldID IN (SELECT ITEM FROM #TEMPFields) AND IsCustom = 0 AND isDeleted = 0 AND isActive=1;

	SELECT   @columnsCustom = STRING_AGG(QUOTENAME(AliasName), ', ')
	FROM M_SubPageFields
	WHERE SubPageID IN (1,5,7,8,10,11,12) AND FieldID IN (SELECT ITEM FROM #TEMPFields) AND isActive = 1 AND isDeleted = 0 AND IsCustom = 1  AND isActive=1;
		IF(@ModuleId IN (1,2,17,18,19,20,21,22,23,24,25,26,27,28,29,30))
			BEGIN
				
					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from view_Analytics_MasterKpiValues WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP) AND ModuleID = @ModuleId AND  YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;

					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsMasterKpiValues @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds,@ModuleId
							SELECT @moduleQuery = '#TempFinancialFx';
					END
					ELSE
						BEGIN
							SELECT @moduleQuery = 'view_Analytics_MasterKpiValues';			
						END
			END
		ELSE IF(@ModuleId = 3)
			BEGIN
				
					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from view_GetAnalyticsOperationalKPIValues WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP) AND   YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;
					
					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsOperationalKpiValues @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
							SELECT @moduleQuery = '#TempFinancialFx';
					END
					ELSE
						BEGIN
							SELECT @moduleQuery = 'view_GetAnalyticsOperationalKPIValues';	
						END
			END
		ELSE IF(@ModuleId = 4)
			BEGIN
				
					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from View_GetAnalyticsInvestment WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP)  AND  YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;
					
					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsInvestmentKpiValues @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
							SELECT @moduleQuery = '#TempFinancialFx';
						END
					ELSE
						BEGIN
							SELECT @moduleQuery = 'View_GetAnalyticsInvestment';
						END
			END
		ELSE IF(@ModuleId = 5)
			BEGIN

					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from view_Analytics_CompanyKpiValues WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP) AND   YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;

					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsCompanyKpiValues @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
							SELECT @moduleQuery = '#TempFinancialFx';
						END
					ELSE
						BEGIN
							SELECT @moduleQuery = 'view_Analytics_CompanyKpiValues';
						END
			END
		ELSE IF(@ModuleId = 6)
			BEGIN
					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from View_GetAnalyticsImpactKpi WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP) AND   YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;

					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsImpactKpiValues @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
							SELECT @moduleQuery = '#TempFinancialFx';
					END
					ELSE
						BEGIN
							SELECT @moduleQuery = 'View_GetAnalyticsImpactKpi';			
						END
			END
		ELSE IF(@ModuleId = 7)
			BEGIN
				SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
						FROM (SELECT DISTINCT KPI from View_AnalyticsProfitLossValues WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP) AND   YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;

				IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> '' AND @ToCurrencyId > 0)
					BEGIN
						INSERT INTO #TempFinancialFx Exec sp_AnalyticsProfitLossValues @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
						SELECT @moduleQuery = '#TempFinancialFx';
					END
				ELSE
					BEGIN
						SELECT @moduleQuery = 'View_AnalyticsProfitLossValues';					
					END
			END
		ELSE IF(@ModuleId = 8)
			BEGIN
				SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
			    FROM (SELECT DISTINCT KPI from View_AnalyticsBalanceSheetValues WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP) AND   YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;
			
			IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
					BEGIN
						INSERT INTO #TempFinancialFx Exec sp_AnalyticsBalanceSheetValues @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
						SELECT @moduleQuery = '#TempFinancialFx';
					END
				ELSE
					BEGIN
						SELECT @moduleQuery = 'View_AnalyticsBalanceSheetValues';
					END					
			END
		ELSE IF(@ModuleId = 9)
			BEGIN
				SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
				FROM (SELECT DISTINCT KPI from View_AnalyticsCashflowValues WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP) AND   YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;
			
			IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
					BEGIN
						INSERT INTO #TempFinancialFx Exec sp_AnalyticsCashflowValues @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
						SELECT @moduleQuery = '#TempFinancialFx';
					END
				ELSE
					BEGIN
						SELECT @moduleQuery = 'View_AnalyticsCashflowValues';				
					END
			END
		ELSE IF(@ModuleId = 11)
			BEGIN		
					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from View_GetAnalyticsCapTable1 WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP)  AND  YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;
					
					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsCapTable1Values @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
							SELECT @moduleQuery = '#TempFinancialFx';
						END
					ELSE
						BEGIN
							SELECT @moduleQuery = 'View_GetAnalyticsCapTable1';
						END
		END	
		ELSE IF (@ModuleId in (31,32,33,34,35,36,37,38,39,40,41,42,43,44,45))
			BEGIN		
			Declare @KpiType Varchar(5000)=NULL;
						SET @KpiType=(SELECT Top 1 AliasName FROM M_SubPageFields where name in(SELECT PageConfigFieldName FROM M_KpiModules WHERE ModuleID=@ModuleId));
					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from (select *, @KpiType as 'KpiType' from [View_GetAnalyticsCapTable] where ModuleId = @ModuleId) a WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP)  AND  YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;
					
					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsCapTableValues @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds,@ModuleId
							SELECT @moduleQuery = '#TempFinancialFx';
						END
					ELSE
						BEGIN
						
							SELECT @moduleQuery = '(								
								SELECT *, ''' + @KpiType + ''' AS KpiType
								FROM [View_GetAnalyticsCapTable] 
								WHERE ModuleId = '+@ModuleId+
							') p';
						END
		END	
		ELSE IF(@ModuleId = 12)
			BEGIN			
					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from View_GetAnalyticsCapTable2 WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP)  AND  YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;
					
					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsCapTable2Values @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
							SELECT @moduleQuery = '#TempFinancialFx';
						END
					ELSE
						BEGIN
							SELECT @moduleQuery = 'View_GetAnalyticsCapTable2';
						END
		END	
		ELSE IF(@ModuleId = 13)
			BEGIN	
					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from View_GetAnalyticsCapTable3 WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP)  AND  YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;
					
					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsCapTable3Values @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
							SELECT @moduleQuery = '#TempFinancialFx';
						END
					ELSE
						BEGIN
							SELECT @moduleQuery = 'View_GetAnalyticsCapTable3';
						END
		END	
		ELSE IF(@ModuleId = 14)
			BEGIN		
					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from View_GetAnalyticsCapTable4 WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP)  AND  YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;
					
					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsCapTable4Values @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
							SELECT @moduleQuery = '#TempFinancialFx';
						END
					ELSE
						BEGIN
							SELECT @moduleQuery = 'View_GetAnalyticsCapTable4';
						END
		END	
				ELSE IF(@ModuleId = 15)
			BEGIN		
					SELECT @finalColumns = STRING_AGG(CONVERT(NVARCHAR(MAX),QUOTENAME(K.Kpi)), ', ')
					FROM (SELECT DISTINCT KPI from View_GetAnalyticsCapTable5 WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP)  AND  YEAR BETWEEN @FromYear AND @ToYear AND KpiId IN (SELECT ITEM FROM #TempKpi)) as K;
					
					IF(@ToCurrencyId IS NOT NULL AND @ToCurrencyId <> ''  AND @ToCurrencyId > 0)
						BEGIN
							INSERT INTO #TempFinancialFx Exec sp_AnalyticsCapTable5Values @CurrencyRateType,@ToCurrencyId,0,@FromYear,@ToYear,@CompanyIds,@KpiIds
							SELECT @moduleQuery = '#TempFinancialFx';
						END
					ELSE
						BEGIN
							SELECT @moduleQuery = 'View_GetAnalyticsCapTable5';
						END
		END	
		IF @columnsDealStaticWithAliases IS NULL
				BEGIN
					SET @columnsDealStaticWithAliases = (SELECT '[PortfolioCompanyId] AS DealCompanyId,[FundId] AS DealFundId,[DealId] As DealId')
				END
		ELSE
				BEGIN
					SET @columnsDealStaticWithAliases = @columnsDealStaticWithAliases +',' + (SELECT '[PortfolioCompanyId] AS DealCompanyId,[FundId] AS DealFundId,[DealId] As DealId')
				END
		IF((@ModuleId = 1 OR @ModuleId = 2) AND (@ToCurrencyId IS NULL OR @ToCurrencyId = 0))
			BEGIN
				SELECT @moduleIdQuery = 'AND ModuleID = '+@ModuleId+' AND'
			END
		ELSE
			BEGIN
				SELECT @moduleIdQuery = 'AND'
		   END
		   IF EXISTS(SELECT * FROM #TempFinancialFx)
		   BEGIN
		   --Begin Fx CalCulation
		    DECLARE @currencyPeriodValuesTable TABLE (FromCurrencyId Int,ToCurrencyId Int,FinancialYearEnd INT,MethodologyId INT,Month INT NULL,Year INT NOT NULL,Quarter VARCHAR(50),PeriodType VARCHAR(100),FxRate decimal(35,7));
		   
				 DECLARE @ProcessTable TABLE (FinancialYearEnd INT,MethodologyId Varchar(Max),FromCurrencyId INT);
				  INSERT INTO @ProcessTable(FinancialYearEnd,MethodologyId,FromCurrencyId)
				   SELECT  FinancialYearEnd,Cast(STRING_AGG(MethodologyId, ',')  AS VARCHAR(MAX)) AS MethodologyId,FromCurrencyId FROM (
					SELECT DISTINCT  FinancialYearEnd,MethodologyId,FromCurrencyId  FROM  #TempFinancialFx
					) AS DistinctMethods GROUP BY FinancialYearEnd,FromCurrencyId;
							    
			DECLARE @FinancialYearEnd INT;
			DECLARE @FromCurrencyId INT;
			DECLARE @MethodologyId VARCHAR(MAX);
			
			DECLARE financial_data_cursor CURSOR FOR
			SELECT  FinancialYearEnd, MethodologyId, FromCurrencyId FROM @ProcessTable;
			
			OPEN financial_data_cursor;
			
			FETCH NEXT FROM financial_data_cursor INTO  @FinancialYearEnd, @MethodologyId, @FromCurrencyId;
			
			WHILE @@FETCH_STATUS = 0
			BEGIN
			    Declare @PeriodJson Varchar(Max)=NULL;
			    SET @PeriodJson = (SELECT DISTINCT Quarter, Year,  Month, HeaderValue PeriodType  FROM   #TempFinancialFx  WHERE FinancialYearEnd = @FinancialYearEnd FOR JSON PATH, INCLUDE_NULL_VALUES);
				IF( @ToCurrencyId > 0 AND @ToCurrencyId <> @FromCurrencyId )
				BEGIN
					INSERT INTO  @currencyPeriodValuesTable (MethodologyId,Month,Year,Quarter,PeriodType,FxRate)
					EXEC [CalculateFxRates] @MethodologyId,@FinancialYearEnd,@CurrencyRateType,@FromCurrencyId,@ToCurrencyId,@PeriodJson
					Update @currencyPeriodValuesTable SET FinancialYearEnd=@FinancialYearEnd,ToCurrencyId=@ToCurrencyId,FromCurrencyId=@FromCurrencyId WHERE FinancialYearEnd IS NULL;
				END
				FETCH NEXT FROM financial_data_cursor INTO  @FinancialYearEnd, @MethodologyId, @FromCurrencyId;
			END;
			
			CLOSE financial_data_cursor;
			DEALLOCATE financial_data_cursor;
			
			UPDATE I SET I.KpiValue = (I.KpiValue*M.FxRate) FROM #TempFinancialFx I INNER JOIN @currencyPeriodValuesTable M ON 
	        ISNULL(I.Quarter, - 1) = ISNULL(M.Quarter, - 1)
			AND ISNULL(I.Year, - 1) = ISNULL(M.Year, - 1) AND ISNULL(I.Month, - 1) = ISNULL(M.Month, - 1) 
			AND I.MethodologyId = M.MethodologyId AND I.HeaderValue=M.PeriodType
			AND I.FinancialYearEnd=M.FinancialYearEnd
			AND I.FromCurrencyId=M.FromCurrencyId
			WHERE I.KpiValue IS NOT NULL AND I.KpiInfo='$'

			--END Fx CalCulation
		END
		IF(@finalColumns IS NOT NULL)
			BEGIN
					IF @columnsCustom IS NULL
						BEGIN
							SET @columnsCustom = (SELECT '[NULL]')
						END
						SET @pivotQuery = N'WITH PivotData AS (
						   SELECT * from '+@moduleQuery+' WHERE PortfolioCompanyId IN (SELECT ITEM FROM #TEMPCOMP) '+@moduleIdQuery+'   YEAR BETWEEN '+@FromYear+' AND '+@ToYear+' AND KpiId IN (SELECT ITEM FROM #TempKpi)	
						)
						select *  from (SELECT Period, case When(KpiInfo = ''%'' and ISNUMERIC(KpiValue)=1) then CAST(TRY_CONVERT(DECIMAL(18, 7), KpiValue)/100 as VARCHAR) Else KpiValue End as ''KpiValue'', KpiInfo, PortfolioCompanyId,PeriodDate,CASE 
            WHEN ValueType = ''Ic'' THEN ''IC'' 
            ELSE ValueType 
        END AS ValueType,Kpi,PeriodType,KpiType,KpiId
						FROM PivotData  group by Period,KpiValue,KpiInfo, PortfolioCompanyId,PeriodDate,ValueType,Kpi,PeriodType,KpiType,KpiId)as Tr
						LEFT JOIN (SELECT PortfolioCompanyId ''CompanyId'',' + @columnsPcWithAliases + ' FROM view_GetAnalyticsPortfolioCompanyDetails) AS PC ON PC.CompanyId = Tr.PortfolioCompanyId AND PC.CompanyId IN (SELECT ITEM FROM #TEMPCOMP)
						LEFT JOIN (SELECT '+@columnsDealStaticWithAliases+' FROM  view_GetDealConsolidatedDetails) Cons  ON Tr.PortfolioCompanyId = Cons.DealCompanyId
						LEFT JOIN (SELECT FundID,' + @columnsFundWithAliases + ' FROM view_ConsolidatedFundDetails) AS F ON F.FundID = Cons.DealFundId
						LEFT JOIN (SELECT *
												FROM (
												  SELECT FieldValue,PageFeatureId, F.AliasName ,F.SubPageID
												  FROM PageConfigurationFieldValue V
												  INNER JOIN M_SubPageFields F ON V.FieldID = F.FieldID
												  WHERE FieldValue IS NOT NULL AND FieldValue!=''NA''  AND V.IsDeleted = 0 AND V.IsActive = 1 AND F.IsActive = 1 
													AND V.FieldID IN (
													  SELECT FieldID
													  FROM M_SubPageFields
													  WHERE SubPageID IN (1,5,7,8,10,11,12) AND FieldID IN (SELECT Item FROM #TEMPFields) AND isActive = 1 AND isDeleted = 0 AND IsCustom = 1 and AliasName!=''Custom %''
													)
												  GROUP BY FieldValue, PageFeatureId, F.AliasName,F.SubPageID
												) AS A
												PIVOT (
												  MAX(FieldValue) FOR AliasName IN (' + @columnsCustom + ')
												) AS PivotTable) AS STA ON ((Cons.DealId = STA.PageFeatureId  AND  STA.SubPageID = 5) or  (PC.CompanyId= STA.PageFeatureId AND  STA.SubPageID in (1,10,11)) or   (Cons.DealFundId = STA.PageFeatureId AND  STA.SubPageID IN (7,8,12)))
						PIVOT (
							MAX(KpiValue) FOR Kpi IN ('+@finalColumns+')
						) AS PivotData order by ValueType, PeriodDate asc;';
						print @pivotQuery
					EXEC sp_executesql @pivotQuery;
					DROP TABLE IF EXISTS #TEMPCOMP
					DROP TABLE IF EXISTS #TempKpi
					DROP TABLE IF EXISTS #TEMPFields
					DROP TABLE IF EXISTS #TEMPSubPageIds
					DROP TABLE IF EXISTS #TempFinancialFx					
			END
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund Financials1')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (109, N'Fund Financials1', 13, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'FundFinancials1')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund Financials2')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (110, N'Fund Financials2', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundFinancials2')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund Financials3')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (111, N'Fund Financials3', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundFinancials3')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund Financials4')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (112, N'Fund Financials4', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundFinancials4')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund Financials5')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (113, N'Fund Financials5', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundFinancials5')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund Financials6')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (114, N'Fund Financials6', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundFinancials6')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund Financials7')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (115, N'Fund Financials7', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundFinancials7')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund Financials8')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (116, N'Fund Financials8', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundFinancials8')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund KPI1')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (117, N'Fund KPI1', 13, NULL, 1, 0, getdate(), 3, NULL,NULL, NULL, 0, 0, N'FundKPI1')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund KPI2')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (118, N'Fund KPI2', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundKPI2')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund KPI3')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (119, N'Fund KPI3', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundKPI3')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund KPI4')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (120, N'Fund KPI4', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundKPI4')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund KPI5')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (121, N'Fund KPI5', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundKPI5')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund KPI6')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (122, N'Fund KPI6', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundKPI6')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund KPI7')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (123, N'Fund KPI7', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundKPI7')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Fund KPI8')
BEGIN
    INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
    [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
    VALUES (124, N'Fund KPI8', 13, NULL, 1, 0, getdate(), 3, NULL, NULL, NULL, 0, 0, N'FundKPI8')
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials1') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials1'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials1') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials1'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials1') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials1'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials1') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials1'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials2') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials2'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials2') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials2'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials2') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials2'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials2') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials2'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials3') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials3'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials3') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials3'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials3') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials3'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials3') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials3'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials4') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials4'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials4') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials4'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials4') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials4'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials4') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials4'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials5') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials5'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials5') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials5'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials5') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials5'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials5') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials5'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials6') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials6'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials6') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials6'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials6') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials6'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials6') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials6'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials7') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials7'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials7') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials7'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials7') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials7'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials7') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials7'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials8') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials8'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials8') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials8'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials8') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials8'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials8') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund Financials8'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI1') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI1'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI1') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI1'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI1') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI1'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI1') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI1'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI2') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI2'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI2') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI2'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI2') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI2'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI2') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI2'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI3') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI3'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI3') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI3'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI3') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI3'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI3') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI3'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI4') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI4'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI4') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI4'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI4') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI4'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI4') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI4'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI5') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI5'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI5') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI5'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI5') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI5'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI5') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI5'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI6') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI6'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI6') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI6'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI6') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI6'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI6') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI6'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI7') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI7'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI7') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI7'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI7') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI7'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI7') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI7'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI8') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI8'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI8') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI8'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI8') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI8'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI8') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Fund KPI8'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO
CREATE OR ALTER PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig] ( @FeatureId INT ) AS
BEGIN   IF (@FeatureId = 14)
BEGIN
   DECLARE @Order TABLE (SubFeatureID INT, DisplayOrder INT);
        INSERT INTO @Order (SubFeatureID, DisplayOrder)
        VALUES
            (2, 1), (3, 2), (4, 3), (5, 4), (6, 5), (7, 6), (9, 7), (10, 8), (11, 9), (12, 10),
            (13, 11), (14, 12), (15, 13), (16, 14), (17, 15), (18, 16), (94, 17), (95, 18), (96, 19),
            (97, 20), (98, 21), (99, 22), (100, 23), (101, 24), (102, 25), (103, 26), (104, 27),
            (105, 28), (106, 29), (107, 30), (108, 31), (55, 32), (56, 33), (57, 34), (58, 35),
            (59, 36), (60, 37), (61, 38), (62, 39), (63, 40), (64, 41), (65, 42), (66, 43),
            (67, 44), (68, 45), (69, 46);
 
        SELECT
            f.SubFeatureID,
            f.SubFeature AS SubFeatureName,
            COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName,
            f.isDeleted AS IsDeleted,
            f.ParentFeatureID AS ParentFeatureId
        FROM M_SubFeature f
        LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (1, 4, (SELECT SubPageID FROM M_SubPageDetails WHERE Name='Documents' AND PageID=1)) AND s.isDeleted = 0
        LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (2, 3, 38, 41, 47) AND sf.isDeleted = 0
        LEFT JOIN @Order o ON f.SubFeatureID = o.SubFeatureID
        WHERE (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) AND ParentFeatureID = 14
        ORDER BY o.DisplayOrder
 END
ELSE IF(@FeatureId = 13)
BEGIN
SELECT       f.SubFeatureID,      f.SubFeature As SubFeatureName,
   COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted, ParentFeatureId
   FROM       M_SubFeature f     LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (7,8,9,15,12,(Select SubPageID from M_SubPageDetails WHERE Name='Documents' AND PageID=2)) AND s.isDeleted = 0
   LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN ((Select SubPageID from M_SubPageDetails WHERE Name='Fund Key Performance Indicator' AND PageID=2),(Select SubPageID from M_SubPageDetails WHERE Name='Fund Financials' AND PageID=2)) AND sf.isDeleted = 0
   LEFT JOIN @Order o ON f.SubFeatureID = o.SubFeatureID
   WHERE (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and ParentFeatureID = 13
   ORDER BY f.SubFeatureID
   END
ELSE IF(@FeatureId = 15)
BEGIN
SELECT f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId FROM       M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (5, 6) AND s.isDeleted = 0
WHERE s.SubPageID IS NOT NULL
END
ELSE IF(@FeatureId = 50)
BEGIN
SELECT  f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId     FROM       M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (19) AND s.isDeleted = 0
LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (19) AND sf.isDeleted = 0
WHERE       (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and  ParentFeatureID = 50
END
ELSE IF(@FeatureId = 42)
BEGIN
SELECT f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId FROM   M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (13,14,15,16,17,18) AND s.isDeleted = 0
WHERE s.SubPageID IS NOT NULL  AND s.SubPageID IN (13,14,15,16,17,18) AND ParentFeatureID = 42
 
UNION ALL
 
SELECT    f.SubFeatureID,      f.SubFeature As SubFeatureName,COALESCE(f.SubFeature, f.SubFeature) AS SubFeatureAliasName,f.isDeleted AS IsDeleted,f.ParentFeatureID AS ParentFeatureId     FROM M_SubFeature f
WHERE ParentFeatureID = 42 AND F.isDeleted=0 AND PageConfigName IS NULL
END
ELSE IF(@FeatureId = 19)
BEGIN
SELECT       SubFeatureID,      SubFeature As SubFeatureName, COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,isDeleted AS IsDeleted,
ParentFeatureID AS ParentFeatureId  FROM  M_SubFeature
WHERE  SubFeatureID  BETWEEN 33 AND 48
END
ELSE IF (@FeatureId = 57)
		BEGIN
			 SELECT
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM
					M_SubFeature
				WHERE  SubFeatureID  BETWEEN 71 AND 81
		END
		ELSE IF (@FeatureId = 58)
		BEGIN
			 SELECT
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM
					M_SubFeature
				WHERE  SubFeatureID  BETWEEN 82 AND 92
		END
END
GO
GO
CREATE OR ALTER PROCEDURE [dbo].[ProcGetFundSubFeaturePermissions]
(
@UserId INT,
@FundId INT,
@FeatureId INT
)
AS
BEGIN
	SELECT SubFeature, 
		   PageConfigName,
		   GroupId,
		   FeatureMappingId 'FundId',
		   musf.SubFeatureID 'SubFeatureId',
		   (SELECT TOP 1 ModuleID FROM MFundKpiModules WHERE PageConfigFieldName = PageConfigName) AS ModuleId,
		   CanAdd, 
		   CanEdit, 
		   CanView, 
		   CanExport,
		   CanImport,
		   musf.IsActive
	FROM Mapping_UserSubFeature AS musf 
		INNER JOIN M_SubFeature AS sf 
		ON musf.SubFeatureID = sf.SubFeatureID AND MUSF.EntityId = @FeatureId
	WHERE musf.FeatureMappingId = @FundId 
		AND musf.GroupId IN (SELECT GroupID FROM Mapping_UserGroup WHERE UserID = @UserId AND IsDeleted = 0)
		AND musf.IsDeleted = 0
		AND sf.IsDeleted = 0
		AND sf.IsActive = 1
		AND sf.ParentFeatureID = @FeatureId
		AND musf.EntityId = @FeatureId
END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'FundMasterKpiValues') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.FundMasterKpiValues') AND name = 'Half')
BEGIN
ALTER TABLE FundMasterKpiValues ADD Half VARCHAR(20) NULL;
END
IF (NOT EXISTS (SELECT * 
                FROM M_ValueTypes
                WHERE HeaderValue = 'Since Inception' 
               ))
BEGIN
INSERT INTO M_ValueTypes(HeaderValue,CreatedOn,CreatedBy,IsDeleted)
SELECT 'Since Inception',GETDATE(),3,0
END
GO
ALTER PROCEDURE [dbo].[spBulkUploadFofInvestmentKpis]
	@TableName VARCHAR(100),
	@UserId INT
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @SQLString NVARCHAR(max);
	DECLARE @InvestmentKpi TABLE (
	[Id] [INT],
	[KpiId] [int],
	[Quarter] [nvarchar](10) NULL,
	[Year] [int] NOT NULL,
	[CompanyId] INT NULL,
	[ModuleId] INT NULL,
	[KpiValue] NVARCHAR(MAX) NULL,
	[Kpi] NVARCHAR(MAX) NULL,
	[KpiInfo] NVARCHAR(100) NULL
	)
		--Declaring inserted rows table to get insert records---
		DECLARE @InsertedRows AS TABLE (ID INT);
SET @SQLString = 'SELECT Id,KpiId,Quarter,Year,CompanyId,ModuleId,KpiValue,Kpi,KpiInfo FROM ' + @TableName + '';
	INSERT INTO @InvestmentKpi EXEC sys.Sp_executesql @SQLString;
		BEGIN TRY
			BEGIN TRANSACTION
						INSERT INTO DataAuditLog(AttributeName,AttributeID,FieldName,MonthAndYear,OldValue,NewValue,Comments,CreatedBy,CreatedOn,IsDeleted,PortfolioCompanyID,Description,NewCurrencyType,OldCurrencyType,DynamicQueryId)
						SELECT 'Investment KPIs',V.PCInvestmentKPIQuarterlyValueID,I.Kpi,CONVERT(VARCHAR,I.Quarter + ' ' + CONVERT(VARCHAR, I.Year)),V.KPIActualValue,I.KpiValue,'actual',@UserId,GETDATE(),0,I.CompanyId,'File Upload',I.KpiInfo,I.KpiInfo,0
						FROM @InvestmentKpi I INNER JOIN Mapping_PortfolioInvestmentKPI M ON I.CompanyId = M.PortfolioCompanyID AND I.KpiId = M.KpiID 
						LEFT JOIN PCInvestmentKPIQuarterlyValue V ON M.KpiID = V.InvestmentKPIID AND M.PortfolioCompanyID = V.PortfolioCompanyID AND ISNULL(V.Quarter, - 1) = ISNULL(I.Quarter, - 1)
						AND ISNULL(V.Year, - 1) = ISNULL(I.Year, - 1) 
						WHERE ISNULL(V.KPIActualValue, 0.00000000) <> ISNULL(I.KpiValue, 0.00000000) 
						AND  V.PCInvestmentKPIQuarterlyValueID IS NOT NULL  AND M.PortfolioCompanyID = I.CompanyId AND I.KpiValue IS NOT NULL 

						UPDATE V SET V.KpiActualValue = I.KpiValue FROM @InvestmentKpi I INNER JOIN Mapping_PortfolioInvestmentKPI M ON I.CompanyId = M.PortfolioCompanyID AND I.KpiId = M.KpiID 
						LEFT JOIN PCInvestmentKPIQuarterlyValue V ON M.KpiID = V.InvestmentKPIID AND M.PortfolioCompanyID = V.PortfolioCompanyID AND ISNULL(V.Quarter, - 1) = ISNULL(I.Quarter, - 1)
						AND ISNULL(V.Year, - 1) = ISNULL(I.Year, - 1) 
						WHERE V.PCInvestmentKPIQuarterlyValueID IS NOT NULL  AND M.PortfolioCompanyID = I.CompanyId AND I.KpiValue IS NOT NULL 
							
				INSERT INTO PCInvestmentKPIQuarterlyValue(PortfolioCompanyID, InvestmentKPIID, KPIActualValue, KPIInfo, Year, Quarter, CreatedOn, CreatedBy, IsDeleted,IsActive,ValueTypeID)
				OUTPUT INSERTED.PCInvestmentKPIQuarterlyValueID INTO @InsertedRows
				SELECT I.CompanyId,I.KpiId,I.KpiValue,I.KpiInfo,I.YEAR,I.Quarter,GETDATE(),@UserId,0,1,4 FROM @InvestmentKpi I INNER JOIN Mapping_PortfolioInvestmentKPI M ON I.CompanyId = M.PortfolioCompanyID AND I.KpiId = M.KpiID 
						LEFT JOIN PCInvestmentKPIQuarterlyValue V ON M.KpiID = V.InvestmentKPIID AND M.PortfolioCompanyID = V.PortfolioCompanyID AND ISNULL(V.Quarter, - 1) = ISNULL(I.Quarter, - 1)
						AND ISNULL(V.Year, - 1) = ISNULL(I.Year, - 1) 
						WHERE V.PCInvestmentKPIQuarterlyValueID IS NULL AND M.PortfolioCompanyID = I.CompanyId AND I.KpiValue IS NOT NULL 

						--Insert into Audit Log--
				INSERT INTO DataAuditLog(AttributeName,AttributeID,FieldName,MonthAndYear,OldValue,NewValue,Comments,CreatedBy,CreatedOn,IsDeleted,PortfolioCompanyID,Description,NewCurrencyType,OldCurrencyType,DynamicQueryId)
				SELECT 'Investment KPIs',PIK.PCInvestmentKPIQuarterlyValueID,M.KPI,CONVERT(VARCHAR,PIK.Quarter + ' ' + CONVERT(VARCHAR, PIK.Year)),NULL,PIK.KPIActualValue,'actual',@UserId,GETDATE(),0,PIK.PortfolioCompanyID,'File Upload',M.KpiInfo,M.KpiInfo,0
				FROM PCInvestmentKPIQuarterlyValue AS PIK 
				INNER JOIN M_InvestmentKPI M ON PIK.InvestmentKPIID = M.InvestmentKPIId AND M.IsDeleted = 0
				WHERE PCInvestmentKPIQuarterlyValueID IN (SELECT ID FROM @InsertedRows)
									
					SET NOCOUNT OFF;
			COMMIT TRANSACTION
		END TRY
		BEGIN CATCH
			ROLLBACK TRANSACTION
			DECLARE @Message varchar(MAX) = ERROR_MESSAGE(),
			@Number int = 6000,
			@State smallint = ERROR_STATE(); 
			SELECT @Number,@Message, @State;
	END CATCH
END
GO
ALTER PROCEDURE [dbo].[spBulkUploadFofTradingRecords]
	@TableName VARCHAR(1000),
	@UserId INT
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @SQLString NVARCHAR(max);
	DECLARE @MasterKpi TABLE (
	[Id] [INT],
	[KpiId] [int],
	[Quarter] [nvarchar](10) NULL,
	[Year] [int] NOT NULL,
	[CompanyId] INT NULL,
	[ModuleId] INT NULL,
	[KpiValue] NVARCHAR(MAX) NULL,
	[Kpi] NVARCHAR(MAX) NULL,
	[KpiInfo] NVARCHAR(100) NULL
	)
	--Declaring inserted rows table to get insert records---
	DECLARE @InsertedRows AS TABLE (ID INT);
	SET @SQLString = 'SELECT Id,KpiId,Quarter,Year,CompanyId,ModuleId,KpiValue,Kpi,KpiInfo FROM ' + @TableName + '';
	INSERT INTO @MasterKpi EXEC sys.Sp_executesql @SQLString;
	BEGIN TRY
	BEGIN TRANSACTION

	--Audit Log Entry for Update--
	INSERT INTO MasterKpiAuditLog (ModuleId,PortfolioCompanyId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy)
	SELECT KPI.ModuleID,KPI.CompanyId,V.PCMasterKpiValueID,V.KPIValue,KPI.KPIValue,V.KPIInfo,M.KpiInfo,'File Upload',0,GETDATE(),@UserID 
	FROM @MasterKpi KPI
	INNER JOIN M_MasterKpis M on M.MasterKpiID = KPI.KpiId
	INNER JOIN Mapping_Kpis Map on Map.KpiID = M.MasterKpiID and Map.ModuleID = KPI.ModuleID AND KPI.CompanyId = MAP.PortfolioCompanyID
	INNER JOIN PCMasterKpiValues V ON V.PortfolioCompanyID = Map.PortfolioCompanyID AND V.MappingKpisID = Map.Mapping_KpisID
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND MONTH IS NULL and V.Quarter IS NOT NULL
		AND V.ValueTypeID = 4
	WHERE 
	ISNULL(V.KPIValue, 0.00000000) <> ISNULL(KPI.KPIValue, 0.00000000) AND 
	Map.PortfolioCompanyID = KPI.CompanyId 
	AND V.ModuleID = 1
	AND V.MappingKpisID IS NOT NULL 
	AND KPI.KPIValue IS NOT NULL

	--Update existing data--
	UPDATE V
	SET V.KPIValue = KPI.KPIValue
	FROM @MasterKpi KPI
	INNER JOIN M_MasterKpis M on M.MasterKpiID = KPI.KpiId
	INNER JOIN Mapping_Kpis Map on Map.KpiID = M.MasterKpiID and Map.ModuleID = KPI.ModuleID AND KPI.CompanyId = MAP.PortfolioCompanyID
	INNER JOIN PCMasterKpiValues V ON V.PortfolioCompanyID = Map.PortfolioCompanyID AND V.MappingKpisID = Map.Mapping_KpisID
		AND MONTH IS NULL AND V.Quarter IS NOT NULL
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = 4
	WHERE 
	ISNULL(V.KPIValue, 0.00000000) <> ISNULL(KPI.KPIValue, 0.00000000) AND 
	 Map.PortfolioCompanyID = KPI.CompanyId 
	AND V.ModuleID = KPI.ModuleID
	AND V.MappingKpisID IS NOT NULL 
    AND KPI.KPIValue IS NOT NULL

	--Insert New Data---
	INSERT INTO PCMasterKpiValues(PortfolioCompanyID, MappingKpisID, KPIValue, KPIInfo, Year, Quarter, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, ValueTypeID,[IsNumeric],[IsYTD],[Half],[ModuleID])
	OUTPUT inserted.PCMasterKpiValueID into @InsertedRows
	SELECT KPI.CompanyId, Map.Mapping_KpisID, Kpi.KPIValue, M.KpiInfo,KPI.Year, KPI.Quarter, GETDATE(), @UserID, NULL, NULL, 0, 4, 1, 0, NULL,1
	from @MasterKpi KPI
	INNER JOIN M_MasterKpis M on M.MasterKpiID = KPI.KpiId
	INNER JOIN Mapping_Kpis Map on Map.KpiID = M.MasterKpiID and Map.ModuleID = KPI.ModuleID
	LEFT JOIN PCMasterKpiValues V ON V.PortfolioCompanyID = Map.PortfolioCompanyID AND V.MappingKpisID = Map.Mapping_KpisID
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = 4
	WHERE Map.PortfolioCompanyID = KPI.CompanyId 
	AND V.PCMasterKpiValueID IS NULL
	AND V.MappingKpisID IS NULL 
	AND KPI.KPIValue IS NOT NULL
	
	--Insert audit logs--
	INSERT INTO MasterKpiAuditLog (ModuleId,PortfolioCompanyId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy)
	select V.ModuleID,V.PortfolioCompanyID,V.PCMasterKpiValueID,null,V.KPIValue,mst.KpiInfo,mst.KpiInfo,'File Upload',0,GETDATE(),@UserId from PCMasterKpiValues V
	INNER JOIN Mapping_Kpis mp on mp.Mapping_KpisID=V.MappingKpisID
	INNER JOIN M_MasterKpis mst on mst.MasterKpiID=mp.KpiID and mst.IsDeleted=0
	where V.PCMasterKpiValueID in (select ID from @InsertedRows)
	COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION
		DECLARE @Message varchar(MAX) = ERROR_MESSAGE(),
        @Number int = 6000,
        @State smallint = ERROR_STATE();
		SELECT @Number,@Message, @State;
	END CATCH

END
GO
CREATE OR ALTER  PROCEDURE ProcGetInvestmentCompanyAndCLOReportingRecords
	@ColumnList NVARCHAR(MAX),
	@TableName NVARCHAR(MAX) 
AS
BEGIN
	DECLARE @SQL NVARCHAR(MAX)  
      
    SET @SQL = 'SELECT ' + @ColumnList + ' FROM ' + @TableName  
      
    EXEC sp_executesql @SQL  
END
GO
