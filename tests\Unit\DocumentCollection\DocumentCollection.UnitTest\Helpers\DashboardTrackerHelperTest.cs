using Contract.PortfolioCompany;
using DataAccessLayer.Models.DashboardTracker;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Helpers;
using Contract.Currency;

namespace DocumentCollection.UnitTest.Helpers
{
    public class DashboardTrackerHelperTest
    {
        [Fact]
        public void GenerateColumnsDtoArray_ReturnsEmpty_WhenNoConfigs()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>();

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void GenerateColumnsDtoArray_ReturnsNonTimeSeriesColumn_WhenFieldTypeIsNot2()
        {
            // Arrange
            var configs = new List<DashboardTrackerConfig>
            {
                new DashboardTrackerConfig
                {
                    ID = 1,
                    FieldType = 1,
                    DataType = 1,
                    Name = "Test Column",
                    IsActive = true,
                    IsDeleted = false
                }
            };

            // Act
            var result = DashboardTrackerHelper.GenerateColumnsDtoArray(configs);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(1, result[0].ID);
            Assert.Equal("Test Column", result[0].Name);
            Assert.False(result[0].IsTimeSeries);
            Assert.Equal(string.Empty, result[0].TimeSeriesID);
        }

        [Fact]
        public void GenerateTimeSeriesColumns_ReturnsEmpty_WhenRequiredFieldsAreMissing()
        {
            // Arrange
            var config = new DashboardTrackerConfig
            {
                ID = 1,
                FieldType = 2,
                StartPeriod = null, // Missing required field
                EndPeriod = "2024",
                FrequencyType = 1
            };

            // Act
            var result = DashboardTrackerHelper.GenerateTimeSeriesColumns(config);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void GeneratePeriodTexts_Monthly_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "01/2023";
            string endPeriod = "01/2023";
            int frequencyType = 1; // Monthly
            string format = "MMM yyyy";

            // Act
            var result = DashboardTrackerHelper.GeneratePeriodTexts(startPeriod, endPeriod, frequencyType, format);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Contains("Jan 2023", result);
        }

        [Fact]
        public void GeneratePeriodTexts_Quarterly_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "Q1 2023";
            string endPeriod = "Q2 2023";
            int frequencyType = 2; // Quarterly
            string format = "Q yyyy";

            // Act
            var result = DashboardTrackerHelper.GeneratePeriodTexts(startPeriod, endPeriod, frequencyType, format);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Contains("Q12023", result);
            Assert.Contains("Q22023", result);
        }

        [Fact]
        public void GeneratePeriodTexts_Yearly_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "2023";
            string endPeriod = "2025";
            int frequencyType = 3; // Yearly
            string format = "yyyy";

            // Act
            var result = DashboardTrackerHelper.GeneratePeriodTexts(startPeriod, endPeriod, frequencyType, format);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count);
            Assert.Contains("2023", result);
            Assert.Contains("2024", result);
            Assert.Contains("2025", result);
        }

        [Fact]
        public void GenerateMonthlyPeriods_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "01/2023";
            string endPeriod = "01/2023";
            string format = "MMM yyyy";

            // Act
            var result = DashboardTrackerHelper.GenerateMonthlyPeriods(startPeriod, endPeriod, format);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public void GenerateQuarterlyPeriods_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "Q1 2023";
            string endPeriod = "Q4 2023";
            string format = "Q yyyy";

            // Act
            var result = DashboardTrackerHelper.GenerateQuarterlyPeriods(startPeriod, endPeriod, format);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(4, result.Count);
        }

        [Fact]
        public void GenerateYearlyPeriods_ReturnsCorrectPeriods()
        {
            // Arrange
            string startPeriod = "2023";
            string endPeriod = "2025";
            string format = "yyyy";

            // Act
            var result = DashboardTrackerHelper.GenerateYearlyPeriods(startPeriod, endPeriod, format);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count);
            Assert.Contains("2023", result);
            Assert.Contains("2024", result);
            Assert.Contains("2025", result);
        }

        [Fact]
        public void TryParseQuarter_ReturnsTrue_ForValidQ1Format()
        {
            // Arrange
            string quarterString = "Q1 2023";

            // Act
            bool result = DashboardTrackerHelper.TryParseQuarter(quarterString, out int year, out int quarter);

            // Assert
            Assert.True(result);
            Assert.Equal(2023, year);
            Assert.Equal(1, quarter);
        }

        [Fact]
        public void TryParseQuarter_ReturnsTrue_ForValidYearFirstFormat()
        {
            // Arrange
            string quarterString = "2023 Q2";

            // Act
            bool result = DashboardTrackerHelper.TryParseQuarter(quarterString, out int year, out int quarter);

            // Assert
            Assert.True(result);
            Assert.Equal(2023, year);
            Assert.Equal(2, quarter);
        }

        [Fact]
        public void TryParseQuarter_ReturnsFalse_ForInvalidFormat()
        {
            // Arrange
            string quarterString = "Invalid Quarter";

            // Act
            bool result = DashboardTrackerHelper.TryParseQuarter(quarterString, out int year, out int quarter);

            // Assert
            Assert.False(result);
            Assert.Equal(0, year);
            Assert.Equal(0, quarter);
        }

        [Fact]
        public void TryParseQuarter_ReturnsFalse_ForEmptyString()
        {
            // Arrange
            string quarterString = "";

            // Act
            bool result = DashboardTrackerHelper.TryParseQuarter(quarterString, out int year, out int quarter);

            // Assert
            Assert.False(result);
            Assert.Equal(0, year);
            Assert.Equal(0, quarter);
        }

        [Fact]
        public void TryParseQuarter_ReturnsFalse_ForInvalidQuarterNumber()
        {
            // Arrange
            string quarterString = "Q5 2023"; // Invalid quarter number

            // Act
            bool result = DashboardTrackerHelper.TryParseQuarter(quarterString, out int year, out int quarter);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void GenerateDashboardTableDataRows_ReturnsRows_WithStaticAndDynamicColumns()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel
                {
                    FundName = "Fund1",
                    CompanyName = "Company1",
                    PortfolioCompanyID = 1,
                    Website = "http://site.com",
                    ReportingCurrencyDetail = new CurrencyModel { Currency = "USD" },
                    MasterCompanyName = "Master1",
                    FinancialYearEnd = "2024",
                    CompanyLegalName = "Legal1"
                }
            };
            var imagedata = new Dictionary<int, string> { { 1, "logo1.png" } };
            var columnsDto = new List<ColumnsDto>
            {
                new ColumnsDto { ID = 1, Name = "DropdownCol", DataType = 4, FieldType = 1 },
                new ColumnsDto { ID = 2, Name = "Website", DataType = 1, FieldType = 1, MapTo = MapWith.Website },
                new ColumnsDto { ID = 3, Name = "Currency", DataType = 1, FieldType = 1, MapTo = MapWith.Currency },
                new ColumnsDto { ID = 4, Name = "MasterCompanyName", DataType = 1, FieldType = 1, MapTo = MapWith.MasterCompanyName },
                new ColumnsDto { ID = 5, Name = "FinancialYearEnd", DataType = 1, FieldType = 1, MapTo = MapWith.FinancialYearEnd },
                new ColumnsDto { ID = 6, Name = "CompanyLegalName", DataType = 1, FieldType = 1, MapTo = MapWith.CompanyLegalName },
                new ColumnsDto { ID = 7, Name = "EmptyCol", DataType = 1, FieldType = 1 }
            };
            var dropdownValuesDict = new Dictionary<int, List<string>> { { 1, new List<string> { "A", "B" } } };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, dropdownValuesDict);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal("Fund1", row["Fund Name"]);
            Assert.Equal("Company1", row["Portfolio Company Name"]);
            Assert.Equal("logo1.png", row["CompanyLogo"]);
            Assert.Equal("A,B", row["DropdownCol"]);
            Assert.Equal("http://site.com", row["Website"]);
            Assert.Equal("USD", row["Currency"]);
            Assert.Equal("Master1", row["MasterCompanyName"]);
            Assert.Equal("2024", row["FinancialYearEnd"]);
            Assert.Equal("Legal1", row["CompanyLegalName"]);
            Assert.Equal(string.Empty, row["EmptyCol"]);
        }

        [Fact]
        public void GenerateDashboardTableDataRows_HandlesMissingImage_AndEmptyDropdown()
        {
            // Arrange
            var companies = new List<PortfolioCompanyQueryModel>
            {
                new PortfolioCompanyQueryModel
                {
                    FundName = "Fund2",
                    CompanyName = "Company2",
                    PortfolioCompanyID = 2,
                    Website = null,
                    ReportingCurrencyDetail = new CurrencyModel { Currency = null },
                    MasterCompanyName = null,
                    FinancialYearEnd = null,
                    CompanyLegalName = null
                }
            };
            var imagedata = new Dictionary<int, string>();
            var columnsDto = new List<ColumnsDto>
            {
                new ColumnsDto { ID = 1, Name = "DropdownCol", DataType = 4, FieldType = 1 },
                new ColumnsDto { ID = 2, Name = "Website", DataType = 1, FieldType = 1, MapTo = MapWith.Website },
                new ColumnsDto { ID = 3, Name = "Currency", DataType = 1, FieldType = 1, MapTo = MapWith.Currency },
                new ColumnsDto { ID = 4, Name = "MasterCompanyName", DataType = 1, FieldType = 1, MapTo = MapWith.MasterCompanyName },
                new ColumnsDto { ID = 5, Name = "FinancialYearEnd", DataType = 1, FieldType = 1, MapTo = MapWith.FinancialYearEnd },
                new ColumnsDto { ID = 6, Name = "CompanyLegalName", DataType = 1, FieldType = 1, MapTo = MapWith.CompanyLegalName },
                new ColumnsDto { ID = 7, Name = "EmptyCol", DataType = 1, FieldType = 1 }
            };
            var dropdownValuesDict = new Dictionary<int, List<string>> { { 1, new List<string>() } };

            // Act
            var result = DashboardTrackerHelper.GenerateDashboardTableDataRows(companies, imagedata, columnsDto, dropdownValuesDict);

            // Assert
            Assert.Single(result);
            var row = result[0];
            Assert.Equal(string.Empty, row["CompanyLogo"]);
            Assert.Null(row["Website"]);
            Assert.Null(row["Currency"]);
            Assert.Null(row["MasterCompanyName"]);
            Assert.Null(row["FinancialYearEnd"]);
            Assert.Null(row["CompanyLegalName"]);
            Assert.Equal(string.Empty, row["EmptyCol"]);
            Assert.Equal(string.Empty, row["DropdownCol"]);
        }

        [Fact]
        public void FillMaptoColumnData_FillsCorrectly_ForEachMapWith()
        {
            // Arrange
            var c = new PortfolioCompanyQueryModel
            {
                Website = "w",
                ReportingCurrencyDetail = new CurrencyModel { Currency = "c" },
                MasterCompanyName = "m",
                FinancialYearEnd = "f",
                CompanyLegalName = "l"
            };
            var dict = new Dictionary<string, object>();

            // Act & Assert
            var col = new ColumnsDto { Name = "Website", MapTo = MapWith.Website };
            typeof(DashboardTrackerHelper).GetMethod("FillMaptoColumnData", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)
                .Invoke(null, new object[] { c, dict, col });
            Assert.Equal("w", dict["Website"]);

            col = new ColumnsDto { Name = "Currency", MapTo = MapWith.Currency };
            typeof(DashboardTrackerHelper).GetMethod("FillMaptoColumnData", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)
                .Invoke(null, new object[] { c, dict, col });
            Assert.Equal("c", dict["Currency"]);

            col = new ColumnsDto { Name = "MasterCompanyName", MapTo = MapWith.MasterCompanyName };
           typeof(DashboardTrackerHelper).GetMethod("FillMaptoColumnData", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)
                .Invoke(null, new object[] { c, dict, col });
            Assert.Equal("m", dict["MasterCompanyName"]);

            col = new ColumnsDto { Name = "FinancialYearEnd", MapTo = MapWith.FinancialYearEnd };
            typeof(DashboardTrackerHelper).GetMethod("FillMaptoColumnData", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)
                .Invoke(null, new object[] { c, dict, col });
            Assert.Equal("f", dict["FinancialYearEnd"]);

            col = new ColumnsDto { Name = "CompanyLegalName", MapTo = MapWith.CompanyLegalName };
            typeof(DashboardTrackerHelper).GetMethod("FillMaptoColumnData", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)
                .Invoke(null, new object[] { c, dict, col });
            Assert.Equal("l", dict["CompanyLegalName"]);
        }

    }
}
