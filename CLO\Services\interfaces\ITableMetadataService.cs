using CLO.Models;

namespace CLO.Services.interfaces
{
    public interface ITableMetadataService
    {
        TableMetadataModel GetTableMetadata(string tableName, bool IsIncludeHiddenColumns = false);
        List<TableMetadataModel> GetTableMetadataWithColumn();
        List<string> GetAllTableNameFromMetadata(bool IsCLO = false);
        List<string> GetAllTableIdentifierFromMetadata(bool IsCLO = false);
    }
}