﻿using CLO.Models;
using CLO.Shared.APIResponse;

public interface IInvestmentCompanyService
{
    Task<List<InvestmentCompanyModel>> GetInvestmentCompanies();
    Task<InvestmentCompanyModel> GetInvestmentCompanyById(int id);
    Task<int> AddCommentaries(CommentaryModel commentaryModel);
    Task<CommentaryModel> GetcommentriesById(int id);
    Task<int> SaveFootnote(TableFootnoteModel footnote);
    Task<string> GetFootnote(TableFootnoteModel footnote);
    Task<int> DeleteInvestmentCompany(int investmentCompanyId, string connectionString, string tableName = null);

    // Add the missing method definitions
    Task<bool> IsInvestmentCompanyExist(InvestmentCompanyModel investmentCompanyModel);
    Task<APIResponse> InsertInvestmentCompanyDetailRepository(InvestmentCompanyModel model);
    Task<APIResponse> UpdateInvestmentCompanyDetailRepository(InvestmentCompanyModel model);
    Task<CLOReportingModel> GetInvestmentCompaniesAndCLODetails(string connectionString);
}