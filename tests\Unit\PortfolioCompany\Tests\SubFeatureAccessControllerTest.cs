﻿using API.Controllers.PortfolioCompany;
using API.Helpers;
using Contract.PortfolioCompany;
using Contract.Utility;
using DapperRepository;
using DapperRepository.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using PortfolioCompany.Interfaces;
using PortfolioCompany.Services;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using Shared;
using System.Security.Claims;

namespace PortfolioCompany.UnitTest.Tests
{
    public class SubFeatureAccessControllerTest
    {
        private readonly Mock<ILogger<SubFeatureAccessController>> _loggerMock;
        private readonly Mock<ISubFeatureAccessService> _subFeatureAccessServiceMock;
        private readonly Mock<IHelperService> _helperServiceMock;
        private readonly Mock<IEncryption> _encryptionMock;
        public readonly Mock<IDapperGenericRepository> _dapper;
        private readonly SubFeatureAccessController _controller;
        public readonly SubFeatureAccessService _service;
        private readonly Mock<ILogger<SubFeatureAccessService>> _loggerServiceMock;
        public readonly SubFeatureAccessController controller;
        public SubFeatureAccessControllerTest()
        {
            _loggerMock = new Mock<ILogger<SubFeatureAccessController>>();
            _subFeatureAccessServiceMock = new Mock<ISubFeatureAccessService>();
            _helperServiceMock = new Mock<IHelperService>();
            _encryptionMock = new Mock<IEncryption>();
            _dapper = new Mock<IDapperGenericRepository>();
            _loggerServiceMock = new Mock<ILogger<SubFeatureAccessService>>();
            _service = new SubFeatureAccessService(_loggerServiceMock.Object, _dapper.Object);
            _controller = new SubFeatureAccessController(
                _loggerMock.Object,
                _subFeatureAccessServiceMock.Object,
                _helperServiceMock.Object,
                _encryptionMock.Object);
            controller = new SubFeatureAccessController(
                _loggerMock.Object,
                _service,
                _helperServiceMock.Object,
                _encryptionMock.Object);
        }

        [Fact]
        public async Task GetSubFeatureAccessPermissions_ShouldReturnOkResult_WhenModuleIdZero()
        {
            // Arrange
            string encryptedCompanyId = "encryptedCompanyId";
            int featureId = 1;
            int moduleId = 0;
            int userId = 3;
            var permissions = new List<SubFeatureAccessPermissionsModel> {
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Kpi1",
                    ModuleId = 2,
                    CanAdd = true,
                    CanExport = true,
                    CanEdit = true,
                    CanImport = true,
                    CanView = true,
                    CompanyId = 2,
                    GroupId = 3,
                    IsActive = true
                },
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Kpi2",
                    ModuleId = 2,
                    CanAdd = true,
                    CanExport = true,
                    CanEdit = true,
                    CanImport = true,
                    CanView = true,
                    CompanyId = 2,
                    GroupId = 3,
                    IsActive = true
                }
            };

            _encryptionMock.Setup(e => e.Decrypt(encryptedCompanyId)).Returns("2");
            _helperServiceMock.Setup(h => h.GetCurrentUserId(_controller.User)).Returns(userId);
            _subFeatureAccessServiceMock.Setup(s => s.GetSubFeatureAccessPermissions(userId, It.IsAny<int>(), featureId, moduleId)).ReturnsAsync(permissions);

            // Act
            var result = await _controller.GetSubFeatureAccessPermissions(encryptedCompanyId, featureId, moduleId) as OkObjectResult;
            Assert.IsType<OkObjectResult>(result);
            Assert.Equal(permissions, result.Value);
        }

        [Fact]
        public async Task GetSubFeatureAccessPermissions_ShouldReturnOkResult()
        {
            // Arrange
            string encryptedCompanyId = "encryptedCompanyId";
            int featureId = 1;
            int moduleId = 2;
            int userId = 3;
            var permissions = new List<SubFeatureAccessPermissionsModel> {
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Kpi1",
                    ModuleId = 2,
                    CanAdd = true,
                    CanExport = true,
                    CanEdit = true,
                    CanImport = true,
                    CanView = true,
                    CompanyId = 2,
                    GroupId = 3,
                    IsActive = true
                },
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Kpi2",
                    ModuleId = 2,
                    CanAdd = true,
                    CanExport = true,
                    CanEdit = true,
                    CanImport = true,
                    CanView = true,
                    CompanyId = 2,
                    GroupId = 3,
                    IsActive = true
                }
            };

            _encryptionMock.Setup(e => e.Decrypt(encryptedCompanyId)).Returns("2");
            _helperServiceMock.Setup(h => h.GetCurrentUserId(_controller.User)).Returns(userId);
            _subFeatureAccessServiceMock.Setup(s => s.GetSubFeatureAccessPermissions(userId, It.IsAny<int>(), featureId, moduleId)).ReturnsAsync(permissions);

            // Act
            var result = await _controller.GetSubFeatureAccessPermissions(encryptedCompanyId, featureId, moduleId) as OkObjectResult;
            Assert.IsType<OkObjectResult>(result);
            Assert.Equal(permissions, result.Value);
        }

        [Fact]
        public async Task GetCommonSubFeatureAccessPermissions_ReturnsOkResult_WithExpectedData()
        {
            string encryptedCompanyId = "encryptedCompanyId";
            int featureId = 42;
            int userId = 1118;
            var permissions = new List<SubFeatureAccessPermissionsModel> {
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Kpi1",
                    InvestorId = 2,
                    CanAdd = true,
                    CanExport = true,
                    CanEdit = true,
                    CanImport = true,
                    CanView = true,
                    CompanyId = 2,
                    GroupId = 3,
                    IsActive = true
                },
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Kpi2",
                    InvestorId = 1,
                    CanAdd = true,
                    CanExport = true,
                    CanEdit = true,
                    CanImport = true,
                    CanView = true,
                    CompanyId = 2,
                    GroupId = 3,
                    IsActive = true
                }
            };
            _dapper.Setup(repo => repo.Query<SubFeatureAccessPermissionsModel>(SqlConstants.ProcGetSubFeaturePermissions, It.IsAny<object>()))
             .ReturnsAsync(permissions);
            _encryptionMock.Setup(e => e.Decrypt(encryptedCompanyId)).Returns("2");
            _helperServiceMock.Setup(h => h.GetCurrentUserId(_controller.User)).Returns(userId);
            var result = await controller.GetCommonSubFeatureAccessPermissions(encryptedCompanyId, featureId) as OkObjectResult;
            Assert.IsType<OkObjectResult>(result);
            Assert.Equal(permissions, result.Value);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithValidInputs_ReturnsOkResultWithTrue()
        {
            // Arrange
            string encryptedFundId = "encryptedFundId";
            int featureId = 3;
            string kpiType = Constants.FundFinancials;
            int userId = 5;
            int fundId = 10;

            _encryptionMock.Setup(e => e.Decrypt(encryptedFundId)).Returns(fundId.ToString());
            _helperServiceMock.Setup(h => h.GetCurrentUserId(_controller.User)).Returns(userId);
            _subFeatureAccessServiceMock.Setup(s => s.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType)).ReturnsAsync(true);

            // Act
            var result = await _controller.GetFundSubFeatureAccessPermissions(encryptedFundId, featureId, kpiType);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.True((bool)okResult.Value);
            _subFeatureAccessServiceMock.Verify(s => s.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType), Times.Once);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithInvalidEncryptedId_ReturnsOkWithFalseResult()
        {
            // Arrange
            string encryptedFundId = "invalidEncryptedId";
            int featureId = 3;
            string kpiType = Constants.FundFinancials;
            int userId = 5;
            
            _encryptionMock.Setup(e => e.Decrypt(encryptedFundId)).Returns("0"); // Invalid fund ID
            _helperServiceMock.Setup(h => h.GetCurrentUserId(_controller.User)).Returns(userId);
            _subFeatureAccessServiceMock.Setup(s => s.GetFundSubFeatureAccessPermissions(userId, 0, kpiType)).ReturnsAsync(false);

            // Act
            var result = await _controller.GetFundSubFeatureAccessPermissions(encryptedFundId, featureId, kpiType);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.False((bool)okResult.Value);
            _subFeatureAccessServiceMock.Verify(s => s.GetFundSubFeatureAccessPermissions(userId, 0, kpiType), Times.Once);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithDifferentKpiType_ReturnsCorrectResult()
        {
            // Arrange
            string encryptedFundId = "encryptedFundId";
            int featureId = 3;
            string kpiType = Constants.FundKpis;
            int userId = 5;
            int fundId = 10;

            _encryptionMock.Setup(e => e.Decrypt(encryptedFundId)).Returns(fundId.ToString());
            _helperServiceMock.Setup(h => h.GetCurrentUserId(_controller.User)).Returns(userId);
            _subFeatureAccessServiceMock.Setup(s => s.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType)).ReturnsAsync(true);

            // Act
            var result = await _controller.GetFundSubFeatureAccessPermissions(encryptedFundId, featureId, kpiType);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.True((bool)okResult.Value);
            _subFeatureAccessServiceMock.Verify(s => s.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType), Times.Once);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_LogsInformation()
        {
            // Arrange
            string encryptedFundId = "encryptedFundId";
            int featureId = 3;
            string kpiType = Constants.FundFinancials;
            int userId = 5;
            int fundId = 10;

            _encryptionMock.Setup(e => e.Decrypt(encryptedFundId)).Returns(fundId.ToString());
            _helperServiceMock.Setup(h => h.GetCurrentUserId(_controller.User)).Returns(userId);
            _subFeatureAccessServiceMock.Setup(s => s.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType)).ReturnsAsync(true);

            // Act
            await _controller.GetFundSubFeatureAccessPermissions(encryptedFundId, featureId, kpiType);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<Microsoft.Extensions.Logging.EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString().Contains("Getting sub feature accesses")),
                    It.IsAny<System.Exception>(),
                    It.IsAny<System.Func<It.IsAnyType, System.Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithServiceReturningFalse_ReturnsOkWithFalseResult()
        {
            // Arrange
            string encryptedFundId = "encryptedFundId";
            int featureId = 3;
            string kpiType = Constants.FundFinancials;
            int userId = 5;
            int fundId = 10;

            _encryptionMock.Setup(e => e.Decrypt(encryptedFundId)).Returns(fundId.ToString());
            _helperServiceMock.Setup(h => h.GetCurrentUserId(_controller.User)).Returns(userId);
            _subFeatureAccessServiceMock.Setup(s => s.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType)).ReturnsAsync(false);

            // Act
            var result = await _controller.GetFundSubFeatureAccessPermissions(encryptedFundId, featureId, kpiType);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.False((bool)okResult.Value);
            _subFeatureAccessServiceMock.Verify(s => s.GetFundSubFeatureAccessPermissions(userId, fundId, kpiType), Times.Once);
        }

        [Fact]
        public async Task GetFundSubFeatureAccessPermissions_WithIntegration_ReturnsExpectedResult()
        {
            // Arrange
            string encryptedFundId = "encryptedFundId";
            int featureId = 3;
            string kpiType = Constants.FundFinancials;
            int userId = 5;
            int fundId = 10;
            
            var permissions = new List<SubFeatureAccessPermissionsModel> {
                new SubFeatureAccessPermissionsModel {
                    SubFeature = "Fund Financials",
                    ModuleId = 1001,
                    CanImport = true,
                    IsActive = true
                }
            };

            _encryptionMock.Setup(e => e.Decrypt(encryptedFundId)).Returns(fundId.ToString());
            _helperServiceMock.Setup(h => h.GetCurrentUserId(controller.User)).Returns(userId);
            _dapper.Setup(repo => repo.Query<SubFeatureAccessPermissionsModel>(SqlConstants.ProcGetFundSubFeaturePermissions, 
                It.Is<object>(o => 
                    o.GetType().GetProperty("UserId").GetValue(o).Equals(userId) && 
                    o.GetType().GetProperty("FundId").GetValue(o).Equals(fundId))))
                .ReturnsAsync(permissions);

            // Act
            var result = await controller.GetFundSubFeatureAccessPermissions(encryptedFundId, featureId, kpiType);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.True((bool)okResult.Value);
        }
    }
}
