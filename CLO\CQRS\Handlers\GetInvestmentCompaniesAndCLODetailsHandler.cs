﻿using CLO.CQRS.Queries;
using CLO.Models;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CLO.CQRS.Handlers
{
    public class GetInvestmentCompaniesAndCLODetailsHandler : IRequestHandler<GetInvestmentCompaniesAndCLODetailsQuery, CLOReportingModel>
    {
        private readonly IInvestmentCompanyService _investmentCompanyService;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetInvestmentCompaniesHandler"/> class.
        /// </summary>
        /// <param name="investmentCompanyService">The investment company service.</param>
        public GetInvestmentCompaniesAndCLODetailsHandler(IInvestmentCompanyService investmentCompanyService)
        {
            _investmentCompanyService = investmentCompanyService;
        }

        public async Task<CLOReportingModel> Handle(GetInvestmentCompaniesAndCLODetailsQuery request, CancellationToken cancellationToken)
        {
            return await _investmentCompanyService.GetInvestmentCompaniesAndCLODetails(request.ConnectionString);
        }
    }
}
